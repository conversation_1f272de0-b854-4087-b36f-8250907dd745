@use "sass:map";
@use "sass:math";
@use '@angular/material' as mat;

$mat-grey: mat.$m2-grey-palette;

$mat-grey: mat.$m2-grey-palette;
$mat-blue  : mat.$m2-blue-palette;
$mat-yellow: mat.$m2-yellow-palette;

@function map_get($map, $key) {
    @return map.get($map, $key);
}
$dark-focused: rgba(black, .12);

$primary-palette: mat.m2-define-palette(mat.$m2-indigo-palette);
$accent-palette : mat.m2-define-palette(mat.$m2-blue-palette, A200, A100, A400);
$warn-palette   : mat.m2-define-palette(mat.$m2-red-palette);

$primary: mat.m2-get-color-from-palette($primary-palette, 500);
$accent : mat.m2-get-color-from-palette($accent-palette , 500);
$warn   : mat.m2-get-color-from-palette($warn-palette   , 500);

$brand-danger: #f44336;
$brand-success: #1270B6;

$hover: #e6e6e9;
$white: #fff;
$blue: #1270B6;
$dark-blue: #005FAA;
$grey: #c1c1c1;
$dark-grey: #484848;
$hard-dark-grey: #333132;
$light-grey: #f5f5f5;
$light-medium-grey: #a7a9Ad;
$orange: #f89406;
$green: #388e3c;
$hover: #ECF1F7;
$red-draft: #dd4b39;

// kolory statusów spraw
$status-new-color: #F4E4F5;
$status-open-color: #E3F1FD;
$status-sent-color: #C3E59E;
$status-closed-color: #EBEBEB;
$status-unaccepted-color: #FFF3E0;

//kolory statusów odpowiedzi do ankiet
$default-color: #e3e3e3;
$default-text-color: #111111;
$new-answers-color: #E4F3E5;
$new-answers-text-color: #32A000;

// obrys statusów spraw
$border-unaccepted-color: #EC9236;

// kolory przebuduwa interfejsu

$fiveways-background-left: #f5f5f7;
$fiveways-gray: #3A3A3A;
$fiveways-stroke-2: #DBDFF7;
$fiveways-button-text: #1A9267;
$fiveways-gray-80: #7d7d7d;
$fiveways-btn-active: #3392ff;
$fiveways-btn-hover: #eff6f2;
$fiveways-btn-color: #007c50;
$fiveways-btn-tag: #5cb990;
$fiveways-bgc-menu: #e6e6e9;
$fiveways-avatar-border: #c9dfff;
$fiveways-light-green-success: #16CF81;
$fiveways-row-selected: #e3f2fd;
$fiveways-mb24: #24b47e;
$fiveways-assistant: #f39c12;
$fiveways-table-hover: #EFF0F7;
$fiveways-row-selected: #e3f2fd;

$fiveways-bg-comments: #ECFAF3;
$fiveways-color-comments: #000000de;
$fiveways-separator-color: rgb(218, 220, 224);
$fiveways-separator-shadow: rgb(95, 99, 104);

$tags-text-color: #000000de;
$tags-border-color: rgba(0, 0, 0, 0.12);
$tags-icon-color: rgba(0, 0, 0, 0.87);
$tags-count-color: #000000;

$client-header-background: #f9f9f9;
$client-icon-background: rgba(0, 0, 0, 0.03);
$client-icon-hover-background: rgba(0, 0, 0, 0.08);
$client-header-shadow: rgba(0, 0, 0, 0.15);
$client-header-gradient: rgba(0, 0, 0, 0.05);
$client-header-border: rgba(0, 0, 0, 0.05);
$client-label-color: #7D7D7D;
$client-value-color: #3A3A3A;

$number-4: 4px;
$number-8: 8px;
$number-16: 16px;
$number-24: 24px;
$number-32: 32px;
$number-40: 40px;
$number-48: 48px;
$number-54: 54px;
$number-64: 64px;

$mat-light-theme-background: (
        status-bar: map_get($mat-grey, 100),
        app-bar:    map_get($mat-grey, 100),
        background: map_get($mat-grey, 50),
        hover:      $hover,
        card:       white,
        dialog:     white,
        disabled-button: rgba(black, 0.12),
        raised-button: white,
        focused-button: $dark-focused,
        selected-button: map_get($mat-grey, 300),
        selected-disabled-button: map_get($mat-grey, 400),
        disabled-button-toggle: map_get($mat-grey, 200),
        unselected-chip: map_get($mat-grey, 300),
        disabled-list-option: map_get($mat-grey, 200),
);

$issue-sentence-paragraph-margin: 10px;

$container-border-radius: 14px;

$dark-theme-background: #424242;
$dark-theme-foreground: #ffffff;

$livechat-wfirma-input-height: 240px;
$red-border: #ff0000;

$mat-form-field-border-radius: 18px;

$fiveways-gray: #3A3A3A;
$fiveways-stroke: #EBEDF9;
$fiveways-stroke-2: #DBDFF7;
$fiveways-button-text: #1A9267;
$fiveways-button-bg: #1A9267;
$fiveways-button-primary-bg: $fiveways-button-text;
$fiveways-button-border: #5CB990;
$fiveways-button-primary-hover-bg: #06663B;
$fiveways-button-primary-hover-border: #007C50;
$fiveways-button-secondary-hover-bg: #EFF6F2;
$fiveways-white: #FFF;
$fiveways-background: #E9E9F1;
$fiveways-shadow: #E6E6E9;
$fiveways-header-bg: #F5F5F7;
$fiveways-warning: #FF6F6B;
$fiveways-background: #E9E9F1;
$fiveways-hover: #EFF0F7;
$fiveways-light-gray-background: #FBFBFB;

.d-flex {
    display: flex;
}

.row {
    flex-direction: row;
}

.col {
    flex-direction: column;
}

.wrap {
    flex-wrap: wrap;
}

.d-grid {
    display: grid;
}

@for $i from 0 through 40 {
    .mt-#{$i} {
        margin-top: #{$i + 'px'};
    }
    .mr-#{$i} {
        margin-right: #{$i + 'px'};
    }
    .mb-#{$i} {
        margin-bottom: #{$i + 'px'};
    }
    .ml-#{$i} {
        margin-left: #{$i + 'px'};
    }
    .m-#{$i} {
        margin: #{$i + 'px'};
    }

    .pt-#{$i} {
        padding-top: #{$i + 'px'};
    }
    .pr-#{$i} {
        padding-right: #{$i + 'px'};
    }
    .pb-#{$i} {
        padding-bottom: #{$i + 'px'};
    }
    .pl-#{$i} {
        padding-left: #{$i + 'px'};
    }
    .p-#{$i} {
        padding: #{$i + 'px'};
    }
}

@for $i from 0 through 200 {
    .w-#{$i} {
        width: #{$i} + '%';
    }
    .h-#{$i} {
        height: #{$i} + '%';
    }
}

@for $i from 0 through 1000 {
    .w-#{$i}-px {
        width: #{$i} + 'px';
    }
    .h-#{$i}-px {
        height: #{$i} + 'px';
    }
}

@for $i from 0 through 100 {
    .h-calc-#{$i} {
        $offset : #{$i} + 'px';
        height: calc(100% - #{$offset});
    }
}
@for $i from 0 through 40 {
    .icon-#{$i} {
        height: #{$i} + 'px';
        width: #{$i} + 'px';
        min-width: #{$i} + 'px';
        min-height: #{$i} + 'px';
        max-width: #{$i} + 'px';
        max-height: #{$i} + 'px';
        font-size: #{$i} + 'px';
        margin:0;
        padding:0;
    }
}

@for $i from 0 through 12 {
    .grid-cols-#{$i} {
        grid-template-columns: repeat(#{$i}, 1fr);
    }
    .grid-rows-#{$i} {
        grid-template-rows: repeat(#{$i}, 1fr);
    }
    .grid-row-gap-#{$i} {
        grid-row-gap: #{$i} + 'px';
    }
}

@for $i from 0 through 10 {
    $opacity: math.div($i, 10);

    .opacity-#{$i} {
        opacity: #{$opacity};
    }

    .hover\:opacity-#{$i} {
        &:hover {
            opacity: #{$opacity};
        }
    }
}

@each $property in background-color, opacity {
    .hover\:#{$property} {
        transition: #{$property} 0.3s ease;
    }
    .transition-#{$property} {
        transition: #{$property} 0.3s ease;
    }
}

$content: (
        start: start,
        end: end,
        center: center,
        around: space-around,
        between: space-between,
        even: space-evenly,
        stretch: stretch
);

$align: (
        start: start,
        center: center,
        end: end
);

@each $i, $iVal in $content {
    .justify-#{$i} {
        justify-content: $iVal;
    }
}

@each $i, $iVal in $align {
    .items-#{$i} {
        align-items: $iVal;
    }
    .m-items-#{$i} {
        @media (max-width: 1280px) {
            align-items: $iVal !important;
        }
    }
}

.overflow-hidden {
    overflow: hidden;
}

.position-relative {
    position: relative;
}

.position-absolute {
    position: absolute;
}

.outline-none {
    outline: none;
}

.resize-none {
    resize: none;
}

.d-none {
    display: none;
}
