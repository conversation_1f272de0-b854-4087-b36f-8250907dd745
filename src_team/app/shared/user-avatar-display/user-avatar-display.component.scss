@use '../../../variables' as *;

.user-avatar {
    cursor: pointer;
    position: relative;

    &-icon {
        color: $fiveways-button-border;
        width: 20px;
        height: 20px;
    }

    &-container {
        transition: transform .3s;
        display: flex;
        margin-left: 6px;

        &.active {
            transform: rotate(180deg);
        }
    }

    .rotated {
        transform: rotate(180deg);
    }
}

.user-avatar.status-enabled {
    .user-status-indicator {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: 1px solid $fiveways-white;

        &.online {
            background-color: $fiveways-button-text;
        }

        &.offline {
            background-color: $brand-danger;
        }
    }
}
