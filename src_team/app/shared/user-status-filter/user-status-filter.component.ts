import {Component, EventEmitter, Input, Output} from '@angular/core';
import {DropdownComponent} from '../../elements/dropdown/dropdown.component';
import {FormsModule} from '@angular/forms';

@Component({
    selector: 'app-user-status-filter',
    template: `
        <app-5ways-dropdown
            [options]="statuses"
            [(ngModel)]="selectedValue"
            (selectionChange)="onSelectValue($event)"
            placeholder="{{ selectPlaceholder }}">
        </app-5ways-dropdown>
      `,
    imports: [DropdownComponent, FormsModule]
})
export class UserStatusFilterComponent {
    @Input()
    selectPlaceholder: string;

    @Input()
    selectedValue: number;

    @Output()
    changeStatus = new EventEmitter();

    statuses = [{value: 0, viewValue: 'aktywni'}, {value: 1, viewValue: 'zablokowani'}];

    constructor() {
    }

    onSelectValue(value) {
        this.changeStatus.emit(value);
    }

}
