import {iconSetMaterial, themeQuartz} from 'ag-grid-community';

// wygenerowane za pomocą: https://www.ag-grid.com/theme-builder/

export const TABLE_THEME = themeQuartz
    .withPart(iconSetMaterial)
    .withParams({
        accentColor: 'transparent',
        rowHoverColor: '#F5F5F7',
        backgroundColor: '#FBFBFB',
        borderColor: '#DBDFF7',
        cellHorizontalPaddingScale: 1,
        columnBorder: true,
        fontFamily: [
            'ManRope',
        ],
        foregroundColor: "#3A3A3A",
        headerBackgroundColor: "#F5F5F7",
        headerFontFamily: [
            'ManRope',
        ],
        headerFontSize: 12,
        headerFontWeight: 600,
        headerRowBorder: true,
        headerVerticalPaddingScale: 0.8,
        oddRowBackgroundColor: '#FBFBFB',
        checkboxCheckedBackgroundColor: '#007c50',
        rowBorder: true,
        spacing: 8,
        wrapperBorder: true,
        buttonActiveBorder: 'transparent'
    });