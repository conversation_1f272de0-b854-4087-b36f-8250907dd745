import {Pipe, PipeTransform} from '@angular/core';
import {secondsToTimePeriod} from '../../common/utils/date-utils';

@Pipe({ name: 'duration' })
export class DurationPipe implements PipeTransform {

    transform(seconds: number, twoSegmentsMode = false): string {
        if (!seconds) {
            return '-';
        }

        const timePeriod = twoSegmentsMode ? secondsToTimePeriod(seconds) : secondsToTimePeriod(seconds, 3);

        return twoSegmentsMode
            ? timePeriod.segment1.value + timePeriod.segment1.description + ' ' + timePeriod.segment2.value + timePeriod.segment2.description
            : timePeriod.segment1.value + timePeriod.segment1.description + ' ' + timePeriod.segment2.value + timePeriod.segment2.description
                + ' ' + timePeriod.segment3.value + timePeriod.segment3.description;
    }
}
