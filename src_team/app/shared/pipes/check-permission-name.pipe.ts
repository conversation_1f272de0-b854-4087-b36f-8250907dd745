import {Pipe, PipeTransform} from '@angular/core';
import {PermissionService} from '../../services/permission.service';

@Pipe({ name: 'checkPermissionName' })
export class CheckPermissionNamePipe implements PipeTransform {

    constructor(private permissionService: PermissionService) {
    }

    transform(permissionName: string): boolean {
        return this.permissionService.checkPermission(permissionName);
    }
}
