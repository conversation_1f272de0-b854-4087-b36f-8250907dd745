import {ChangeDetector<PERSON><PERSON>, Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {Subscription} from 'rxjs';
import {AttachmentsManagementService} from '../../services/attachments-management.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MatListSubheaderCssMatStyler, MatDivider } from '@angular/material/list';
import { DefaultFlexDirective } from 'ngx-flexible-layout/flex';
import { MatIconButton } from '@angular/material/button';
import { MatTooltip } from '@angular/material/tooltip';
import { MatIcon } from '@angular/material/icon';
import { NgFor } from '@angular/common';
import { DownloadButtonComponent } from '../download-button/download-button.component';

@Component({
    selector: 'app-template-file-list',
    templateUrl: './template-file-list.component.html',
    styleUrls: ['./template-file-list.component.scss'],
    imports: [Mat<PERSON>av<PERSON>ist, MatListSubheaderCssMatStyler, DefaultFlexDirective, MatIconButton, MatTooltip, MatIcon, MatDivider, NgFor, DownloadButtonComponent, TranslatePipe]
})
export class TemplateFileListComponent implements OnInit, OnDestroy {
    filesSaved: any[] = [];
    private subscription: Subscription;

    addAttachmentToolTip = this.translate.instant('TEMPLATE-FILE-LIST.ADD-ATTACHMENT');


    constructor(
        private attachmentsManagementService: AttachmentsManagementService,
        private cd: ChangeDetectorRef,
        private translate: TranslateService
    ) {
    }

    ngOnInit() {
        this.subscription = this.attachmentsManagementService.filesSaved.subscribe(filesSaved => {
            this.filesSaved = filesSaved;
            this.cd.markForCheck();
        });
    }

    ngOnDestroy() {
        this.subscription?.unsubscribe();
    }

    addFile(): void {
        this.attachmentsManagementService.status.next('addFile');
    }

    removeFile(event: MouseEvent, file): void {
        event.preventDefault();
        event.stopPropagation();
        this.attachmentsManagementService.removeFileAndRefreshFileList(file.response.common_file_object_id);
    }
}
