<mat-nav-list>
    <h3 mat-subheader>
        {{'TEMPLATE-FILE-LIST.FILES-LIST' | translate}}
        <span fxFlex></span>
        <button mat-icon-button (click)="addFile()" [matTooltip]="addAttachmentToolTip" matTooltipPosition="after">
            <mat-icon>attachment</mat-icon>
        </button>
    </h3>

    <mat-divider></mat-divider>

    <div class="files-list-wrapper" *ngFor="let file of filesSaved" mat-list-item>
        <app-download-button [fileId]="file.response.common_file_id" [fileName]="file.name" [fileType]="file.type"></app-download-button>
        <span fxFlex></span>
        <button mat-icon-button (click)="removeFile($event, file)">
            <mat-icon>delete</mat-icon>
        </button>
    </div>
</mat-nav-list>
