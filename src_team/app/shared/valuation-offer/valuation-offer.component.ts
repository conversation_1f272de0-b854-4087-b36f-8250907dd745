import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import {ConfirmDialogComponent} from '../confirm-dialog/confirm-dialog.component';
import {filter} from 'rxjs/operators';
import {MatDialog} from '@angular/material/dialog';
import {IssuePaymentOfferInterface} from '../../common/interfaces/issue-payment.interface';
import {IssuePaymentService} from '../../services/issue-payment.service';
import {Router} from '@angular/router';
import { TranslateService, TranslateDirective, TranslatePipe } from '@ngx-translate/core';
import {DateUtilsService} from '../../services/date-utils.service';
import { NgIf, NgFor } from '@angular/common';
import { MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle } from '@angular/material/expansion';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { MatIcon } from '@angular/material/icon';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatCard } from '@angular/material/card';
import { MatFormField, MatLabel, MatSuffix, MatError, MatSelect } from '@angular/material/select';
import { MatInput } from '@angular/material/input';
import { MatAutocompleteTrigger, MatAutocomplete, MatOption } from '@angular/material/autocomplete';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { MatCheckbox } from '@angular/material/checkbox';
@Component({
    selector: 'app-valuation-offer',
    templateUrl: './valuation-offer.component.html',
    styleUrls: ['./valuation-offer.component.scss'],
    imports: [NgIf, MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle, DefaultLayoutDirective, DefaultLayoutAlignDirective, MatIcon, TranslateDirective, MatButton, MatCard, FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, MatAutocompleteTrigger, MatIconButton, MatSuffix, MatError, MatAutocomplete, NgFor, MatOption, DefaultClassDirective, MatSelect, MatCheckbox, TranslatePipe]
})
export class ValuationOfferComponent implements OnInit {
    @Input() recommendedSettingVisible = false;
    @Input() editingAllowed = true;
    @Input() data: any;
    @Input() highlighted = false;
    @Input() set expanded(expand: boolean) {
        this._expanded = expand;
    }
    @Input() set editMode(edit: boolean) {
        edit ? this.starEditing() : this.cancelEditing();
    }
    @Input() set templates(value: any) {
        this._templates = value;
        this.shownTemplates = value;
    }

    @Output() save = new EventEmitter();
    @Output() delete = new EventEmitter();
    @Output() edit = new EventEmitter();
    @Output() cancel = new EventEmitter();

    _editMode = false;
    _expanded = false;
    _templates: IssuePaymentOfferInterface[];

    shownTemplates: IssuePaymentOfferInterface[];
    offerForm: UntypedFormGroup;
    timeMetric = 'hours';
    workTime: number;
    workTimeDescription: string;
    manHoursDescription: string;
    issueId: number;

    constructor(
        private matDialog: MatDialog,
        private formBuilder: UntypedFormBuilder,
        private issuePaymentService: IssuePaymentService,
        private router: Router,
        public translate: TranslateService,
        private _dateUtilsService: DateUtilsService
    ) {
        this.issueId = +this.router.url.split('/')[2].split('?')[0];

        this.offerForm = this.formBuilder.group({
            'name': ['', [Validators.required, Validators.maxLength(512)]],
            'amount': [null, [Validators.required, Validators.pattern('^[0-9]+((\.|\,)[0-9]{1,2})?$'), Validators.min(1)]],
            'work_time': [null, [Validators.required, Validators.pattern('^[0-9]*$'), Validators.min(1)]],
            'is_recommended': [null],
            'hourly_rate': [null, [Validators.required, Validators.pattern('^[0-9]+((\.|\,)[0-9]{1,2})?$'), Validators.min(1)]],
            'man_hours': [null, [Validators.required, Validators.pattern('^[0-9]*$'), Validators.min(1)]]
        });
    }

    private setWorkTime(hours: number) {
        const workTime = this._dateUtilsService.getWorkTime(hours);
        this.workTimeDescription = workTime.timeDescription;
        this.timeMetric = workTime.timeMetric;
        this.workTime = workTime.workTime;
    }

    private setManHours(hours: number) {
        const manHours = this._dateUtilsService.getWorkTime(hours);

        this.manHoursDescription = manHours.timeDescription;
    }

    public calcAmount() {
        const hourlyRate = +this.offerForm.get('hourly_rate').value,
            manHours = +this.offerForm.get('man_hours').value;

        if (!manHours || !hourlyRate) {
            return;
        }

        this.offerForm.get('amount').setValue(manHours * hourlyRate);
    }

    ngOnInit() {
        if (this.data) {
            this.setWorkTime(+this.data.work_time);
            this.setManHours(+this.data.man_hours);
        }
    }

    get nameCtrl() {
        return this.offerForm.get('name');
    }

    get amountCtrl() {
        return this.offerForm.get('amount');
    }

    get workTimeCtrl() {
        return this.offerForm.get('work_time');
    }

    get hourlyRateCtrl() {
        return this.offerForm.get('hourly_rate');
    }

    get manHoursCtrl() {
        return this.offerForm.get('man_hours');
    }

    get isRecommendedCtrl() {
        return this.offerForm.get('is_recommended');
    }

    starEditing() {
        this._editMode = true;
        this.setRecommendedVisible();

        this.nameCtrl.setValue(this.data.name);
        this.amountCtrl.setValue(this.data.amount);
        this.manHoursCtrl.setValue(this.data.man_hours);
        this.hourlyRateCtrl.setValue(this.data.hourly_rate);
        this.workTimeCtrl.setValue(this.workTime);
        this.isRecommendedCtrl.setValue(+this.data.is_recommended);

        this.edit.emit();
    }

    cancelEditing() {
        this.offerForm.reset();
        this._editMode = false;
        this.cancel.emit();
    }

    setRecommendedVisible() {
        this.issuePaymentService.getIssuePaymentOffers(this.issueId).subscribe(
            paymentOffers => {
                this.recommendedSettingVisible = true;

                paymentOffers.forEach(paymentOffer => {
                    if (+paymentOffer.IssuePaymentOffer.is_recommended && +paymentOffer.IssuePaymentOffer.id !== +this.data.id) {
                        this.recommendedSettingVisible = false;
                    }
                });
            }
        );
    }

    onCancelEditingClick() {
        this._expanded = true;
        this.cancelEditing();
    }

    saveOffer() {
        const offer = {
            ...this.offerForm.value,
            work_time: this.timeMetric === 'hours' ? this.workTimeCtrl.value : this.workTimeCtrl.value * 24,
            id: this.data.id ? +this.data.id : null
        };

        this.save.emit(offer);
    }

    deleteOffer(offerId: number) {
        this.matDialog.open(
            ConfirmDialogComponent,
            {
                width: '400px',
                data: {
                    header: this.translate.instant('SHARED.ARE-YOU-SURE-DELETED-TEMPLATE'),
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => !!result)).subscribe(
            () => this.delete.emit(offerId)
        );
    }

    onNameKeyUp(event) {
        if (!this._templates || !event.target.value) {
            return;
        }

        const name = event.target.value;

        this.shownTemplates = this._templates.filter(item => item.IssuePaymentOffer.name.toLowerCase().includes(name.toString().toLowerCase()));
    }

    onTemplateSelected(event) {
        if (!this._templates || !event.option.value) {
            return;
        }

        const selectedTemplate = this._templates.find(template => template.IssuePaymentOffer.name === event.option.value);

        if (selectedTemplate) {
            this.offerForm.get('name').setValue(selectedTemplate.IssuePaymentOffer.name);
            this.offerForm.get('amount').setValue(selectedTemplate.IssuePaymentOffer.amount);
            this.offerForm.get('work_time').setValue(selectedTemplate.IssuePaymentOffer.work_time);
            this.offerForm.get('man_hours').setValue(selectedTemplate.IssuePaymentOffer.man_hours);
            this.offerForm.get('hourly_rate').setValue(selectedTemplate.IssuePaymentOffer.hourly_rate);
        }
    }
}
