import {Component, Input, Output, EventEmitter} from '@angular/core';
import {SimpleMenuItem} from '../../common/interfaces/simple-menu.interface';
import { NgIf, NgFor } from '@angular/common';

@Component({
    selector: 'app-simple-menu',
    templateUrl: './simple-menu.component.html',
    styleUrls: ['./simple-menu.component.scss'],
    imports: [NgIf, NgFor]
})
export class SimpleMenuComponent {
    @Input()
        menuItems: SimpleMenuItem[] = [];

    @Input()
        isOpen: boolean = false;

    @Output()
        isOpenChange = new EventEmitter<boolean>();

    onItemClicked(item: SimpleMenuItem): void {
        item.action();
        this.toggleMenu();
    }

    toggleMenu(): void {
        this.isOpen = !this.isOpen;
        this.isOpenChange.emit(this.isOpen);
    }
}
