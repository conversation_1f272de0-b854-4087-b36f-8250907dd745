import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { TopMenuService } from '../../services/top-menu.service';
import { ButtonVariant } from '../../common/enums/button-variant.enum';
import { Subscription } from 'rxjs';
import {ButtonComponent} from '../../elements/button/button.component';
import {NgIf} from '@angular/common';
import {CheckPermissionNamePipe} from '../pipes/check-permission-name.pipe';
import {TranslatePipe} from '@ngx-translate/core';

@Component({
    selector: 'app-clients-top-menu-buttons',
    templateUrl: './clients-top-menu-buttons.component.html',
    imports: [
        ButtonComponent,
        NgIf,
        CheckPermissionNamePipe,
        TranslatePipe
    ],
    styleUrls: ['./clients-top-menu-buttons.component.scss']
})
export class ClientsTopMenuButtonsComponent implements OnInit, OnDestroy {

    protected readonly ButtonVariant = ButtonVariant;

    private subscriptions = new Subscription();

    public selectedMode = false;

    constructor(
        private topMenuService: TopMenuService
    ) {}

    ngOnInit(): void {
        this.subscriptions.add(
            this.topMenuService.selectedMode$.subscribe(isEnabled => {
                this.selectedMode = isEnabled;
            })
        );
    }

    ngOnDestroy(): void {
        this.subscriptions?.unsubscribe();
    }

    onAddClicked() {
        this.topMenuService.emitAction({ type: 'ADD' });
    }

    onSendMailClicked() {
        this.topMenuService.emitAction({ type: 'SEND_MAIL' });
    }

    onSendIssueClicked() {
        this.topMenuService.emitAction({ type: 'SEND_ISSUE' });
    }

}
