<ng-container>
    <ng-container *ngIf="!selectedMode; else selectedModeTemplate">
        <app-5ways-button
                *ngIf="'clientsListManagement' | checkPermissionName"
                class="mt-3"
                iconLeft="plus"
                [variant]="ButtonVariant.TERTIARY"
                (click)="onAddClicked()">
            {{ 'USERS-LIST.ADD' | translate }}
        </app-5ways-button>
    </ng-container>

    <ng-template #selectedModeTemplate>
        <app-5ways-button
                class="mt-3 mr-16"
                iconLeft="plus"
                [variant]="ButtonVariant.TERTIARY"
                (click)="onSendMailClicked()">
            {{ 'MENU-SIDEBAR.MAIL.CLIENTS-MESSAGE' | translate }}
        </app-5ways-button>

        <app-5ways-button
                *ngIf="'sendIssue' | checkPermissionName"
                class="mt-3"
                iconLeft="plus"
                [variant]="ButtonVariant.TERTIARY"
                (click)="onSendIssueClicked()">
            {{ 'ISSUE-LIST.ADD' | translate }}
        </app-5ways-button>
    </ng-template>
</ng-container>
