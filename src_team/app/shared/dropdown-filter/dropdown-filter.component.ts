import {Component, EventEmitter, Input, Output} from '@angular/core';
import {FilterOption} from '../../common/interfaces/filter-option.interface';
import { DropdownComponent } from '../../elements/dropdown/dropdown.component';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-dropdown-filter',
    template: `
        <app-5ways-dropdown
                [options]="options"
                [searchMode]="searchMode"
                [(ngModel)]="selectedValue"
                (selectionChange)="onSelectValue($event)"
                placeholder="{{ selectPlaceholder }}">
        </app-5ways-dropdown>
    `,
    imports: [DropdownComponent, FormsModule]
})
export class DropdownFilterComponent {

    @Input()
    filterLabel: string;

    @Input()
    selectedValue: number;

    @Input()
    selectPlaceholder: string;

    @Input()
    options: FilterOption[];

    @Input()
    searchMode: boolean = false;

    @Output()
    changePriority = new EventEmitter();

    onSelectValue(value) {
        this.changePriority.emit(value);
    }

}
