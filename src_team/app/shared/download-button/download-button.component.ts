import {ChangeDetectorRef, Component, inject, Input} from '@angular/core';
import saveAs from 'file-saver';
import {IssueSentenceService} from '../../services/issue-sentence.service';
import {MatButton} from '@angular/material/button';
import {MatTooltip} from '@angular/material/tooltip';
import {NgIf, NgStyle} from '@angular/common';
import {MatProgressSpinner} from '@angular/material/progress-spinner';
import {DefaultStyleDirective} from 'ngx-flexible-layout/extended';
import {MatIcon} from '@angular/material/icon';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';

@Component({
    selector: 'app-download-button',
    template: `
        <button mat-button color="primary" matTooltip="Pobierz załącznik" matTooltipPosition="above" (click)="downloadAttachment()" [disabled]="disabled" class="download-button">
            <mat-spinner diameter="20" *ngIf="disabled" [ngStyle]="{display: 'inline-block', 'margin-right': '4px'}"></mat-spinner>
            <mat-icon *ngIf="!disabled">{{ iconTypeByMime(fileType) }}</mat-icon>
            <span class="common-file-name-wrapper">{{ fileName }}</span>
        </button>
    `,
    styles: [`
        .common-file-name-wrapper {
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .download-button {
            max-width: 100%;
            text-align: left;
        }
    `],
    standalone: true,
    imports: [MatButton, MatTooltip, NgIf, MatProgressSpinner, NgStyle, DefaultStyleDirective, MatIcon]
})
export class DownloadButtonComponent {
    @Input() fileId: string;
    @Input() fileName: string;
    @Input() fileType: string;

    disabled = false;

    private alertService: AlertService = inject(AlertService);

    constructor(private issueSentence: IssueSentenceService, private changeDetectorRef: ChangeDetectorRef) {
    }

    iconTypeByMime(mime) {
        let icon = 'attachment';

        if (/^image\/.*$/.test(mime)) {
            icon = 'image';
        }

        return icon;
    }

    downloadAttachment() {
        this.disabled = true;
        this.issueSentence.downloadAttachment(this.fileId).subscribe(result => {
                try {
                    const isFileSaverSupported = !!new Blob;
                } catch (e) {
                    this.disabled = false;

                    return console.error(e);
                }
                const blob = new Blob([result.body], {type: this.fileType});

                saveAs(blob, this.fileName);

                this.disabled = false;
                this.changeDetectorRef.detectChanges();

            },
            error => {
                this.disabled = false;
                this.changeDetectorRef.detectChanges();

                const message = error.status === 403 ? 'Trwa skanowanie pliku.' : 'Wystąpił błąd podczas pobierania pliku.';

                this.alertService.showAlert(message, AlertType.ERROR, AlertDuration.MEDIUM);
            }
        );
    }
}
