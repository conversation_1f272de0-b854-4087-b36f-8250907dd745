import {ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges, OnInit, OnDestroy, inject} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { filter, map, take } from 'rxjs/operators';
import { TagService } from '../../services/tag.service';
import { IssueService } from '../../services/issue.service';
import { TagAddComponent } from '../../settings/tags/tag-add/tag-add.component';
import { IsMobileService } from '../../services/is-mobile.service';
import { Subscription } from 'rxjs';
import { ObjectNameType } from '../../interfaces/object-name-type';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {NgFor, NgIf, NgStyle, SlicePipe} from '@angular/common';
import {MatChip, MatChipListbox, MatChipRemove} from '@angular/material/chips';
import {DefaultLayoutAlignDirective, DefaultLayoutDirective} from 'ngx-flexible-layout/flex';
import {DefaultStyleDirective} from 'ngx-flexible-layout/extended';
import {MatIcon} from '@angular/material/icon';
import {MatTooltip} from '@angular/material/tooltip';
import {MatIconButton} from '@angular/material/button';
import {MatMenu, MatMenuContent, MatMenuItem, MatMenuTrigger} from '@angular/material/menu';
import {ButtonComponent} from '../../elements/button/button.component';
import {CheckPermissionNamePipe } from '../pipes/check-permission-name.pipe';import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';

@Component({
    selector: 'app-tags',
    templateUrl: './tags.component.html',
    styleUrls: ['./tags.component.scss'],
    standalone: true,
    imports: [NgIf, MatChipListbox, DefaultLayoutDirective, NgFor, MatChip, NgStyle, DefaultStyleDirective, MatIcon, MatChipRemove, MatTooltip, MatIconButton, MatMenuTrigger, ButtonComponent, MatMenu, MatMenuContent, MatMenuItem, DefaultLayoutAlignDirective, SlicePipe, CheckPermissionNamePipe, TranslatePipe]
})
export class TagsComponent implements OnChanges, OnInit, OnDestroy {

    @Input() maxVisibleTags: number = 2;

    @Input()
    objectName: ObjectNameType = 'issue';

    @Input()
    objectId;

    @Input()
    tagsIds = '';

    @Input()
    clientId: string;

    @Input()
    editMode = true;

    @Input()
    iconsMode = false;

    @Input()
    place: 'issue' | 'client' | 'mail' = 'issue';

    private tagsChangedSubscription: Subscription;

    shownIconsCount: number;
    actualTags = [];
    allTags = [];
    loadedAll = false;

    private alertService: AlertService = inject(AlertService);

    constructor(
        private tagService: TagService,
        private issueService: IssueService,
        public dialog: MatDialog,
        private isMobileService: IsMobileService,
        public translate: TranslateService,
        private cdr: ChangeDetectorRef
    ) {
        this.isMobileService.isTabletView.subscribe(isTabletView => this.shownIconsCount = isTabletView ? 2 : 3);
    }

    ngOnInit() {
        this.tagsChangedSubscription = this.tagService.tagsChanged.subscribe(
            change => {
                if (change.objectName === this.objectName && +change.objectId === +this.objectId) {
                    this.tagsIds = change.tags;
                    this.getTags();
                    this.cdr.detectChanges();
                }
            }
        );
    }

    ngOnDestroy() {
        if (this.tagsChangedSubscription) {
            this.tagsChangedSubscription?.unsubscribe();
        }
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['tagsIds']) {
            this.getTags();
        }
    }

    getTags() {
        if (!this.tagsIds) {
            this.actualTags = [];
            return;
        }

        const tagsArray = this.tagsIds.match(/\d+/g);

        if (!tagsArray || !tagsArray.length) {
            this.actualTags = [];
            return;
        }

        this.tagService.getTagsByIdsArray(tagsArray)
            .pipe(
                take(1),
                map(filter => filter.filter(tag => +tag[this.place] === 1))
            )
            .subscribe(result => {
                this.actualTags = result || [];
                this.cdr.detectChanges(); // Wymuszamy detekcję zmian
            });
    }

    addTag(id) {
        const actual = (this.actualTags || []).map(tag => tag.id);

        if (actual.filter(tagId => tagId === id).length) {
            return;
        }

        this.allTags = this.allTags.filter(tag => tag.id !== id);
        this.cdr.detectChanges();

        actual.push(id);

        this.tagService.setTagsTo(this.objectName, this.objectId, actual).subscribe(
            result => {
                if (result && result.status !== 'OK') {
                    return;
                }

                this.tagsIds = actual.length > 0 ? `(${actual.join('),(')})` : '';

                this.getTags();

                this.updateAvailableTags();

                setTimeout(() => {
                    this.cdr.detectChanges();
                }, 0);

                this.alertService.showAlert(this.translate.instant('ISSUE-TAGS.TAGS-ADDED'), AlertType.SUCCESS, AlertDuration.MEDIUM);
            }
        );
    }

    removeTag(id) {
        const removedTag = this.actualTags.find(tag => tag.id === id);
        const actual = (this.actualTags || []).map(tag => tag.id).filter(tagId => tagId !== id);

        if (removedTag) {
            this.allTags = [...this.allTags, removedTag].sort((a, b) => a.name.localeCompare(b.name));
            this.cdr.detectChanges();
        }

        this.tagService.setTagsTo(this.objectName, this.objectId, actual).subscribe(result => {
            if (result && result.status !== 'OK') {
                return;
            }

            this.tagsIds = actual.length > 0 ? `(${actual.join('),(')})` : '';

            this.getTags();

            // Aktualizujemy listę dostępnych etykiet po usunięciu
            this.updateAvailableTags();

            setTimeout(() => {
                this.cdr.detectChanges();
            }, 0);

            this.alertService.showAlert(this.translate.instant('ISSUE-TAGS.TAGS-DELETE'), AlertType.SUCCESS, AlertDuration.MEDIUM);
        });
    }

    addNewTag() {
        this.dialog.open(TagAddComponent, {
            width: '290px',
            data: {insertNow: true, place: [this.place]},
            panelClass: 'full-width-dialog'
        }).afterClosed()
            .pipe(filter(result => !!result))
            .subscribe(tagId => {
                if (tagId) {
                    this.addTag(tagId);
                }
            });
    }

    trackByIndex(index, item) {
        return item.id;
    }

    get shownIcons() {
        if (!this.actualTags) {
            return [];
        }

        return this.actualTags.slice(0, this.shownIconsCount);
    }

    get remainingIcons() {
        if (!this.actualTags) {
            return [];
        }

        return this.actualTags.slice(this.shownIconsCount);
    }
    getRemainingTagsTooltip(): string {
        if (!this.actualTags || this.actualTags.length <= this.maxVisibleTags) {
            return '';
        }

        const remaining = this.actualTags.slice(this.maxVisibleTags);
        return remaining.map(tag => tag.name).join(', ');
    }

    /**
     * Aktualizuje listę dostępnych etykiet, usuwając te, które są już dodane
     */
    updateAvailableTags(): void {
        const addedTagsIds = (this.actualTags || []).map(element => element.id);

        this.tagService.allTags$
            .pipe(
                take(1),
                map(filter => filter.filter(tag => +tag[this.place] === 1))
            )
            .subscribe(result => {
                this.allTags = result.filter(element => !addedTagsIds.includes(element.id));
                this.loadedAll = true;
                this.cdr.detectChanges();
            });
    }
}
