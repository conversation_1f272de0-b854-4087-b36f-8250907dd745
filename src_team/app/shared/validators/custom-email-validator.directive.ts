import {Directive} from '@angular/core';
import {AbstractControl, NG_VALIDATORS, ValidationErrors, Validator} from '@angular/forms';

@Directive({
    selector: '[customEmailValidator]',
    providers: [
        {
            provide: NG_VALIDATORS,
            useExisting: CustomEmailValidatorDirective,
            multi: true
        }
    ]
})
export class CustomEmailValidatorDirective implements Validator {
    validate(control: AbstractControl): ValidationErrors | null {
        if (!control.value) {
            return null;
        }

        const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailPattern.test(control.value) ? null : { email: true };
    }
}
