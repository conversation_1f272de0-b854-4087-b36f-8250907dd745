import {Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output} from '@angular/core';
import {Subscription} from 'rxjs';
import {SequencedItem} from '../../common/interfaces/sequenced-item.interface';
import {IsMobileService} from '../../services/is-mobile.service';
import { Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON>tyle, AsyncPipe } from '@angular/common';
import { MatNavList, MatListItem } from '@angular/material/list';
import { DefaultLayoutDirective } from 'ngx-flexible-layout/flex';
import { MatIcon } from '@angular/material/icon';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { DefaultStyleDirective } from 'ngx-flexible-layout/extended';

@Component({
    selector: 'app-draggable-items-list',
    templateUrl: './draggable-items-list.component.html',
    styleUrls: ['./draggable-items-list.component.scss'],
    imports: [NgI<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>F<PERSON>, MatListItem, DefaultLayoutDirective, MatIcon, MatProgressSpinner, Ng<PERSON>tyle, DefaultStyleDirective, AsyncPipe]
})
export class DraggableItemsListComponent implements OnInit, OnDestroy {
    @Input() items: SequencedItem[] = [];
    @Input() displayedFieldName = '';
    @Input() labelFieldName: string;
    @Input() itemsLoaded = false;

    @Output() itemClicked: EventEmitter<SequencedItem> = new EventEmitter();
    @Output() itemDropped: EventEmitter<SequencedItem[]> = new EventEmitter();

    private dropItemSubscription: Subscription;

    constructor(public isMobileService: IsMobileService) {
    }

    private onDropItem(htmlIds: string[]) {
        const changedItems = htmlIds.map((id, index) => {
            const item = this.items.find((element: SequencedItem) => element.id === id);

            return index === +item.sequence ? {id: '', sequence: ''} : {id: item.id, sequence: index.toString()};
        }).filter(element => element.id !== '');

        this.itemDropped.emit(changedItems);
    }

    ngOnInit() {
    }

    ngOnDestroy() {
        if (this.dropItemSubscription) {
            this.dropItemSubscription?.unsubscribe();
        }
    }

    onItemClick(item: SequencedItem) {
        this.itemClicked.emit(item);
    }
}
