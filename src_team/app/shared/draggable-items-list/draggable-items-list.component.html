<div *ngIf="itemsLoaded; else loading" class="draggable-items-list">
    <mat-nav-list
        *ngIf="!(isMobileService.isHandsetOrTablet | async); else mobileList"
        class="draggable-list"
    >
        <mat-list-item *ngFor="let item of items;" (click)="onItemClick(item)" class="list-item" fxLayout="row" fxLayoutAlign="start center">
            <mat-icon class="drag-handle">drag_handle</mat-icon>
            <span class="item-name">{{item[displayedFieldName]}}</span>
            <span *ngIf="item[labelFieldName]" class="label">{{item[labelFieldName]}}</span>
            <span style="display: none">{{item.id}}</span>
        </mat-list-item>
    </mat-nav-list>

    <ng-template #mobileList>
        <mat-nav-list>
            <mat-list-item *ngFor="let item of items;" (click)="onItemClick(item)">
                {{item[displayedFieldName]}}
                <span style="display: none">{{item.id}}</span>
            </mat-list-item>
        </mat-nav-list>
    </ng-template>
</div>

<ng-template #loading>
    <mat-spinner [ngStyle]="{margin: '0 auto'}"></mat-spinner>
</ng-template>
