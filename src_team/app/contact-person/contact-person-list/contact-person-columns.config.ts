import {ColDef, CellClickedEvent, ValueGetterParams} from 'ag-grid-community';
import {TranslateService} from '@ngx-translate/core';
import {BlockedInfoRendererComponent} from '../../components/blocked-info-renderer/blocked-info-renderer.component';
import {isNullOrWhitespace} from '../../common/utils/string-utils';

export function getContactPersonColumnDefs(
    translate: TranslateService,
    openContactPersonDetails: (event: CellClickedEvent) => void,
): ColDef[] {
    return [
        {
            field: 'first_name',
            headerName: translate.instant('CONTACT-PERSONS-LIST.FIRSTNAME'),
            minWidth: 150,
            flex: 1,
            sortingOrder: ['asc', 'desc'],
            sortable: true,
            cellClass: 'custom-ellipsis-cell',
            filter: false,
            valueGetter: (params: ValueGetterParams) => `${params.data?.ContactPerson.first_name}`,
            onCellClicked: openContactPersonDetails
        },
        {
            field: 'last_name',
            headerName: translate.instant('CONTACT-PERSONS-LIST.LASTNAME'),
            minWidth: 150,
            flex: 1,
            sortingOrder: ['asc', 'desc'],
            sortable: true,
            cellClass: 'custom-ellipsis-cell',
            filter: false,
            valueGetter: (params: ValueGetterParams) => `${params.data?.ContactPerson.last_name ?? ''}`,
            onCellClicked: openContactPersonDetails
        },
        {
            field: 'company_name',
            headerName: translate.instant('CONTACT-PERSONS-LIST.COMPANY-NAME'),
            minWidth: 200,
            flex: 1,
            sortable: false,
            cellClass: 'custom-ellipsis-cell',
            filter: false,
            valueGetter: (params: ValueGetterParams) => {
                const companyName: string = params.data?.IssueInitiator?.name;
                const firstName: string = params.data?.IssueInitiator?.first_name ?? '';
                const lastName: string = params.data?.IssueInitiator?.last_name ?? '';

                const name: string = (!companyName || companyName === 'undefined') ? `${lastName} ${firstName}` : companyName;

                return isNullOrWhitespace(name) ? '' : name;
            },
            onCellClicked: openContactPersonDetails
        },
        {
            field: 'email',
            headerName: translate.instant('CONTACT-PERSONS-LIST.E-MAIL'),
            minWidth: 250,
            flex: 2,
            sortingOrder: ['asc', 'desc'],
            sortable: true,
            cellClass: 'custom-ellipsis-cell',
            filter: false,
            icons: {
                sortAscending: '<i data-lucide="chevron-up" style="color: #1A9267; width: 12px; height: 16px; stroke-width: 3px; display: flex; align-items: center;"></i>',
                sortDescending: '<i data-lucide="chevron-down" style="color: #1A9267; width: 12px; height: 16px; stroke-width: 3px; display: flex; align-items: center;"></i>',
                sortUnSort: ''
            },
            cellRenderer: BlockedInfoRendererComponent,
            valueGetter: (params: ValueGetterParams) => `${params.data?.ContactPerson?.email ?? ''}`,
            onCellClicked: openContactPersonDetails
        },
        {
            field: 'phone',
            headerName: translate.instant('CONTACT-PERSONS-LIST.PHONE'),
            minWidth: 200,
            flex: 1,
            sortable: false,
            cellClass: 'custom-ellipsis-cell',
            filter: false,
            valueGetter: (params: ValueGetterParams) => `${params.data?.ContactPerson.phone ?? ''}`,
            onCellClicked: openContactPersonDetails
        }

    ];
}
