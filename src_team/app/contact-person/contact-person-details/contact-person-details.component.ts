import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { ButtonVariant } from '../../common/enums/button-variant.enum';
import { ContactPersonService } from '../../services/contact-person.service';
import { DialogService } from '../../services/dialog-details.service';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { ClientsService } from '../../services/clients.service';
import { AlertType } from '../../common/enums/alert-type.enum';
import { AlertService } from 'src_team/app/services/alert.service';
import { AvatarModule } from 'ngx-avatars';
import { ButtonComponent } from '../../elements/button/button.component';
import { CommonModule } from '@angular/common';
import { MatTooltip } from '@angular/material/tooltip';

@Component({
    selector: 'app-contact-person-details',
    templateUrl: './contact-person-details.component.html',
    styleUrls: ['./contact-person-details.component.scss'],
    imports: [AvatarModule, TranslatePipe, ButtonComponent, CommonModule, MatTooltip]
})
export class ContactPersonDetailsComponent implements OnInit, OnDestroy {

    @Input()
    public data: any;

    public contactName: string;
    public company: string;
    private subscriptions: Subscription = new Subscription();
    public readonly ButtonVariant = ButtonVariant;

    constructor(
        private contactPersonService: ContactPersonService,
        private dialogService: DialogService,
        public translate: TranslateService,
        private clientsService: ClientsService,
        private alertService: AlertService
    ) {}

    ngOnInit(): void {
        this.contactName = `${this.data?.first_name} ${this.data?.last_name}`;
        this.loadCompanyName();
    }

    ngOnDestroy(): void {
        this.subscriptions?.unsubscribe();
    }

    private loadCompanyName(): void {
        const initiatorId = this.data?.issue_initiator_id;

        if (initiatorId) {
            const sub = this.clientsService.getClients('?id=' + initiatorId + '&limit=1').subscribe(
                (clients) => {
                    const client = clients?.items?.[0];

                    if (client) {
                        this.company = client.name || `${client.last_name} ${client.first_name}`;
                    }
                }
            );

            this.subscriptions.add(sub);
        }
    }

    editContactPerson() {
        this.contactPersonService.setPersonEdit(true, this.data);
    }

    removeContactPerson() {
        this.subscriptions.add(
            this.contactPersonService.deleteContactPerson(this.data.id).subscribe(() => {
                    this.alertService.showAlert(this.translate.instant('CONTACT-PERSON-DETAILS.REMOVE-SUCCESS'), AlertType.SUCCESS);
                    this.dialogService.closeDialog();
                },
                error => this.alertService.showAlert(this.translate.instant('CONTACT-PERSON-DETAILS.REMOVE-ERROR'), AlertType.ERROR)
            )
        );
    }
}
