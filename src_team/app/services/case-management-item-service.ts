import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class CaseManagementItemService {
    private panelState = new BehaviorSubject<boolean>(false);
    public panelState$ = this.panelState.asObservable();

    expandPanel() {
        this.panelState.next(true);
    }

    collapsePanel() {
        this.panelState.next(false);
    }
}
