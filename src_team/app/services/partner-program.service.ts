import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {EnvironmentService} from './environment.service';
import {Observable} from 'rxjs';
import {PartnerProgramListTableInterface, WidgetInterface} from '../interfaces/partner-program';
import {map, tap} from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class PartnerProgramService {
    /**
     * obiekt env
     * @private
     */
    private _environment: any;

    constructor(private _httpClient: HttpClient, private _environmentService: EnvironmentService) {
        this._environment = this._environmentService.selectedEnvironment;
    }

    /**
     * pobieramy wszystkie widgety wchodzące w skład naszego customer_id
     */
    public getWidgets(): Observable<PartnerProgramListTableInterface[]> {
        return this._httpClient.get(this._environment.apiUrl + 'widget')
            .pipe(map((response: { results, total: number }) => response.results.map(element => element.Widget)));
    }

    /**
     * tworzymy nowy widget
     * @param data
     */
    public createWidget(data: WidgetInterface): Observable<WidgetInterface> {
        return this._httpClient.post<WidgetInterface>(this._environment.apiUrl + 'widget', JSON.stringify({Widget: data}));
    }
}

