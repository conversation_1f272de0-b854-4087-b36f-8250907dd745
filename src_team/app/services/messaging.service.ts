import {inject, Injectable} from '@angular/core';
import {AlertService} from './alert.service';
import {AlertType} from '../common/enums/alert-type.enum';
import {AlertDuration} from '../common/enums/alert-duration.enum';

@Injectable({
    providedIn: 'root'
})
export class MessagingService {
    private alertService: AlertService = inject(AlertService);

    constructor() {
    }

    showError(message: string) {
        this.alertService.showAlert(message, AlertType.ERROR, AlertDuration.MEDIUM);
    }

    showConfirmation(message: string) {
        this.alertService.showAlert(message, AlertType.SUCCESS, AlertDuration.MEDIUM);
    }

    logToConsoleAndShowError(message: string, err) {
        console.error(err);
        this.showError(message);
    }
}
