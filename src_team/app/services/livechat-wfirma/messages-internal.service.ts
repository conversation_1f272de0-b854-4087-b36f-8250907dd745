import {Injectable} from '@angular/core';
import {environment} from '../../../environments/environment';
import {DataResponseInterface} from '../../common/interfaces/data-response.interface';
import {map} from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import {AuthService} from '../auth/auth.service';
import {Observable} from 'rxjs';
import {TranslateService} from '@ngx-translate/core';
import {ChatInternalMessages} from '../../common/interfaces/chat-internal-messages.interface';
import {ChatMessage} from '../../common/interfaces/chat-message.interface';

@Injectable({
    providedIn: 'root'
})
export class MessagesInternalService {
    messagesUrl = environment.apiUrl + 'chat_internal_message';

    constructor(private http: HttpClient, private authService: AuthService, public translate: TranslateService) {
    }

    getFirstMessageInThreadId(threadId: number) {
        return this.http.get<DataResponseInterface>(this.messagesUrl + '?chat_internal_thread_id=' + threadId + '&limit=1&order=id|asc').pipe(
            map(result => result.results[0]),
            map((message: any) => message ? +message.ChatInternalMessage.id : null)
        );
    }

    getMessages(threadId: number, lastMessageId: number, order: string, packetSize: number): Observable<ChatInternalMessages[]> {
        const idQuery = lastMessageId
                ? order === 'desc'
                    ? '&id=lt_' + lastMessageId
                    : '&id=gt_' + lastMessageId
                : '',
            query = '?chat_internal_thread_id=' + threadId + '&order=id|' + order + '&limit=' + packetSize + idQuery;

        return this.http.get<DataResponseInterface>(this.messagesUrl + query).pipe(
            map(result => result.results),
            map(messages => messages.map(message => message.ChatInternalMessage))
        );
    }

    setMessageIsRead(messageId: number, isRead: boolean) {
        const body = {
            ChatInternalMessage: {
                is_read: isRead,
            }
        };

        return this.http.put<ChatInternalMessages>(this.messagesUrl, JSON.stringify(body));
    }

    postMessage(message: any): Observable<ChatInternalMessages> {
        const body = {
            ChatInternalMessage: {
                user_id: this.authService.getUserId(),
                chat_internal_thread_id: message.chat_internal_thread_id,
                message: message.message,
                type: message.type,
                is_read: false,
            }
        };

        return this.http.post<ChatInternalMessages>(this.messagesUrl, JSON.stringify(body));
    }
}
