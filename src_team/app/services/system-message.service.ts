import { Injectable } from '@angular/core';
import {Observable, of} from 'rxjs';
import { HttpClient } from '@angular/common/http';
import {EnvironmentService} from './environment.service';
import {SystemMessageInterface, SystemMsgsApiResponse, SystemMessageUserInterface, SystemMsgsUserApiResponse} from '../common/interfaces/system-message.interface';
import {map, tap} from 'rxjs/operators';
import {DataResponseInterface} from '../common/interfaces/data-response.interface';

@Injectable({
  providedIn: 'root'
})
export class SystemMessageService {
    private environment: any;

    constructor(
        private http: HttpClient,
        private environmentService: EnvironmentService
    ) {
        this.environment = this.environmentService.selectedEnvironment;
    }

    getSystemMessages(ids: string[] | null = null): Observable<SystemMessageInterface[]> {

       if (ids !== null) {
            const conditions = 'id=' + ids.join(',');
            return this.http.get<SystemMsgsApiResponse>(this.environment.apiUrl + `system_message/?${conditions}&limit=1000&order=created|desc`).pipe(
                map(response => response.results.map(result => result.SystemMessage))
            );
        } else {
           return this.http.get<SystemMsgsApiResponse>(this.environment.apiUrl + `system_message/?limit=1000&order=created|desc`).pipe(
               map(response => response.results.map(result => result.SystemMessage))
           );
       }
    }

    getSystemMessageUsers(userId: number): Observable<SystemMessageUserInterface[]> {
        return this.http.get<SystemMsgsUserApiResponse>(this.environment.apiUrl + `system_message_user/?user_id=${userId}&limit=1000`)
            .pipe(
                map(response => response.results
                    .map(result => result.SystemMessageUser)
                )
            );
    }

    updateReadStatus(id: number): Observable<any> {

        const user = {
            SystemMessageUser: {
                is_read: '1'
            }
        };

        return this.http.put(this.environment.apiUrl + 'system_message_user/' + id, JSON.stringify(user));
    }
}
