import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class SidebarsContainerService {

    private _actual = new BehaviorSubject<string | null>(null);
    private _menuState = new BehaviorSubject<boolean>(true);
    private _usersState = new BehaviorSubject<boolean>(true);
    private _bannerState = new BehaviorSubject<boolean>(true);

    users = {
        expand: true,
        mode: 'side' as 'over' | 'push' | 'side',
        drawerPush: true,
        canExpand: true
    };

    menu = {
        expand: false,
        mode: 'side' as 'over' | 'push' | 'side',
        drawerPush: false
    };

    banner = {
        expand: true,
        mode: 'side' as 'over' | 'push' | 'side',
        drawerPush: true
    };

    constructor() {
        this.updateStates();
    }

    sidebar(value: 'menu' | 'users' | 'banner') {
        this._actual.next(value);
        return this;
    }

    expand() {
        const sidebar = this[this._actual.getValue()];
        if (sidebar) {
            sidebar.expand = true;
            this.updateStates();
        }
        return this;
    }

    compress() {
        const sidebar = this[this._actual.getValue()];
        if (sidebar) {
            sidebar.expand = false;
            this.updateStates();
        }
        return this;
    }

    toggle() {
        const sidebar = this[this._actual.getValue()];
        if (sidebar) {
            sidebar.expand = !sidebar.expand;
            this.updateStates();
        }
        return this;
    }

    mode(mode: 'side' | 'over' | 'push') {
        const sidebar = this[this._actual.getValue()];
        if (sidebar) {
            sidebar.mode = mode;
        }
        return this;
    }

    drawerPush(disable: boolean) {
        const sidebar = this[this._actual.getValue()];
        if (sidebar) {
            sidebar.drawerPush = disable;
        }
        return this;
    }

    private updateStates() {
        this._menuState.next(this.menu.expand);
        this._usersState.next(this.users.expand);
    }

    isExpanded(value: 'menu' | 'users' | 'banner'): boolean {
        return this[value].expand;
    }

}
