import {Injectable} from '@angular/core';
import {Observable, ReplaySubject} from 'rxjs';
import {distinctUntilChanged, filter} from 'rxjs/operators';
import {UserInputStorageService} from './user-input-storage.service';
import {AuthService} from './auth/auth.service';
import { HttpClient } from '@angular/common/http';
import {UserService} from './user.service';
import {environment} from '../../environments/environment';

@Injectable({
    providedIn: 'root'
})
export class ApplicationSettingsService {
    environment = environment;

    settingsSubject: ReplaySubject<{name: string, value: any}>;

    public settings = {
        ATTACHMENT_MAX_FILE_SIZE: 1024 * 1024 * 16,
        AVATAR_MAX_FILE_SIZE: 1024 * 100,
        USERS_REFRESH_INTERVAL: 1000 * 60,
        APPLICATION_VERSION_CHECK_INTERVAL: 1000 * 60 * 60,
        USERS_LAST_ACTIVE_THRESHOLD_1: 1000 * 60 * 10,
        USER_MAX_IDLE_TIME: 1000 * 60 * 60,
        IDLE_CHECK_TIME: 1000 * 60,
        CHAT_MESSAGE_PACKET_SIZE: 10,
        CHAT_IS_REPLYING_TIMEOUT: 10000,
        darkMode: 0,
        language: null,
        agGridState: null,
        sidebar: {
            opened: true,
            caseManagement: {
                visible: false
            },
            chat: {
                visible: false
            },
            chat_internal: {
                visible: false
            },
            poll: {
                visible: false
            }
        }
    };

    propagateDarkModeValue() {
        this.settings.darkMode = +this.userInputStorageService.getValue('darkMode');
        this.settingsSubject.next({name: 'darkMode', value: this.settings.darkMode});
    }

    propagateLanguageValue() {
        this.userService.getUser().subscribe(result => {
            this.settings.language = result.language;

            const localLanguage = this.userInputStorageService.getValue('language');

            if (!localLanguage || localLanguage !== this.settings.language) {
                localStorage.setItem('language', this.settings.language);
            }
        });
    }

    constructor(
        authService: AuthService,
        private userInputStorageService: UserInputStorageService,
        public http: HttpClient,
        public userService: UserService
    ) {
        this.settingsSubject = new ReplaySubject(1);

        this.propagateDarkModeValue();

        authService.isLoggedIn
            .pipe(
                distinctUntilChanged(),
                filter(isLoggedIn => isLoggedIn)
            )
            .subscribe(() => {
                this.propagateDarkModeValue();
                this.propagateLanguageValue();
            });
    }

    getValue(key: string) {
        return this.settings[key];
    }

    setValue(name: string, value: any) {
        this.settings[name] = value;
        this.userInputStorageService.setValue(name, value);
        this.settingsSubject.next({name, value});
    }

    sidebarGetValue(itemName: string, fieldName: string) {
        return this.settings['sidebar']?.[itemName]?.[fieldName];
    }

    sidebarSetValue(itemName: string, fieldName: string, value: any, settingsId: number) {
        this.settings['sidebar'][itemName][fieldName] = value;

        const name = itemName + '_' + fieldName;

        this.userInputStorageService.setValue(name, value);
        this.settingsSubject.next({name, value});

        this.setSidebarConfig(settingsId)
            .subscribe();
    }

    getConfig() {
        return this.http.get(this.environment.apiUrl + 'settings/');
    }

    setSidebarConfig(settingsId: number) {
        const data = {
            Settings: {
                sidebar: this.settings['sidebar'],
                dashboard: {}
            }
        };

        return this.http.put(this.environment.apiUrl + 'settings/' + settingsId,
            data
        );
    }
}
