import {Injectable} from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {EnvironmentService} from './environment.service';
import {PermissionService} from './permission.service';

@Injectable({
    providedIn: 'root'
})
export class ReportService {
    private environment: any;

    constructor(
        private http: HttpClient,
        private environmentService: EnvironmentService,
    ) {
        this.environment = this.environmentService.selectedEnvironment;
    }

    getReport(params: HttpParams = new HttpParams(), page: number = 1): Observable<{ total: number, reports: any[] }> {
        if (!params.has('page') && page > 1) {
            params = params.set('page', page.toString());
        }

        if (!params.has('order')) {
            params = params.set('order', 'lastname|ASC');
        }

        return this.http.get(`${this.environment.apiUrl}report`, { params })
            .pipe(
                map((response: any) => ({
                    total: response.total,
                    reports: response.results.map((res: any) => res.Report)
                }))
            );
    }

    getReportAnnual(userId, year): Observable<any> {
        return this.http.get(this.environment.apiUrl + 'report_annual/?' + 'year=' + year + '&id=' + userId)
            .pipe( map((response: {results: Object[]}) => response.results.map((res: any) => res.Report)));
    }

    getLoggedUserWorkTime(): Observable<number> {
        return this.http.get(this.environment.apiUrl + 'report/1')
            .pipe(map((response: { Report: { work_time: number } }) => response.Report.work_time)
            );
    }

}
