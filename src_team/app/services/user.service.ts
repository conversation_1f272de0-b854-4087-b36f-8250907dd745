import {Injectable} from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import dayjs from 'dayjs';
import {Observable, Subject} from 'rxjs';
import {map} from 'rxjs/operators';
import {AuthService} from './auth/auth.service';
import {EnvironmentService} from './environment.service';
import {TranslateService} from '@ngx-translate/core';

export interface GetUsersConditionsInterface {
    groupId?: any;
    roleId?: any;
    banned?: any;
    userName?: string;
}

@Injectable({
    providedIn: 'root'
})
export class UserService {
    public refreshUsersTable$: Subject<void> = new Subject<void>();

    private environment: any;
    userEdited: Subject<number> = new Subject();


    constructor(
        environmentService: EnvironmentService,
        private http: HttpClient,
        private authService: AuthService,
        public translate: TranslateService
    ) {
        this.environment = environmentService.selectedEnvironment;
    }

    getUsers(
        conditions: string | { [key: string]: any } | HttpParams = '',
        groupFilterVal?: string
    ): Observable<any> {
        let paramsString = '';

        if (typeof conditions === 'string') {
            paramsString = conditions;
        } else if (conditions instanceof HttpParams) {
            paramsString = conditions.toString();
        } else if (typeof conditions === 'object' && conditions !== null) {
            // Konwertuj obiekt na query string
            paramsString = Object.entries(conditions)
                .filter(([key, value]) => value !== undefined && value !== null && value !== '')
                .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
                .join('&');
        }

        // Dodaj znak zapytania tylko, jeśli paramsString nie jest pusty
        const groupPart = +groupFilterVal > 0 ? '/user_group' : '';
        const queryPart = paramsString ? `?${paramsString}` : '';
        const url = `${this.environment.apiUrl}user${groupPart}${queryPart}`;

        return this.http.get(url).pipe(
            map((response: { results: object[], total: number }) => {
                const results = response.results
                    .filter((res: any) => {
                        if (!res.UserGroup) return true;
                        return res.UserGroup.length;
                    })
                    .map((res: any) => {
                        const user = res.User;
                        user.role = res.UserRole;
                        user.isOnline = res.OauthAccessToken.length > 0
                            ? dayjs(res.OauthAccessToken[0].expires).valueOf() >= Date.now()
                            : false;
                        user.lastActiveTime = res.OauthAccessToken[0] ? res.OauthAccessToken[0].expires : '';
                        return user;
                    });

                return {
                    results,
                    total: response.total
                };
            })
        );
    }


    getUser(userId: number | null = null) {
        if (!+userId) {
            userId = this.authService.getUserId();
        }

        return this.http.get(this.environment.apiUrl + 'user/' + userId).pipe(
            map((res: any) => {
                const user = res.User;

                if (res.UserRole) {
                    user.role = res.UserRole;
                }

                return user;
            })
        );
    }

    getUserRoles(): Observable<any> {
        return this.http.get(this.environment.apiUrl + 'user_role').pipe(map((res: any) => res.results.map(role => role.UserRole)));
    }

    addUser(data) {
        return this.http.post(this.environment.apiUrl + 'user', JSON.stringify({User: data}));
    }

    resetPassword(data) {
        const user = {
            User: {
                password: data.password,
                password_confirmation: data.password_confirmation,
                id: data.id,
                key: data.key
            }
        };

        return this.http.put(this.environment.apiUrl + 'user_password/' + data.id, JSON.stringify(user));
    }

    changePassword(newPassword, confirmPassword, oldPassword): Observable<any> {
        return this.http.put(this.environment.apiUrl + 'user_change_password/' + this.authService.getUserId(),
            {User: {password: newPassword, password_confirmation: confirmPassword, password_old: oldPassword}}
        );
    }

    changeEmail(email, emailConfirmation, password): Observable<any> {
        return this.http.put(this.environment.apiUrl + 'user_change_email/' + this.authService.getUserId(),
            {User: {email, email_confirmation: emailConfirmation, password}}
        );
    }

    editUser(userId, data) {
        const user = {
            User: {
                email: data.email,
                firstname: data.firstname,
                lastname: data.lastname,
                company_name: data.company_name,
                phone: data.phone,
                description: data.description,
                user_role_id: data.user_role_id,
                mentor_id: data.mentor_id,
                has_user_access: data.has_user_access,
                has_chat_access: data.has_chat_access,
                has_poll_access: data.has_poll_access,
                banned: data.banned,
                is_public: data.is_public
            }
        };

        return this.http.put(this.environment.apiUrl + 'user/' + userId, JSON.stringify(user));
    }

    banUser(userId, banned) {
        const user = {
            User: {
                banned: banned
            }
        };

        return this.http.put(this.environment.apiUrl + 'user/' + userId, JSON.stringify(user)).pipe(
            map(res => {
                    return res;
                }
            )
        );
    }

    changeLanguage(language) {
        this.translate.use(language);

        return this.http.put(this.environment.apiUrl + 'user/' + this.authService.getUserId(), JSON.stringify({User: {language}}));
    }

    deleteUser(userId) {
        return this.http.delete(this.environment.apiUrl + 'user/' + userId).pipe(
            map(res => {
                    return res;
                }
            )
        );
    }

    getUsersWithGroups() {
        return this.http.get(this.environment.apiUrl + 'user/user_group&User_banned=0&User_is_client=0&limit=9999').pipe(
            map((response: any) => response.results.map(({User, UserGroup, UserRole, OauthAccessToken}) => ({User, UserGroup, UserRole, OauthAccessToken}))),
            map((users: any[]) => users.map(user => {
                user.User.role = user.UserRole;

                user.User.isOnline = user.OauthAccessToken.length > 0 ? dayjs(user.OauthAccessToken[0].expires).valueOf() >= Date.now() : false;
                user.User.lastActiveTime = user.OauthAccessToken[0] ? user.OauthAccessToken[0].expires : '';

                return user;
            }))
        );
    }

    addCompany(companyName) {
        return this.http.post(this.environment.apiUrl + 'customer_company', JSON.stringify(
            {
                'CustomerCompany': {
                    name: companyName
                }
            }
        ));
    }

    getCompany() {
        return this.http.get(this.environment.apiUrl + 'customer_company');
    }

    editCompany(companyId, data) {
        return this.http.put(this.environment.apiUrl + 'customer_company/' + companyId, JSON.stringify(data));
    }

    getUserHistoryChanges(modelName: string): Observable<any> {
        const params = new HttpParams()
            .set('model_name', modelName)
            .set('limit', '9999');

        return this.http.get(this.environment.apiUrl + 'modification_history_change', { params }).pipe(
            map((response: any) => {
                return {
                    ...response
                };
            })
        );
    }

    getOtherInfo(conditions: string): Observable<any> {
        return this.http.get(this.environment.apiUrl + 'rodo_other_info/' + conditions);
    }

    addOtherInfo(obj: any): Observable<any> {
        return this.http.post(this.environment.apiUrl + 'rodo_other_info/', JSON.stringify({ RodoOtherInfo: obj }));
    }

    updateOtherInfo(clientId: string, obj: any): Observable<any> {
        return this.http.put(this.environment.apiUrl + `rodo_other_info/${clientId} `, JSON.stringify({ RodoOtherInfo: obj }));
    }

    getActiveUserCounter(): Observable<{userCounter: number}> {
        const params = new HttpParams()
            .set('banned', '0')
            .set('is_client', '0')
            .set('limit', '1');

        return this.http.get(this.environment.apiUrl + 'user', { params })
            .pipe(map((response: {total: number}) => ({userCounter: response.total})))
    }

    refreshTable() {
        this.refreshUsersTable$.next();
    }
}
