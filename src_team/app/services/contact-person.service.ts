import { Injectable } from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import { map } from 'rxjs/operators';
import {BehaviorSubject, Observable, Subject} from 'rxjs';
import { EnvironmentService } from './environment.service';
import { ContactPersonInterface } from '../common/interfaces/contact-person.interface';

@Injectable({
    providedIn: 'root'
})
export class ContactPersonService {
    private environment: any;

    private personEdit = new Subject<{ isOpen: boolean; data?: any }>();
    personEdit$ = this.personEdit.asObservable();

    private hasChanged = new Subject<boolean>();
    hasChanged$ = this.hasChanged.asObservable();

    constructor(private http: HttpClient, private environmentService: EnvironmentService) {
        this.environment = this.environmentService.selectedEnvironment;
    }

    addContactPerson(data: Partial<ContactPersonInterface>): Observable<any> {
        return this.http.post(this.environment.apiUrl + 'contact_person/', JSON.stringify({ ContactPerson: data }))
            .pipe(map(() => {
                this.hasChanged.next(true);
            }));
    }

    updateContactPerson(id: number, data: Partial<ContactPersonInterface>): Observable<any> {
        return this.http.put(this.environment.apiUrl + 'contact_person/' + id, JSON.stringify({ ContactPerson: data }))
            .pipe(map(() => this.hasChanged.next(true)));
    }

    deleteContactPerson(id: number): Observable<any> {
        return this.http.delete(this.environment.apiUrl + 'contact_person/' + id)
            .pipe(map(() => this.hasChanged.next(true)));
    }

    getContactPersonById(id: number): Observable<ContactPersonInterface> {
        return this.http.get(this.environment.apiUrl + 'contact_person/' + id).pipe(
            map((res: any) => res.ContactPerson)
        );
    }

    getContactPersons(conditions: string | HttpParams = ''): Observable<any> {
        return this.http.get<any>(`${this.environment.apiUrl}contact_person${conditions}`)
            .pipe(
                map(response => ({
                    items: response,
                    totalCount: response.total
                }))
            );
    }

    setPersonEdit(isOpen: boolean, data: any) {
        this.personEdit.next({isOpen, data});
    }

}
