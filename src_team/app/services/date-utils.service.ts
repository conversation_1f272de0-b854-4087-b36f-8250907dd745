import { Injectable } from '@angular/core';
import {TranslateService} from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class DateUtilsService {

  constructor(private _translate: TranslateService) {
  }

  public getWorkTime(hours: number) {
    const timeMetric = (hours <= 24 || hours % 24 !== 0) ? 'hours' : 'days';

    if (timeMetric === 'days') {
      return {
        timeMetric,
        workTime: hours / 24,
        timeDescription: this._translate.instant('SHARED.DAYS'),
      };
    }

    let timeDescription: string;

    const digits = hours.toString(10).split(''),
        lastDigit = +digits[digits.length - 1];

    if (digits.length === 1) {
      if (lastDigit === 1) {
        timeDescription = this._translate.instant('SHARED.HOUR');
      } else {
        timeDescription = [2, 3, 4].includes(lastDigit) ? this._translate.instant('SHARED.HOURS') : this._translate.instant('SHARED.MORE-HOURS');
      }
    } else {
      const penultimateDigit = +digits[digits.length - 2];

      if (penultimateDigit === 1) {
        timeDescription = this._translate.instant('SHARED.MORE-HOURS');
      } else {
        timeDescription = [2, 3, 4].includes(lastDigit) ? this._translate.instant('SHARED.HOURS') : this._translate.instant('SHARED.MORE-HOURS');
      }
    }

    return {
      timeDescription,
      timeMetric,
      workTime: hours
    };
  }

}
