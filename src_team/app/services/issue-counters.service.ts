import {Injectable, On<PERSON><PERSON>roy} from '@angular/core';
import {forkJoin, Observable, of, ReplaySubject, BehaviorSubject, combineLatest, Subscription, fromEvent} from 'rxjs';
import {catchError, filter, map, shareReplay, startWith, tap, debounceTime, take} from 'rxjs/operators';
import { UserInputStorageService } from './user-input-storage.service';
import { IssueService } from './issue.service';
import { Title } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { environment } from '../../environments/environment';
import {SocketIssuesService} from './sockets/socket-issues.service';
import {Store} from '@ngrx/store';
import * as IssueCountersActions from '../store/issue-counters/issue-counters.actions';
import {IssueCountersInterface} from '../common/interfaces/issue-counters.interface';

interface ListWithGroupId {
    listName: string;
    userGroupId: number;
}

@Injectable({
    providedIn: 'root'
})
export class IssueCountersService implements OnD<PERSON>roy {
    private countersSubject = new BehaviorSubject<IssueCountersInterface>({
        my: 0,
        unassigned: 0,
        unaccepted: 0,
        delegated: 0,
    });

    counters$ = this.countersSubject.asObservable();

    refreshListCounter: ReplaySubject<ListWithGroupId> = new ReplaySubject(1);
    unassignedList$: Observable<number[]>;
    myList$: Observable<number[]>;
    unacceptedList$: Observable<number[]>;
    delegatedList$: Observable<number[]>;
    onlineSubscription: Subscription;
    socketConnectSubscription: Subscription;

    constructor(
        private userInputStorageService: UserInputStorageService,
        private issueService: IssueService,
        private socketIssuesService: SocketIssuesService,
        private titleService: Title,
        private translate: TranslateService,
        private store: Store
    ) {
        this.myList$ = this.refreshListCounter
            .pipe(
                startWith({ listName: 'my', userGroupId: null }),
                filter(listData => listData.listName === 'my'),
                map(listData => this.getUserIdsFromFilters('my')),
                shareReplay(1)
            );
        this.unassignedList$ = this.refreshListCounter
            .pipe(
                startWith({ listName: 'unassigned', userGroupId: null }),
                filter(listData => listData.listName === 'unassigned'),
                map(listData => this.getUserIdsFromFilters('unassigned')),
                shareReplay(1)
            );

        this.delegatedList$ = this.refreshListCounter.pipe(
            startWith({ listName: 'delegated', userGroupId: null }),
            filter(listData => listData.listName === 'delegated'),
            map(listData => this.getUserIdsFromFilters('delegated')),
            shareReplay(1)
        );

        this.unacceptedList$ = this.refreshListCounter.pipe(
            startWith({ listName: 'unaccepted', userGroupId: null }),
            filter(listData => listData.listName === 'unaccepted'),
            map(listData => this.getUserIdsFromFilters('unaccepted')),
            shareReplay(1)
        );

        this.initializeCounters();
        this.listenToSocketChanges();
        this.listenToGroupChanges();
        this.setupReconnectionHandlers();
    }

    private initializeCounters(): void {
        const delegatedUserIds = this.getUserIdsFromFilters('delegated');
        const unacceptedUserIds = this.getUserIdsFromFilters('unaccepted');

        if (delegatedUserIds.length > 0) {
            this.refreshListCounter.next({ listName: 'delegated', userGroupId: delegatedUserIds[0] });
        }
        if (unacceptedUserIds.length > 0) {
            this.refreshListCounter.next({ listName: 'unaccepted', userGroupId: unacceptedUserIds[0] });
        }
    }

    private getUserIdsFromFilters(listName: string): number[] {
        const settingsStr = this.userInputStorageService.getValue('filters_' + listName);

        if (!settingsStr) {
            return [];
        }

        try {
            const settings = JSON.parse(settingsStr);

            if (settings && settings.filters && Array.isArray(settings.filters)) {
                const groupOwnersFilter = settings.filters.find(settingsFilter => settingsFilter.key === 'groupOwners');

                if (groupOwnersFilter && Array.isArray(groupOwnersFilter.value) && groupOwnersFilter.value.length) {
                    return groupOwnersFilter.value;
                }
            }
        } catch (error) {
            console.error(`Error parsing filters for ${listName}:`, error);
        }

        return [];
    }

    updateAllCounters(): Observable<IssueCountersInterface> {
        const delegatedUserIds = this.getUserIdsFromFilters('delegated');
        const unacceptedUserIds = this.getUserIdsFromFilters('unaccepted');

        return forkJoin({
            my: this.issueService.getCountingIssues('my'),
            unassigned: this.issueService.getCountingIssues('unassigned'),
            unaccepted: this.issueService.getCountingIssues('unaccepted', unacceptedUserIds),
            delegated: this.issueService.getCountingIssues('delegated', delegatedUserIds)
        }).pipe(
            tap(newCounters => {
                const updatedCounters = { ...newCounters };
                this.store.dispatch(IssueCountersActions.loadIssueCountersSuccess({ issueCounters: updatedCounters }));
                this.setTitle(updatedCounters.my);
                this.countersSubject.next(updatedCounters);
            }),
            catchError(error => {
                console.error('Error updating issue-counters:', error);
                this.store.dispatch(IssueCountersActions.loadIssueCountersFailure({ error: error.message }));
                return of({ ...this.countersSubject.value });
            })
        );
    }

    private setTitle(myIssuesCount: number) {
        const title = myIssuesCount === 0
            ? environment.appTitle
            : this.translate.instant('MENU-SIDEBAR.NEW-ISSUES', { myIssuesCount });
        this.titleService.setTitle(title);
    }

    private listenToSocketChanges(): void {
        combineLatest([
            this.socketIssuesService.myIssuesObservable,
            this.socketIssuesService.unassignedIssuesObservable,
            this.socketIssuesService.unacceptedIssuesObservable,
            this.socketIssuesService.delegatedIssuesObservable
        ]).pipe(
            debounceTime(500)
        ).subscribe();
    }

    private listenToGroupChanges(): void {
        this.delegatedList$.subscribe(userIds => {
            this.updateCounterForList('delegated', userIds);
        });

        this.unacceptedList$.subscribe((userIds: number[]) => {
            this.updateCounterForList('unaccepted', userIds);
        });
    }

    private updateCounterForList(listName: string, userIds: number[]): void {
        this.issueService.getCountingIssues(listName, userIds).subscribe(count => {
            const updatedCounters = {
                ...this.countersSubject.value,
                [listName]: count
            };

            this.countersSubject.next(updatedCounters);

            this.store.dispatch(IssueCountersActions.loadIssueCountersSuccess({
                issueCounters: updatedCounters
            }));
        });
    }

    /**
     * Obsługa zdarzeń ponownego połączenia
     */
    private setupReconnectionHandlers(): void {
        this.onlineSubscription = fromEvent(window, 'online').subscribe(() => {
            this.updateAllCounters().pipe(take(1)).subscribe();
        });
        if (this.socketIssuesService['socket']) {
            this.socketConnectSubscription = new Observable<void>(observer => {
                this.socketIssuesService['socket'].on('connect', () => {
                    observer.next();
                });
                return () => {
                    if (this.socketIssuesService['socket']) {
                        this.socketIssuesService['socket'].off('connect');
                    }
                };
            }).subscribe(() => {
                this.updateAllCounters().pipe(take(1)).subscribe();
            });
        }
    }

    ngOnDestroy(): void {
        this.onlineSubscription?.unsubscribe();
        this.socketConnectSubscription?.unsubscribe();
    }
}
