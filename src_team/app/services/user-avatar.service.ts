import {Injectable} from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {DomSanitizer} from '@angular/platform-browser';
import {Observable, of} from 'rxjs';
import {catchError, map, switchMap} from 'rxjs/operators';
import {EnvironmentService} from './environment.service';

@Injectable({
    providedIn: 'root'
})
export class UserAvatarService {
    private environment: any;

    constructor(private http: HttpClient, private domSanitizer: DomSanitizer, private environmentService: EnvironmentService) {
        this.environment = this.environmentService.selectedEnvironment;
    }

    blobToString(user, blob) {
        return new Observable(observer => {
            const fileReader = new FileReader();

            fileReader.onload = () => observer.next({
                ...user,
                avatar: this.domSanitizer.bypassSecurityTrustUrl(fileReader.result.toString())
            });
            fileReader.onloadend = () => observer.complete();
            fileReader.onerror = err => observer.error(err);

            fileReader.readAsDataURL(blob);
        });
    }

    addUserAvatar(user: any) {
        const userWithoutAvatar = {
            ...user,
            avatar: ''
        };

        if (+user.User.has_avatar === 0) {
            return of(userWithoutAvatar);
        }

        return this.getAvatar(user.User.id).pipe(
            switchMap((blob: Blob) => this.blobToString(user, blob)),
            catchError(() => of(userWithoutAvatar))
        );
    }

    getAvatar(userId): Observable<any> {
        return this.http.get(this.environment.apiUrl + 'common_file_user_avatar/' + userId, {responseType: 'blob'});
    }

    getDefaultAvatar(avatarUrl: string) {
        return this.http.get('/assets/images/' + avatarUrl, {responseType: 'blob'}).pipe(
            switchMap(blob => this.blobToString({}, blob)),
            map((obj: any) => obj.avatar)
        );
    }
}
