import {Injectable} from '@angular/core';
import {map, pluck} from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import {IssueInitiatorNote} from '../common/interfaces/issue-initaitor-note.interface';
import {EnvironmentService} from './environment.service';

@Injectable({
    providedIn: 'root'
})
export class IssueInitiatorNoteService {
    private endpointUrl: string;

    constructor(private http: HttpClient, private environmentService: EnvironmentService) {
        this.endpointUrl = this.environmentService.selectedEnvironment.apiUrl  + 'issue_initiator_note';
    }

    getNotesForInitiator(initiatorId: number) {
        return this.http.get(this.endpointUrl + '?issue_initiator_id=' + initiatorId + '&limit=999').pipe(
            pluck('results'),
            map((notes: any[]) =>  notes.map(item => item.IssueInitiatorNote))
        );
    }

    addNote(note: IssueInitiatorNote) {
        return this.http.post(this.endpointUrl, JSON.stringify({IssueInitiatorNote: note}));
    }

    updateNote(note: IssueInitiatorNote) {
        return this.http.put(this.endpointUrl + '/' + note.id, JSON.stringify({IssueInitiatorNote: note}));
    }

    deleteNote(noteId: number) {
        return this.http.delete(this.endpointUrl + '/' + noteId);
    }
}
