import {Injectable} from '@angular/core';
import {<PERSON><PERSON>, Line, PdfMakeWrapper, Txt} from 'pdfmake-wrapper';
import htmlToPdfMake from 'html-to-pdfmake';
import {UserInterface} from '../common/interfaces/user.interface';
import {IssueInterface} from '../common/interfaces/issue.interface';
import {SentenceDataInterface} from '../common/interfaces/sentence-data.interface';
import {IssueSentenceInterface} from '../common/interfaces/issue-sentence.interface';
import {TranslateService} from '@ngx-translate/core';
import {forkJoin} from 'rxjs';
import {IssueSentenceService} from './issue-sentence.service';
import {UserStoreService} from './store/user-store.service';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable({
    providedIn: 'root'
})
export class PdfExportService {

    pdf: PdfMakeWrapper;

    issueInternalId: number;
    fileName: string;

    styles = {
        subject: {
            fontSize: 14,
            bold: true
        },
        rodo: {
            fontSize: 12,
            bold: true
        },
        date: {
            color: '#787878',
            fontSize: 9
        },
        log: {
            italics: true
        },
        username: {
            bold: true
        }
    };

    constructor(
        public translate: TranslateService,
        private issueSentenceService: IssueSentenceService,
        private userStoreService: UserStoreService
    ) {}

    private makeFooter(text) {
        this.pdf.footer(new Txt(text).alignment('center').fontSize(9).end);
    }

    public initPdf(texts) {
        if (!texts) {
            return;
        }

        Object.entries(texts).forEach((v: [string, any]) => {
            texts[v[0]] = v[1].replace(/%issueInternalId/, this.issueInternalId);
        });

        const {title, author, subject, footer, fileName} = texts;

        this.fileName = fileName;

        this.pdf = new PdfMakeWrapper();

        this.makeFooter(footer);

        this.pdf.pageSize('A4');

        this.pdf.defaultStyle({
            fontSize: 11
        });

        this.pdf.styles(this.styles);

        this.pdf.info({title, author, subject});
    }

    private drawLine() {
        this.pdf.add(this.pdf.ln(1));
        this.pdf.add(
            new Canvas([
                new Line([0, 0], [520, 0]).end
            ]).end
        );
        this.pdf.add(this.pdf.ln(1));
    }

    private drawSentence(text, date, user) {
        this.pdf.add({text: user, style: 'username'});
        this.pdf.add({text: date, style: 'date'});
        this.pdf.add(htmlToPdfMake(text));
        this.drawLine();
    }

    private drawLog(text, date) {
        this.pdf.add({text: date, style: 'date'});
        this.pdf.add({text, style: 'log'});
        this.drawLine();
    }

    public download() {
        this.pdf.create().download(this.fileName);
    }

    issueToPdf(
        texts: any,
        issueData: IssueInterface,
        issueSentences: Array<SentenceDataInterface | IssueSentenceInterface>,
        ownerData?: UserInterface,
        initiatorName?: string
    ) {
        this.issueInternalId = issueData.issue_internal_id;

        this.initPdf(texts);

        this.pdf.add({text: this.translate.instant('ISSUE-PRINT-VIEW.TITLE') + ': ' + issueData.subject, style: 'subject'});
        this.pdf.add(this.translate.instant('ISSUE-PRINT-VIEW.NUMBER') + ': ' + issueData.issue_internal_id);

        if (ownerData) {
            this.pdf.add(this.translate.instant('ISSUE-PRINT-VIEW.USER') + ': ' + ownerData.firstname + ' ' + ownerData.lastname);
        }

        this.pdf.add(this.pdf.ln(1));

        issueSentences.forEach(sentence => {
            const {body, speaker, created} = sentence['IssueSentence'];

            if (['client_log'].includes(speaker)) {
                return;
            }

            if (['log'].includes(speaker)) {
                this.drawLog(body, created);

                return;
            }

            const {firstname, lastname} = sentence['User'];

            if (speaker === 'initiator' && firstname) {
                initiatorName = firstname;
            }

            this.drawSentence(body, created, speaker === 'initiator' ? initiatorName : (firstname + ' ' + lastname));
        });

        this.download();
    }

    downloadIssuePDF(issueData: IssueInterface, initiatorFirstName: string, showLogs: boolean): void {
        const speakerQuery = `initiator,owner${showLogs ? ',log' : ''}`;
        const texts = {
            title: this.translate.instant('ISSUE-VIEW-MENU.ISSUE-NUMBER'),
            author: '5ways.com',
            subject: this.translate.instant('ISSUE-VIEW-MENU.ISSUE-PRINT'),
            footer: this.translate.instant('ISSUE-VIEW-MENU.ISSUE-PRINT'),
            fileName: this.translate.instant('ISSUE-VIEW-MENU.ISSUE-FILE-NAME')
        };

        forkJoin([
            this.issueSentenceService.getIssueSentencesWithUsers(
                `IssueSentence_issue_id=${issueData.id}&status=sent&speaker=${speakerQuery}&order=IssueSentence_modified|ASC&limit=500`
            ),
            this.userStoreService.getUserFromStoreTake1(issueData.owner_id)
        ]).subscribe(
            response => {
                const owner = response[1] ? response[1].User : null;
                this.issueToPdf(texts, issueData, response[0], owner, initiatorFirstName);
            },
            (error: HttpErrorResponse) => console.error(error)
        );
    }


    public resetPdf() {
        this.pdf = null;
    }

}
