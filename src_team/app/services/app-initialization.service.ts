import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { first, filter } from 'rxjs/operators';
import * as UserSelectors from '../store/user/user.selectors';
import * as UserActions from '../store/user/user.actions';

@Injectable({
    providedIn: 'root'
})
export class AppInitializationService {
    constructor(private store: Store) {}

    initializeUserData(): Promise<any> {
        return new Promise<void>((resolve) => {
            this.store.select(UserSelectors.selectCurrentUser)
                .pipe(first())
                .subscribe(user => {
                    if (user === null) {
                        this.store.dispatch(UserActions.loadCurrentUser());

                        this.store.select(UserSelectors.selectCurrentUser)
                            .pipe(
                                filter(loadedUser => loadedUser !== null),
                                first()
                            )
                            .subscribe(() => resolve());
                    } else {
                        resolve();
                    }
                });
        });
    }
}
