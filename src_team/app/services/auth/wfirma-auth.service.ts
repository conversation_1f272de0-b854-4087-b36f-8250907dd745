import { Injectable } from '@angular/core';
import {EnvironmentService} from '../environment.service';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class WfirmaAuthService {
  private environment: any;

  constructor(
      environmentService: EnvironmentService,
      private http: HttpClient
  ) {
    this.environment = environmentService.selectedEnvironment;
  }

  wfirmaAuthorize(code: string) {
    return this.http.post(
        this.environment.wFirmaOauth2.authCodeEndpoint,
        {
          'WfirmaOauth2': {
            'auth_code': code
          }
        }
    );
  }

  wFirmaCheckAuth() {
    return this.http.get(
        this.environment.wFirmaOauth2.authCodeEndpoint
    );
  }

  wFirmaLogOut(id: string) {
    return this.http.delete(
        this.environment.wFirmaOauth2.authCodeEndpoint + `/${id}`
    );
  }

  wFirmaCompanyAuthorize(id: string, companyId: string, companyName: string) {
    return this.http.put(
        this.environment.wFirmaOauth2.authCodeEndpoint + `/${id}`,
        {
          'WfirmaOauth2': {
            'company_id': companyId,
            'name': companyName
          }
        }
    );
  }

  wFirmaSetTaxRate(id: string, taxRate: string, companyId: string) {
    return this.http.put(
        this.environment.wFirmaOauth2.authCodeEndpoint + `/${id}`,
        {
          'WfirmaOauth2': {
            'company_id': companyId,
            'lump_tax_rate': taxRate
          }
        }
    );
  }
}
