import {Injectable} from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import {filter, map, take, first} from 'rxjs/operators';
import {AuthService} from '../auth/auth.service';
import {UserService} from '../user.service';
import {Observable} from 'rxjs';
import {UserInterface} from '../../common/interfaces/user.interface';
import UserRolesId from '../../common/enums/user-roles-id.enum';
import * as UserSelectors from '../../store/user/user.selectors';
import {Store} from '@ngrx/store';

@Injectable({
    providedIn: 'root'
})
export class TraineeAccessDeniedGuard  {
    constructor(private store: Store) {
    }

    canActivate(
        next: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<boolean> {
        return this.store.select(UserSelectors.selectCurrentUser).pipe(
            first(),
            map((user: UserInterface | null) => {
                return !isTraineeUser(user);
            })
        );
    }
}

export function isTraineeUser(user: UserInterface): boolean {
    return (user && +user.user_role_id === UserRolesId.TRAINEE);
}
