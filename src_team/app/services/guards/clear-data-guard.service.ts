import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { EmailService } from '../email.service';

@Injectable({
    providedIn: 'root'
})
export class ClearDataGuard implements CanActivate {

    constructor(private emailService: EmailService) {}

    canActivate(
        next: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<boolean> | Promise<boolean> | boolean {
        this.emailService.clearAllData();
        return true;
    }
}
