import {Injectable} from '@angular/core';
import {NavigationEnd, Route, Router} from '@angular/router';
import {BehaviorSubject, Observable, of, Subject} from 'rxjs';
import {filter, switchMap} from 'rxjs/operators';
import {MenuItem} from '../interfaces/menu-item';
import {ThreadsInternalService} from './livechat-wfirma/threads-internal.service';
import {CustomerService} from './Customer.service';
import {Store} from '@ngrx/store';
import { selectIssueCountersState } from '../store/issue-counters/issue-counters.selectors';
import { TopMenuAction } from '../common/interfaces/top-menu-action.interface';

@Injectable({
    providedIn: 'root'
})
export class TopMenuService {

    private chatInternalId: number;
    private chatInternalState: boolean = true;
    private readonly menuItemsSubject$ = new BehaviorSubject<Readonly<MenuItem[]>>([]);
    menuItems$ = this.menuItemsSubject$.asObservable();

    private showMailButtonsSubject$ = new BehaviorSubject<boolean>(false);
    showMailButtons$ = this.showMailButtonsSubject$.asObservable();

    private topMenuActionsSubject = new Subject<TopMenuAction>();
    topMenuActions$: Observable<TopMenuAction> = this.topMenuActionsSubject.asObservable();

    private selectedModeSubject = new BehaviorSubject<boolean>(false);
    selectedMode$: Observable<boolean> = this.selectedModeSubject.asObservable();

    constructor(
        private router: Router,
        private threadsInternalService: ThreadsInternalService,
        private customerService: CustomerService,
        private store: Store

    ) {
        this.getChatInternalPath();

        this.router.events
            .pipe(filter(event => event instanceof NavigationEnd))
            .subscribe(() => {
                this.updateMenuItems();
                this.updateShowMailButtons();
            });

        this.updateShowMailButtons();
        this.updateMenuItems();
    }

    /**
     * Aktualizuje listę elementów menu w oparciu o aktualną trasę.
     */
    private updateMenuItems(): void {
        this.store.select(selectIssueCountersState).pipe(
            switchMap(counters => {
                const currentPath = this.getCurrentPath();
                const matchedRoute = this.findMatchingRoute(this.router.config, currentPath);

                let menuItems = matchedRoute?.children
                    ? this.getRoutesWithNames(matchedRoute.children)
                        .filter((item: MenuItem) => item.topMenuName)
                    : [];

                menuItems = menuItems
                    .filter((item: MenuItem) =>
                        !(item.name === 'chatInternalManagement' && !this.chatInternalState)
                    )
                    .map((item: MenuItem) => {
                        const counterValue = counters.issueCounters[item.name];
                        const chatInternalPath = item.chatInternalPath ? `chat/internal/thread/${this.chatInternalId}` : undefined;

                        return {
                            ...item,
                            chatInternalPath: chatInternalPath,
                            counter: counterValue !== undefined ? counterValue : 0,
                        };
                    });

                this.menuItemsSubject$.next(menuItems);

                return of(menuItems);
            })
        ).subscribe();
    }

    /**
     * Pobiera bieżącą ścieżkę nawigacji.
     */
    private getCurrentPath(): string {
        const urlTree = this.router.parseUrl(this.router.url);
        const segments = urlTree.root.children['primary']?.segments.map(segment => segment.path) || [];
        return '/' + segments.join('/');
    }

    /**
     * Znajduje trasę odpowiadającą bieżącej ścieżce.
     * @param routes Lista dostępnych tras.
     * @param path Aktualna ścieżka.
     */
    private findMatchingRoute(routes: Route[], path: string): Route | undefined {
        const normalizedPath = path.endsWith('/') ? path.slice(0, -1) : path;

        return routes.find(route => {
            const routePath = route.path ? `/${route.path}` : '';
            return normalizedPath === routePath || normalizedPath.startsWith(routePath + '/');
        });
    }

    /**
     * Aktualizuje flagę `showMailButtons` na podstawie aktualnej trasy.
     */
    private updateShowMailButtons(): void {
        const currentPath = this.getCurrentPath();
        const isMailRoute = currentPath.startsWith('/mail');
        this.showMailButtonsSubject$.next(isMailRoute);
    }

    /**
     * Tworzy listę elementów menu na podstawie zdefiniowanych tras.
     * @param routes Lista tras.
     */
    private getRoutesWithNames(routes: Route[]): MenuItem[] {
        const urlTree = this.router.parseUrl(this.router.url);
        const segments = urlTree.root.children['primary']?.segments.map(segment => segment.path) || [];

        return routes
            .filter(route => route.data?.name)
            .map(route => ({
                path: `${segments[0]}/${route.path}`.replace(/\/+/g, '/'),
                name: route.data.name,
                topMenuName: route.data.topMenuName,
                permission: route.data.permission,
                chatInternalPath: route.data.chatInternalPath
            }));
    }

    /**
     * Pobiera identyfikator pierwszego wątku czatu wewnętrznego.
     * Aktualizuje właściwość chatInternalId.
     */
    private getChatInternalPath(): void {
        this.threadsInternalService.getThreads().subscribe(chatInternalThread => {
            this.chatInternalId = chatInternalThread[0]?.id;
        });
    }

    /**
     * Publikuje akcję wywołaną w menu.
     * @param action Akcja do opublikowania.
     */
    public emitAction(action: TopMenuAction) {
        this.topMenuActionsSubject.next(action);
    }

    setSelectedMode(isEnabled: boolean): void {
        this.selectedModeSubject.next(isEnabled);
    }
}
