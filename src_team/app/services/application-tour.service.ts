import {Injectable} from '@angular/core';
import {TourService} from 'ngx-ui-tour-md-menu';
import {TranslateService} from '@ngx-translate/core';
import {BehaviorSubject} from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ApplicationTourService {

    constructor(
        private tourService: TourService,
        private translate: TranslateService
    ) {
    }

    activeTourName: BehaviorSubject<string> = new BehaviorSubject<string>('empty');

    tourConfiguration = {
        prevBtnTitle: this.translate.instant('TOUR.PREVIOUS'),
        nextBtnTitle: this.translate.instant('TOUR.NEXT'),
        endBtnTitle: this.translate.instant('TOUR.END'),
        closeOnOutsideClick: true
    }

    issueSidebarTourSteps = [
        {
            stepId: 'menu-sidebar.case-management',
            anchorId: 'menu-sidebar.case-management',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.CASE-MANAGEMENT-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.CASE-MANAGEMENT-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.unassigned',
            anchorId: 'menu-sidebar.unassigned',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.UNASSIGNED-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.UNASSIGNED-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.unaccepted',
            anchorId: 'menu-sidebar.unaccepted',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.UNACCEPTED-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.UNACCEPTED-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.my',
            anchorId: 'menu-sidebar.my',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.MY-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.MY-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.delegated',
            anchorId: 'menu-sidebar.delegated',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.DELEGATED-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.DELEGATED-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.sent',
            anchorId: 'menu-sidebar.sent',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.SENT-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.SENT-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.archives',
            anchorId: 'menu-sidebar.archives',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.ARCHIVES-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.ARCHIVES-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.complaints',
            anchorId: 'menu-sidebar.complaints',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.COMPLAINTS-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.COMPLAINTS-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.add',
            anchorId: 'menu-sidebar.add',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.ADD-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.ADD-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        }
    ];

    chatSidebarTourSteps = [
        {
            stepId: 'menu-sidebar.chat',
            anchorId: 'menu-sidebar.chat',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.CHAT-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.CHAT-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.external-chat',
            anchorId: 'menu-sidebar.external-chat',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.CHAT-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.CHAT-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.internal-chat',
            anchorId: 'menu-sidebar.internal-chat',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.CHAT-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.CHAT-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        }
    ];

    pollSidebarTourSteps = [
        {
            stepId: 'menu-sidebar.polls',
            anchorId: 'menu-sidebar.polls',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.POLL-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.POLL-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.user-poll',
            anchorId: 'menu-sidebar.user-poll',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.POLL-USER-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.POLL-USER-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        }
    ];

    mailSidebarTourSteps = [
        {
            stepId: 'menu-sidebar.mail.name',
            anchorId: 'menu-sidebar.mail.name',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.MAILS-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.MAILS-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        }
    ];

    lastTourStep = [
        {
            stepId: 'header.tour',
            anchorId: 'header.tour', // ostatni krok musi zawsze być dostępny dla wszystkich użytkowników
            title: this.translate.instant('TOUR.HEADER.TOUR-TITLE'),
            content: this.translate.instant('TOUR.HEADER.TOUR-CONTENT'),
            enableBackdrop: true
        }
    ];

    defaultTourSteps = [
        {
            stepId: 'menu-sidebar.dashboard',
            anchorId: 'menu-sidebar.dashboard',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.DASHBOARD-TITLE'),
            content: `
                <p>
                    ${this.translate.instant('TOUR.MENU-SIDEBAR.DASHBOARD-CONTENT.PART1')}
                    <br>
                    ${this.translate.instant('TOUR.MENU-SIDEBAR.DASHBOARD-CONTENT.PART2')}
                </p>
                <ul class="mt-6">
                    <li class="mt-2">${this.translate.instant('TOUR.MENU-SIDEBAR.DASHBOARD-CONTENT.PART3')}</li>
                    <li class="mt-2">${this.translate.instant('TOUR.MENU-SIDEBAR.DASHBOARD-CONTENT.PART4')}</li>
                    <li class="mt-2">${this.translate.instant('TOUR.MENU-SIDEBAR.DASHBOARD-CONTENT.PART5')}</li>
                    <li class="mt-2">${this.translate.instant('TOUR.MENU-SIDEBAR.DASHBOARD-CONTENT.PART6')}</li>
                </ul>
            `,
            enableBackdrop: true,
            isOptional: true
        },
        ...this.mailSidebarTourSteps,
        {
            stepId: 'menu-sidebar.clients',
            anchorId: 'menu-sidebar.clients',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.CLIENTS-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.CLIENTS-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        ...this.issueSidebarTourSteps,
        ...this.chatSidebarTourSteps,
        ...this.pollSidebarTourSteps,
        {
            stepId: 'menu-sidebar.users',
            anchorId: 'menu-sidebar.users',
            title: this.translate.instant('TOUR.SETTINGS.EMPLOYEES-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.EMPLOYEES-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.calendar',
            anchorId: 'menu-sidebar.calendar',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.SCHEDULE-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.SCHEDULE-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'menu-sidebar.reports',
            anchorId: 'menu-sidebar.reports',
            title: this.translate.instant('TOUR.MENU-SIDEBAR.REPORTS-TITLE'),
            content: this.translate.instant('TOUR.MENU-SIDEBAR.REPORTS-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        // {
        //     stepId: 'menu-sidebar.settings',
        //     anchorId: 'menu-sidebar.settings',
        //     title: this.translate.instant('TOUR.MENU-SIDEBAR.SETTINGS-TITLE'),
        //     content: this.translate.instant('TOUR.MENU-SIDEBAR.SETTINGS-CONTENT'),
        //     enableBackdrop: true
        // },
        {
            stepId: 'menu-sidebar.widgets',
            anchorId: 'menu-sidebar.widgets',
            title: this.translate.instant('TOUR.SETTINGS.WIDGETS-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.WIDGETS-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.company',
            anchorId: 'settings.company',
            title: this.translate.instant('TOUR.SETTINGS.COMPANY-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.COMPANY-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.employees',
            anchorId: 'settings.employees',
            title: this.translate.instant('TOUR.SETTINGS.EMPLOYEES-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.EMPLOYEES-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.upgrade-employees',
            anchorId: 'settings.upgrade-employees',
            title: this.translate.instant('TOUR.SETTINGS.EMPLOYEES-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.EMPLOYEES-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.groups',
            anchorId: 'settings.groups',
            title: this.translate.instant('TOUR.SETTINGS.GROUPS-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.GROUPS-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.templates',
            anchorId: 'settings.templates',
            title: this.translate.instant('TOUR.SETTINGS.TEMPLATES-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.TEMPLATES-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.tags',
            anchorId: 'settings.tags',
            title: this.translate.instant('TOUR.SETTINGS.TAGS-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.TAGS-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.categories',
            anchorId: 'settings.categories',
            title: this.translate.instant('TOUR.SETTINGS.CATEGORIES-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.CATEGORIES-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.integrations',
            anchorId: 'settings.integrations',
            title: this.translate.instant('TOUR.SETTINGS.INTEGRATIONS-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.INTEGRATIONS-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.messages',
            anchorId: 'settings.messages',
            title: this.translate.instant('TOUR.SETTINGS.MESSAGES-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.MESSAGES-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'settings.email-integration',
            anchorId: 'settings.email-integration',
            title: this.translate.instant('TOUR.SETTINGS.MAIL-TITLE'),
            content: this.translate.instant('TOUR.SETTINGS.MAIL-CONTENT'),
            enableBackdrop: true,
            isOptional: true
        },
        {
            stepId: 'header.avatar',
            anchorId: 'header.avatar',
            title: this.translate.instant('TOUR.HEADER.AVATAR-TITLE'),
            content: `
                <p>
                    ${this.translate.instant('TOUR.HEADER.AVATAR-CONTENT.PART1')}
                </p>
                <ul class="mt-6">
                    <li class="mt-2"><strong>${this.translate.instant('TOUR.HEADER.AVATAR-CONTENT.PART2')}</strong> ${this.translate.instant('TOUR.HEADER.AVATAR-CONTENT.PART3')}</li>
                    <li class="mt-2"><strong>${this.translate.instant('TOUR.HEADER.AVATAR-CONTENT.PART4')}</strong> ${this.translate.instant('TOUR.HEADER.AVATAR-CONTENT.PART5')}</li>
                    <li class="mt-2">${this.translate.instant('TOUR.HEADER.AVATAR-CONTENT.PART6')} <strong>${this.translate.instant('TOUR.HEADER.AVATAR-CONTENT.PART7')}</strong></li>
                </ul>
            `,
            enableBackdrop: true
        },
        {
            stepId: 'header.tour',
            anchorId: 'header.tour', // ostatni krok musi zawsze być dostępny dla wszystkich użytkowników
            title: this.translate.instant('TOUR.HEADER.TOUR-TITLE'),
            content: this.translate.instant('TOUR.HEADER.TOUR-CONTENT'),
            route: '/start',
            enableBackdrop: true
        }
    ];

    formTourSteps = [
        {
            stepId: 'form.preview',
            anchorId: 'form.preview',
            title: this.translate.instant('TOUR.FORM.PREVIEW-TITLE'),
            content: this.translate.instant('TOUR.FORM.PREVIEW-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'form.edit',
            anchorId: 'form.edit',
            title: this.translate.instant('TOUR.FORM.EDIT-TITLE'),
            content: this.translate.instant('TOUR.FORM.EDIT-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'form.save',
            anchorId: 'form.save',
            title: this.translate.instant('TOUR.FORM.SAVE-TITLE'),
            content: this.translate.instant('TOUR.FORM.SAVE-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'form.script',
            anchorId: 'form.script',
            title: this.translate.instant('TOUR.FORM.SCRIPT-TITLE'),
            content: this.translate.instant('TOUR.FORM.SCRIPT-CONTENT'),
            enableBackdrop: true
        }
    ];

    chatTourSteps = [
        {
            stepId: 'chat.preview',
            anchorId: 'chat.preview',
            title: this.translate.instant('TOUR.CHAT.PREVIEW-TITLE'),
            content: this.translate.instant('TOUR.CHAT.PREVIEW-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'chat.edit',
            anchorId: 'chat.edit',
            title: this.translate.instant('TOUR.CHAT.EDIT-TITLE'),
            content: this.translate.instant('TOUR.CHAT.EDIT-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'chat.save',
            anchorId: 'chat.save',
            title: this.translate.instant('TOUR.CHAT.SAVE-TITLE'),
            content: this.translate.instant('TOUR.CHAT.SAVE-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'chat.script',
            anchorId: 'chat.script',
            title: this.translate.instant('TOUR.CHAT.SCRIPT-TITLE'),
            content: this.translate.instant('TOUR.CHAT.SCRIPT-CONTENT'),
            enableBackdrop: true
        }
    ];

    pollTourSteps = [
        {
            stepId: 'poll.preview',
            anchorId: 'poll.preview',
            title: this.translate.instant('TOUR.POLL.PREVIEW-TITLE'),
            content: this.translate.instant('TOUR.POLL.PREVIEW-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'poll.edit',
            anchorId: 'poll.edit',
            title: this.translate.instant('TOUR.POLL.EDIT-TITLE'),
            content: this.translate.instant('TOUR.POLL.EDIT-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'poll.add-section',
            anchorId: 'poll.add-section',
            title: this.translate.instant('TOUR.POLL.ADD-SECTION-TITLE'),
            content: this.translate.instant('TOUR.POLL.ADD-SECTION-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'poll.report',
            anchorId: 'poll.report',
            title: this.translate.instant('TOUR.POLL.REPORT-TITLE'),
            content: this.translate.instant('TOUR.POLL.REPORT-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'poll.settings',
            anchorId: 'poll.settings',
            title: this.translate.instant('TOUR.POLL.SETTINGS-TITLE'),
            content: this.translate.instant('TOUR.POLL.SETTINGS-CONTENT'),
            enableBackdrop: true
        },
        {
            stepId: 'poll.share',
            anchorId: 'poll.share',
            title: this.translate.instant('TOUR.POLL.SHARE-TITLE'),
            content: this.translate.instant('TOUR.POLL.SHARE-CONTENT'),
            enableBackdrop: true
        },
    ];

    initTour() {
        this.activeTourName.next('default');
        this.tourService.initialize(this.defaultTourSteps, this.tourConfiguration);
    }

    initFormTour() {
        this.activeTourName.next('form');
        this.tourService.initialize([...this.formTourSteps, ...this.issueSidebarTourSteps, ...this.lastTourStep], this.tourConfiguration);
    }

    initChatTour() {
        this.activeTourName.next('chat');
        this.tourService.initialize([...this.chatTourSteps, ...this.chatSidebarTourSteps, ...this.lastTourStep], this.tourConfiguration);
    }

    initPollTour() {
        this.activeTourName.next('poll');
        this.tourService.initialize([...this.pollTourSteps, ...this.pollSidebarTourSteps, ...this.lastTourStep], this.tourConfiguration);
    }

}
