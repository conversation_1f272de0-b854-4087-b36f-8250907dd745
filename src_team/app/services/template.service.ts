import {Injectable} from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {TemplateInterface} from '../common/interfaces/template.interface';
import {EnvironmentService} from './environment.service';

export interface AddTemplateInterface {
    template_group_id: number;
    name: string;
    body: string;
    keywords?: string;
}

@Injectable({
    providedIn: 'root'
})
export class TemplateService {
    private environment: any;

    constructor(private http: HttpClient, private environmentService: EnvironmentService) {
        this.environment = this.environmentService.selectedEnvironment;
    }

    addTemplate(data: AddTemplateInterface): Observable<any> {
        return this.http.post(this.environment.apiUrl + 'template/', JSON.stringify({Template: data}));
    }

    updateTemplate(templateId: number, data): Observable<any> {
        return this.http.put(this.environment.apiUrl + 'template/' + templateId, JSON.stringify({Template: data}));
    }

    deleteTemplate(templateId: number): Observable<any> {
        return this.http.delete(this.environment.apiUrl + 'template/' + templateId);
    }

    getTemplateByGroupId(groupId: number): Observable<[TemplateInterface]> {
        return this.http.get(this.environment.apiUrl + 'template/?template_group_id=' + groupId + '&limit=500&order=sequence|ASC').pipe(
            map((res: any) => res.results.map((element) => element.Template))
        );
    }

    deleteAttachment(commonFileObjectId): Observable<any> {
        return this.http.delete(this.environment.apiUrl + 'common_file_object/' + commonFileObjectId);
    }

    getAllAttachmentsByTemplateId(templateId): Observable<any> {
        return this.http.get(
            this.environment.apiUrl
                + 'common_file/common_file_object?CommonFileObject_object_name=Template&CommonFileObject_object_id='
                + templateId
        );
    }

    getCommonFileObjectsByTemplateId(templateId): Observable<any> {
        return this.http.get(this.environment.apiUrl + 'common_file_object/?object_name=Template&object_id=' + templateId)
            .pipe(map((res: any) => res.results));
    }
}
