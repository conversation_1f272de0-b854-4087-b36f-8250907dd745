import {Injectable} from '@angular/core';
import * as SocketIOClient from 'socket.io-client';
import {Subject} from 'rxjs';
import {IssueInitiatorService} from '../issue-initiator.service';
import Socket = SocketIOClient.Socket;

@Injectable({
    providedIn: 'root'
})
export class SocketCommentService {

    private _socket: Socket;

    /**
     * przypisanie subjecta do przekazu zmian na paśmie kanału.
     * @private
     */
    private _commentsSubject = new Subject<Object>();

    /**
     * odpalenie subskrybcji na zmiany zachodzące na kanale.
     */
    public getComments = this._commentsSubject.asObservable();

    private _userId: number;


    constructor(private _issueInitiatorService: IssueInitiatorService) {
    }

    /**
     * inicjalizacja pakietu komentarzy w sockecie
     * socket.service.ts
     * @param userId
     * @param socket
     */
    public initialize(userId: number, socket: Socket) {
        this._socket = socket;
        this._userId = userId;
        this._join();
    }

    /**
     * odpalenie nasłuchiwania na zmiany zachodzące na kanale "changeComment"
     * @private
     */
    private _join(): void {
        if (this._socket) {
            this._socket.emit('joinCommentSentencesRoom', this._userId);
            this._socket.on('changeComment', data => {
                const _data = JSON.parse(data);
                this._commentsSubject.next(_data);
            });
        }
    }

    public updateComments(data): void {
        this._commentsSubject.next(data);
    }

    /**
     * czyszczenie kanału i nasłuchiwania
     */
    public leave(): void {
        if (this._socket) {
            this._socket.emit('leaveComment');
            this._socket.off('changeComment');
        }
    }


}
