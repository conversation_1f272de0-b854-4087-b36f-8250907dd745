import {Injectable} from '@angular/core';
import {BehaviorSubject, interval, Observable, Subscription} from 'rxjs';
import io from 'socket.io-client';
import {SocketIssuesService} from './socket-issues.service';
import {SocketUsersStatusesService} from './socket-users-statuses.service';
import {SocketIssueSentencesService} from './socket-issue-sentences.service';
import {SocketNotificationsService} from './socket-notifications.service';
import {SocketIssueLockService} from './socket-issue-lock.service';
import {AuthService} from '../auth/auth.service';
import {EnvironmentService} from '../environment.service';
import {SocketLivechatWfirmaMessagesService} from './socket-livechat-wfirma-messages.service';
import {TranslateService} from '@ngx-translate/core';
import {SocketCommentService} from './socket-comment.service';
import {SocketChatInternalMessagesService} from './socket-chat-internal-messages.service';
import {UserService} from '../user.service';
import {map} from 'rxjs/operators';
import {SocketMailService} from './socket-mail.service';

@Injectable({
    providedIn: 'root'
})
export class SocketsService {
    connectedSubject: BehaviorSubject<boolean> = new BehaviorSubject(true);
    private environment: any;
    private socket;
    private connectTimerSubscription: Subscription;
    private CONNECTION_ERROR_TIMEOUT = 5000;

    constructor(
        private socketIssuesService: SocketIssuesService,
        private socketUsersStatusesService: SocketUsersStatusesService,
        private socketIssueSentencesService: SocketIssueSentencesService,
        private socketNotificationsService: SocketNotificationsService,
        private authService: AuthService,
        private socketIssueLockService: SocketIssueLockService,
        private livechatWfirmaMessagesService: SocketLivechatWfirmaMessagesService,
        environmentService: EnvironmentService,
        public translate: TranslateService,
        private _socketComment: SocketCommentService,
        private _socketMailService: SocketMailService,
        private userService: UserService,
        private socketChatInternalMessagesService: SocketChatInternalMessagesService
    ) {
        this.environment = environmentService.selectedEnvironment;
        this.setupLoginSubscription();
        this.connectedSubject.next(false);
    }

    get connected$() {
        return this.connectedSubject.asObservable();
    }

    private get userId(): number {
        return this.authService.getUserId();
    }

    private async customerId(): Promise<number> {
        const user = await this.userService.getUser(this.userId).toPromise();

        return user.customer_id;
    }

    private get token() {
        return this.authService.getWsToken();
    }

    private setConnectTimerSubscription() {
        if (this.connectTimerSubscription && !this.connectTimerSubscription.closed) {
            return;
        }

        if (this.socket && this.socket.conected) {
            this.removeConnectTimerSubscription();

            return;
        }

        this.connectedSubject.next(false);

        this.connectTimerSubscription = interval(this.CONNECTION_ERROR_TIMEOUT)
            .subscribe(() => {
                this.connect();
            });
    }

    private removeConnectTimerSubscription() {
        if (this.connectTimerSubscription) {
            this.connectTimerSubscription?.unsubscribe();
        }
    }

    private onErrors() {
        this.socket.on('error', err => {
            console.error(this.translate.instant('SERVICE.ERROR-WEBSOCKET'), err);
            this.setConnectTimerSubscription();
        });

        this.socket.on('connect_timeout', err => {
            console.error(this.translate.instant('SERVICE.TIMEOUT-WEBSOCKET'), err);
            this.setConnectTimerSubscription();
        });

        this.socket.on('connect_error', err => {
            console.error(this.translate.instant('SERVICE.CONNECTION-ERROR-WEBSOCKET'), err);
            this.setConnectTimerSubscription();
        });
    }

    private async onConnect() {
        this.socket.on('connect', async () => {
            if (!this.socket.connected) {
                this.setConnectTimerSubscription();

                return;
            }
            this.removeConnectTimerSubscription();

            const customerId = await this.customerId();

            this.initializeSocketsServices(this.userId, this.socket, customerId);
        });
    }

    private onDisconnect() {
        this.socket.on('disconnect', reason => {
            console.error('Socket disconnected', reason);
            this.connectedSubject.next(false);
            this.setConnectTimerSubscription();
        });
    }

    private initializeSocketsServices(userId: number, socket: SocketIOClient.Socket, customerId: number) {
        this.socketIssuesService.initialize(userId, socket);
        this.socketUsersStatusesService.initialize(userId, socket);
        this.socketIssueSentencesService.initialize(userId, socket);
        this.socketNotificationsService.initialize(userId, socket);
        this.socketIssueLockService.initialize(userId, socket);
        this.livechatWfirmaMessagesService.initialize(userId, socket);
        this.socketChatInternalMessagesService.initialize(userId, socket, customerId);
        this._socketComment.initialize(userId, socket);
        this._socketMailService.initialize(userId, socket);
        this.connectedSubject.next(true);
    }

    private addSocketListeners() {
        this.onConnect();
        this.onErrors();
        this.onDisconnect();
    }

    connect() {
        if (this.socket && this.socket.connected) {
            return;
        }

        if (this.socket) {
            this.socket.close();
        }

        this.socket = io(this.environment.socketsUrl, {
            transports: ['websocket'],
            reconnection: false,
            query: {token: this.token, userId: this.userId}
        });

        this.socket ? this.addSocketListeners() : this.setConnectTimerSubscription();
    }

    disconnect() {
        if (this.socket) {
            this.socket.off('disconnect');
            this.socket.disconnect();
        }
    }

    private setupLoginSubscription() {
        this.authService.isLoggedIn$.pipe().subscribe(isLoggedIn => {
            if (isLoggedIn) {
                this.connect();
            } else {
                this.disconnect();
            }
        });
    }

}
