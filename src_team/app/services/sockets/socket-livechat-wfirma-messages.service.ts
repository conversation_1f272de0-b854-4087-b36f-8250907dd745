import {Injectable} from '@angular/core';
import {Subject} from 'rxjs';
import {SocketMessage} from '../../common/interfaces/socket-message';

@Injectable({
    providedIn: 'root'
})
export class SocketLivechatWfirmaMessagesService {

    private socket;
    private userId: number;
    private threadId: number;
    private messagesSubject = new Subject<SocketMessage>();
    private threadsSubject = new Subject<SocketMessage>();
    private typingMessageSubject: Subject<null> = new Subject();

    messages$ = this.messagesSubject.asObservable();
    threads$ = this.threadsSubject.asObservable();
    typingMessage$ = this.typingMessageSubject.asObservable();

    constructor() {
    }

    initialize(userId: number, socket) {
        this.socket = socket;
        this.userId = userId;
        this.joinAllThreadsChanges();
    }

    joinAllThreadsChanges() {
        if (this.socket) {
            this.socket.emit('joinThreadsChangesRoom');

            this.socket.on('threadChange', data => {
                const threadChanged = JSON.parse(data);

                this.threadsSubject.next(threadChanged);
            });
        }
    }

    join(threadId: number) {
        this.threadId = threadId;

        if (this.socket) {
            this.socket.emit('joinChatMessagesRoom', threadId);

            this.socket.on('newChatMessage', data => {
                const issueSentenceChanged = JSON.parse(data);

                this.messagesSubject.next(issueSentenceChanged);
            });

            this.socket.on('clientTypingMessage', () => {
                this.typingMessageSubject.next(null);
            });
        }
    }

    leave(threadId: number) {
        if (this.socket) {
            this.socket.emit('leaveChatMessagesRoom', threadId);
            this.socket.off('newChatMessage');
            this.socket.off('clientTypingMessage');
            this.threadId = null;
        }
    }

    typing(threadId: number) {
        this.socket.emit('ownerTypingMessage', threadId);
    }
}
