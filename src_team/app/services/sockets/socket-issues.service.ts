import {Injectable} from '@angular/core';
import {Observable, Subject} from 'rxjs';
import {filter, map} from 'rxjs/operators';
import {SocketsChangeType} from '../../common/enums/socket-change-type';
import {SocketChange} from '../../common/classes/socket-change';
import {SocketsUtils} from '../../common/utils/sockets-utils';
import * as _ from 'underscore';
import {SocketCommentService} from './socket-comment.service';
import {ActivatedRoute} from '@angular/router';
// Dostępne statusy ['open', 'new', 'unaccepted', 'sent', 'closed'];

@Injectable({
    providedIn: 'root'
})
export class SocketIssuesService {
    private issuesSubject = new Subject<any>();
    private socket;
    private userId: number;

    constructor(private _commentsSocketService: SocketCommentService,
                private _route: ActivatedRoute) {
    }

    initialize(userId: number, socket) {
        this.socket = socket;
        this.userId = userId;
        this.join();
    }

    join() {
        this.socket.emit('joinIssuesChangesRoom');

        this.socket.on('issueChange', data => {
            if (_.has(JSON.parse(data), 'state')) {
                this._commentsSocketService.updateComments(data);
            } else {
                this.issuesSubject.next(data);
            }
        });
    }

    get issuesObservable() {
        return this.issuesSubject.asObservable().pipe(map(element => JSON.parse(element)));
    }

    get unassignedIssuesObservable(): Observable<SocketChange> {
        const allowedStatusList = ['open', 'new'];

        return this.issuesObservable.pipe(
            filter((element: any) => {
                // Tylko sprawy które są lub były nieprzypisane  i mają lub miały odpowiedni status
                const isOrWasUnassigned = SocketsUtils.isOrWasEqual(element, 'owner_id', '0'),
                    isOrWasAllowedStatus = SocketsUtils.isOrWasInList(element, 'status', allowedStatusList);

                return isOrWasUnassigned && isOrWasAllowedStatus;
            }),
            map((element: any) => {
                // Zmiana ownera
                if (!!element.changeData.owner_id) {
                    return +element.changeData.owner_id === 0
                        ? SocketsUtils.createChangedItem(element, SocketsChangeType.added)
                        : SocketsUtils.createChangedItem(element, SocketsChangeType.removed);
                }

                // Zmiana statusu
                if (element.changeData.status) {
                    const belongsNow = allowedStatusList.includes(element.changeData.status),
                        belongedBefore = allowedStatusList.includes(element.oldData.status);

                    if (belongsNow && belongedBefore) {
                        return SocketsUtils.createChangedItem(element, SocketsChangeType.updated);
                    }

                    return belongsNow
                        ? SocketsUtils.createChangedItem(element, SocketsChangeType.added)
                        : SocketsUtils.createChangedItem(element, SocketsChangeType.removed);
                }

                // Wszelkie inne zmiany
                return SocketsUtils.createChangedItem(element, SocketsChangeType.updated);
            })
        );
    }

    get unacceptedIssuesObservable(): Observable<SocketChange> {
        return this.issuesObservable.pipe(
            filter((element: any) => SocketsUtils.isOrWasEqual(element, 'status', 'unaccepted')),
            map((element: any) => {
                if (element.changeData.status) {
                    return element.changeData.status === 'unaccepted'
                        ? SocketsUtils.createChangedItem(element, SocketsChangeType.added)
                        : SocketsUtils.createChangedItem(element, SocketsChangeType.removed);
                }

                return SocketsUtils.createChangedItem(element, SocketsChangeType.updated);
            })
        );
    }

    get myIssuesObservable(): Observable<SocketChange> {
        const allowedStatusList = ['open', 'new', 'unaccepted'];

        return this.issuesObservable.pipe(
            filter((element: any) => {
                // Tylko sprawy które są lub były danego usera i mają lub miały odpowiedni status
                const isOrWasMyIssue = SocketsUtils.isOrWasEqual(element, 'owner_id', this.userId),
                    isOrWasAllowedStatus = SocketsUtils.isOrWasInList(element, 'status', allowedStatusList);

                return isOrWasMyIssue && isOrWasAllowedStatus;
            }),
            map((element: any) => {
                // Zmiana ownera
                if (!!element.changeData.owner_id) {
                    return this.userId === +element.changeData.owner_id
                        ? SocketsUtils.createChangedItem(element, SocketsChangeType.added)
                        : SocketsUtils.createChangedItem(element, SocketsChangeType.removed);
                }

                // Zmiana statusu
                if (element.changeData.status) {
                    const belongsNow = allowedStatusList.includes(element.changeData.status),
                        belongedBefore = allowedStatusList.includes(element.oldData.status);

                    if (belongsNow && belongedBefore) {
                        return SocketsUtils.createChangedItem(element, SocketsChangeType.updated);
                    }

                    return belongsNow
                        ? SocketsUtils.createChangedItem(element, SocketsChangeType.added)
                        : SocketsUtils.createChangedItem(element, SocketsChangeType.removed);
                }

                // Wszelkie inne zmiany
                return SocketsUtils.createChangedItem(element, SocketsChangeType.updated);
            })
        );
    }

    get delegatedIssuesObservable(): Observable<SocketChange> {
        const allowedStatusList = ['open', 'new'];

        return this.issuesObservable.pipe(
            filter((element: any) => {
                // Tylko sprawy które są lub były oddelegowane i mają lub miały odpowiedni status
                const isOrWasDelegated = +element.oldData.owner_id > 0 || +element.changeData.owner_id > 0,
                    isOrWasAllowedStatus = SocketsUtils.isOrWasInList(element, 'status', allowedStatusList);

                return isOrWasDelegated && isOrWasAllowedStatus;
            }),
            map((element: any) => {
                // Zmiana ownera
                if (!!element.changeData.owner_id) {
                    return +element.changeData.owner_id > 0
                        ? SocketsUtils.createChangedItem(element, SocketsChangeType.added)
                        : SocketsUtils.createChangedItem(element, SocketsChangeType.removed);
                }

                // Zmiana statusu
                if (element.changeData.status) {
                    const belongsNow = allowedStatusList.includes(element.changeData.status);

                    return belongsNow
                        ? SocketsUtils.createChangedItem(element, SocketsChangeType.added)
                        : SocketsUtils.createChangedItem(element, SocketsChangeType.removed);
                }

                // Wszelkie inne zmiany
                return SocketsUtils.createChangedItem(element, SocketsChangeType.updated);
            })
        );
    }

    get sentIssuesObservable(): Observable<SocketChange> {
        return this.issuesObservable.pipe(
            filter((element: any) => SocketsUtils.isOrWasEqual(element, 'status', 'sent')),
            map((element: any) => {
                if (element.changeData.status) {
                    return element.changeData.status === 'sent'
                        ? SocketsUtils.createChangedItem(element, SocketsChangeType.added)
                        : SocketsUtils.createChangedItem(element, SocketsChangeType.removed);
                }

                return SocketsUtils.createChangedItem(element, SocketsChangeType.updated);
            })
        );
    }

    get closedIssuesObservable(): Observable<SocketChange> {
        return this.issuesObservable.pipe(
            filter((element: any) => SocketsUtils.isOrWasEqual(element, 'status', 'closed')),
            map((element: any) => {
                if (element.changeData.status) {
                    return element.changeData.status === 'closed'
                        ? SocketsUtils.createChangedItem(element, SocketsChangeType.added)
                        : SocketsUtils.createChangedItem(element, SocketsChangeType.removed);
                }

                return SocketsUtils.createChangedItem(element, SocketsChangeType.updated);
            })
        );
    }

    get allIssuesObservable(): Observable<SocketChange> {
        return this.issuesObservable.pipe(
            filter((element: any) => element.changeData && !Array.isArray(element.changeData)),
            map((element: any) => SocketsUtils.createChangedItem(element, SocketsChangeType.unknown))
        );
    }
}
