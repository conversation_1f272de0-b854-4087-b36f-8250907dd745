import { ElementRef, EventEmitter, Output, ViewChild, ViewContainerRef, Directive } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { SimpleMenuItem } from '../../common/interfaces/simple-menu.interface';
import { MoreOptionsMenuComponent } from '../more-options-menu/more-options-menu.component';
import { TranslateService } from '@ngx-translate/core';

@Directive()
export abstract class AbstractMoreOptionsRendererComponent implements ICellRendererAngularComp {
    @ViewChild('optionsButton', { static: false }) optionsButton!: ElementRef;
    @Output() refreshList: EventEmitter<any> = new EventEmitter();
    @Output() openDialogDetails: EventEmitter<any> = new EventEmitter();

    moreOptionsMenu: SimpleMenuItem[] = [];
    overlayRef!: OverlayRef;
    priority: number;
    isRead: number;
    params: any;

    protected constructor(
        protected viewContainerRef: ViewContainerRef,
        protected overlay: Overlay,
        protected translate: TranslateService
    ) {}

    agInit(params: any): void {
        this.params = params;
        this.priority = +params.data.priority;
        this.isRead = +params.data.is_read;

        this.updateMoreOptionsMenu();
    }

    abstract updateMoreOptionsMenu(): void;

    refresh(params: any): boolean {
        this.moreOptionsMenu = params.data?.moreOptionsMenu || [];
        return true;
    }

    toggleMenu(event: MouseEvent): void {
        if (this.overlayRef) {
            this.closeMenu();
            return;
        }

        const positionStrategy = this.overlay.position()
            .flexibleConnectedTo(this.optionsButton)
            .withPositions([
                { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top',  offsetX: -162 },
                { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top',  offsetX: -162 }
            ]);

        this.overlayRef = this.overlay.create({
            hasBackdrop: true,
            backdropClass: 'cdk-overlay-transparent-backdrop',
            positionStrategy
        });

        const portal = new ComponentPortal(MoreOptionsMenuComponent, this.viewContainerRef);
        const componentRef = this.overlayRef.attach(portal);
        componentRef.instance.menuItems = this.moreOptionsMenu;
        componentRef.instance.closeMenu.subscribe(() => this.closeMenu());

        this.overlayRef.backdropClick().subscribe(() => this.closeMenu());

        event.stopPropagation();
    }

    closeMenu(): void {
        if (this.overlayRef) {
            this.overlayRef.dispose();
            this.overlayRef = null!;
        }
    }

    clickAnswer(params: any): void {
        params.openDialog(params);
        this.closeMenu();
    }

    protected togglePriority(): void {
        +this.priority ? this.clickDelPriority() : this.clickAddPriority();
    }

    protected abstract clickAddPriority(): void;
    protected abstract clickDelPriority(): void;
}
