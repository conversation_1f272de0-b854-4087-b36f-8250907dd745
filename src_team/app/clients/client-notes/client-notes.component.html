<div class="client-notes">
    <div *ngIf="editedNoteId === -1" class="new-note">
        <ng-container *ngTemplateOutlet="noteEditForm"></ng-container>
    </div>
    <ng-container [ngSwitch]="loadingStatus">
        <ng-container *ngSwitchCase="'loaded'">
            <ng-container *ngIf="notes.length; else noData">
                <ng-container *ngFor="let note of notes">
                    <div *ngIf="note.id !== editedNoteId; else noteEditForm">
                        <div class="d-flex row justify-between pt-20">
                            <div><strong>{{note.title}}</strong> {{note.modified | date }}</div>
                            <div>
                                <button (click)="editNote(note.id)" class="action-btn">
                                    <img src="assets/images/icons/5ways-pencil-icon.svg" alt="{{'CLIENT-NOTES.EDIT-NOTES' | translate}}" width="16" height="16">
                                </button>
                                <button (click)="deleteNote(note.id)" class="action-btn">
                                    <img src="assets/images/icons/5ways-delete-icon.svg" alt="{{'CLIENT-NOTES.DELETE-NOTES' | translate}}" width="16" height="16">
                                </button>
                            </div>
                        </div>
                        <div class="note-text">
                            <div>{{note.note}}</div>
                        </div>
                    </div>
                </ng-container>
            </ng-container>
        </ng-container>
        <div *ngSwitchCase="'loading'" fxLayoutAlign="center center">
            <mat-spinner></mat-spinner>
        </div>
        <div *ngSwitchCase="'error'">{{'CLIENT-NOTES.FETCHING-ERROR' | translate}}</div>
    </ng-container>
</div>

<ng-template #noData><div class="no-data">{{'CLIENT-NOTES.NO-NOTES' | translate}}</div></ng-template>
<ng-template #noteEditForm>
    <mat-card>
        <form novalidate #form="ngForm">
            <mat-form-field appearance="outline">
                <mat-label>{{'CLIENT-NOTES.TITLE-NOTES' | translate}}</mat-label>
                <input #title="ngModel" name="title" matInput [(ngModel)]="editedNote.title" required maxlength="100">
                <mat-error *ngIf="title.invalid && (title.dirty || title.touched)">
                    <div *ngIf="title.errors?.required">
                        {{'CLIENT-DETAILS.FIELD-REQUIRED' | translate}}
                    </div>
                </mat-error>
            </mat-form-field>
            <mat-form-field appearance="outline">
                <mat-label>{{'CLIENT-NOTES.NOTES' | translate}}</mat-label>
                <textarea
                    #note="ngModel"
                    name="note"
                    matInput
                    placeholder="{{'CLIENT-NOTES.NOTES' | translate}}"
                    [(ngModel)]="editedNote.note"
                    [matTextareaAutosize]="true"
                    maxlength="1000"
                    required
                ></textarea>
                <mat-error *ngIf="note.invalid && (note.dirty || note.touched)">
                    <div *ngIf="note.errors?.required">
                        {{'CLIENT-DETAILS.FIELD-REQUIRED' | translate}}
                    </div>
                </mat-error>
            </mat-form-field>
            <div class="note-form-actions" fxLayout fxLayoutAlign="space-between">
                <button mat-button (click)="resetEditedNote()">{{'CLIENT-NOTES.CANCEL-NOTES' | translate}}</button>
                <button mat-button color="primary" (click)="saveNote()" [disabled]="form.invalid">{{'CLIENT-NOTES.SAVE-NOTES' | translate}}</button>
            </div>
        </form>
    </mat-card>
</ng-template>

<app-5ways-button
    class="fixed-bottom ml-20 mb-20"
    [variant]="ButtonVariant.SECONDARY"
    [disabled]="!!editedNoteId"
    (click)="addNote()">
        Dodaj notatkę
</app-5ways-button>
