<div class="clients">

    <app-dialog-details
            [isOpen]="isDetailsOpen"
            [data]="detailsData"
            [model]="detailsModel"
            (isOpenChange)="isDetailsOpen = $event">
    </app-dialog-details>

    <div class="toolbar">
        <div class="d-flex">
            <app-5ways-paginator
                class="mr-10"
                [pageSize]="limit"
                [pageIndex]="page-1"
                [length]="totalClients"
                (page)="onPageEvent($event)"
                fxHide.md fxHide.xs fxHide.sm>
            </app-5ways-paginator>
            <app-5ways-search-input
                [(ngModel)]="clientNameFilter"
                (searchTriggered)="searchByName()"
                placeholder="{{ 'CLIENTS-LIST.SEARCH' | translate }}">
            </app-5ways-search-input>
        </div>
        <div class="d-flex toolbar__filters">
            <app-client-status-filter
                (changeStatus)="onStatusChange($event)"
                [selectedValue]="filters.status.value">
            </app-client-status-filter>
            <app-client-type-filter
                (changeType)="onTypeChange($event)"
                [selectedValue]="filters.type.value">
            </app-client-type-filter>
        </div>
    </div>

    <div class="table-container">
        <div class="table-wrapper" *ngIf="clients?.length">
            <ag-grid-angular
                [rowData]="clients"
                [columnDefs]="columnDefs"
                [defaultColDef]="defaultColDef"
                [rowSelection]="{ mode: 'multiRow' }"
                [getRowStyle]="getRowStyle"
                [gridOptions]="gridOptions"
                (selectionChanged)="onSelectionChanged($event)"
                (sortChanged)="applySort($event)"
                [context]="{ componentParent: this }">
            </ag-grid-angular>
        </div>

        <div *ngIf="clients && clients.length === 0" class="no-data">
            Brak klientów
        </div>
    </div>
</div>
