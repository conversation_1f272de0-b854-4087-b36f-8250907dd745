<div appLucideIcons *ngIf="clientId" class="client-details">

    <mat-tab-group (selectedTabChange)="onTabChange($event)" class="tabs mt-8 ml-20" #tabs animationDuration="0ms">
        <ng-container *ngTemplateOutlet="matTabBodyContent"></ng-container>
        <ng-template #matTabBodyContent>
            <div class="client-details-mat-tab-body-content"></div>
            <div class="client-details-mat-ink-bar"></div>
        </ng-template>
        <mat-tab label="Dane firmy">
            <ng-container *ngIf="status === 'loaded'">
                <div class="client-info-header">
                    <div class="client-header">
                        <div class="d-flex align-center justify-between">
                            <div class="d-flex align-center">
                                <div class="client-name-container">
                                    <span class="client-name" *ngIf="clientData?.name">{{ clientData?.name }}</span>
                                    <span class="client-name" *ngIf="!clientData?.name && (clientData?.last_name || clientData?.first_name)">
                            {{ clientData?.last_name }} {{ clientData?.first_name }}
                        </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="client-info-header__row mt-16">
                        <div class="client-info-section" *ngIf="clientData?.name || clientData?.first_name || clientData?.last_name">
                            <div class="client-info-section__label">
                                <ng-container *ngIf="clientData?.name && clientData?.name !== 'undefined'">{{ 'CLIENT-DETAILS.NAME' | translate }}</ng-container>
                                <ng-container *ngIf="(!clientData?.name || clientData?.name === 'undefined') && (clientData?.first_name || clientData?.last_name)">{{ 'USERS-LIST.LASTNAME-AND-FIRSTNAME' | translate }}</ng-container>
                            </div>
                            <div class="client-info-section__value">
                                <ng-container *ngIf="clientData?.name && clientData?.name !== 'undefined'">{{ clientData?.name }}</ng-container>
                                <ng-container *ngIf="(!clientData?.name || clientData?.name === 'undefined') && (clientData?.first_name || clientData?.last_name)">
                                    <ng-container *ngIf="clientData?.last_name">{{ clientData?.last_name }}</ng-container>
                                    <ng-container *ngIf="clientData?.first_name && clientData?.last_name"> </ng-container>
                                    <ng-container *ngIf="clientData?.first_name"> {{ clientData?.first_name }}</ng-container>
                                </ng-container>
                            </div>
                        </div>

                        <div class="client-info-section" *ngIf="clientData?.street || clientData?.city || clientData?.zip">
                            <div class="client-info-section__label">{{ 'CLIENT-DETAILS.ADDRESS' | translate }}</div>
                            <div class="client-info-section__value">
                                <ng-container *ngIf="clientData?.street">{{ clientData?.street }}</ng-container>
                                <ng-container *ngIf="clientData?.city || clientData?.zip">
                                    <br *ngIf="clientData?.street">
                                    <ng-container *ngIf="clientData?.zip">{{ clientData?.zip }}</ng-container>
                                    <ng-container *ngIf="clientData?.city">
                                        <ng-container *ngIf="clientData?.zip">, </ng-container>
                                        {{ clientData?.city }}
                                    </ng-container>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="clientData" class="d-flex col justify-between client-details__section">
                    <div class="client-details__content">
                        <mat-accordion multi="true">
                            <mat-expansion-panel [expanded]="true">
                                <mat-expansion-panel-header>
                                    <mat-panel-title>
                                        <i-lucide name="id-card" [size]="16" class="client-details-icon" data-lucide="id-card"></i-lucide>
                                        <div class="ml-12"><strong>{{ 'CLIENT-DETAILS.CLIENT-DATA' | translate }}</strong></div>
                                    </mat-panel-title>
                                </mat-expansion-panel-header>
                                <div class="client-info-container">
                                    <div class="client-info-grid">
                                        <div class="client-info-column">
                                            <div class="client-info-item">
                                                <span class="client-info-label">{{ 'CLIENT-DETAILS.FIRSTNAME-LASTNAME' | translate }}: </span>
                                                <strong class="client-info-value">{{ (clientData?.first_name || '') + ' ' + (clientData?.last_name || '') || '-' }}</strong>
                                            </div>
                                            <div class="client-info-item">
                                                <span class="client-info-label">{{ 'CLIENT-DETAILS.E-MAIL' | translate }}: </span>
                                                <strong class="client-info-value">{{ clientData?.email || '-' }}</strong>
                                            </div>
                                            <div class="client-info-item">
                                                <span class="client-info-label">{{ 'CLIENT-DETAILS.WEBSITE' | translate }}: </span>
                                                <strong class="client-info-value">{{ clientData?.web || '-' }}</strong>
                                            </div>
                                        </div>
                                        <div class="client-info-column">
                                            <div class="client-info-item">
                                                <span class="client-info-label">{{ 'CLIENT-DETAILS.TAX-ID' | translate }}: </span>
                                                <strong class="client-info-value">{{ clientData?.nip || '-' }}</strong>
                                            </div>
                                            <div class="client-info-item">
                                                <span class="client-info-label">{{ 'CLIENT-DETAILS.PHONE' | translate }}: </span>
                                                <strong class="client-info-value">{{ clientData?.phone || '-' }}</strong>
                                            </div>
                                            <div class="client-info-item">
                                                <span class="client-info-label">{{ 'CLIENT-DETAILS.CREATED-DATE' | translate }}: </span>
                                                <strong class="client-info-value">{{ clientData?.created || '-' }}</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </mat-expansion-panel>

                            <mat-expansion-panel *ngIf="contactPersons && contactPersons.length > 0" [expanded]="true">
                                <mat-expansion-panel-header>
                                    <mat-panel-title>
                                        <i-lucide name="users" [size]="16" class="client-details-icon" data-lucide="users"></i-lucide>
                                        <div class="ml-12"><strong>{{ 'CLIENT-DETAILS.CONTACTS' | translate }}</strong></div>
                                    </mat-panel-title>
                                </mat-expansion-panel-header>
                                <div class="client-info-container">
                                    <div class="contacts-scrollable-container">
                                        <div class="contact-person-item" *ngFor="let contact of contactPersons">
                                            <div class="contact-info-grid">
                                                <div class="contact-info-column">
                                                    <div class="contact-info-item">
                                                        <span class="contact-info-label">{{ 'CLIENT-DETAILS.FIRSTNAME-LASTNAME' | translate }}: </span>
                                                        <span class="contact-info-value">{{ (contact.first_name || '') + ' ' + (contact.last_name || '') || '-' }}</span>
                                                    </div>
                                                    <div class="contact-info-item" *ngIf="contact.email">
                                                        <span class="contact-info-label">{{ 'CONTACT-PERSONS-DETAILS.EMAIL' | translate }}: </span>
                                                        <span class="contact-info-value">{{ contact.email }}</span>
                                                    </div>
                                                </div>
                                                <div class="contact-info-column">
                                                    <div class="contact-info-item" *ngIf="contact.phone">
                                                        <span class="contact-info-label">{{ 'CONTACT-PERSONS-DETAILS.PHONE' | translate }}: </span>
                                                        <span class="contact-info-value">{{ contact.phone }}</span>
                                                    </div>
                                                    <div class="contact-info-item" *ngIf="contact.fax">
                                                        <span class="contact-info-label">{{ 'CONTACT-PERSONS-DETAILS.FAX' | translate }}: </span>
                                                        <span class="contact-info-value">{{ contact.fax }}</span>
                                                    </div>
                                                    <div class="contact-info-item" *ngIf="contact.description">
                                                        <span class="contact-info-label">{{ 'CONTACT-PERSONS-DETAILS.DESCRIPTION' | translate }}: </span>
                                                        <span class="contact-info-value">{{ contact.description }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </mat-expansion-panel>
                            <mat-expansion-panel *ngFor="let biz of clientBusinesses">
                                <mat-expansion-panel-header>
                                    <mat-panel-title>
                                        <div class="rodo-icon-container">
                                            <mat-icon class="material-icon">security</mat-icon>
                                        </div>
                                        <div class="d-flex row w-100 justify-between">
                                            <div class="ml-12"><strong>{{'CLIENT-DETAILS.COMPANY-DETAILS'| translate}} {{biz.id}}</strong></div>
                                            <div class="date-created">z {{biz?.dateCreated}}</div>
                                        </div>
                                    </mat-panel-title>
                                </mat-expansion-panel-header>
                                <div class="client-info-container">
                                    <ul class="client-info-list">
                                        <li *ngFor="let data of biz.businessData">
                                            <span class="client-info-label">{{ data?.key }}: </span>
                                            <strong class="client-info-value">{{ data?.value }}</strong>
                                        </li>
                                    </ul>
                                </div>
                            </mat-expansion-panel>
                        </mat-accordion>
                    </div>
                    <div class="client-details__footer">
                        <app-5ways-button
                                *ngIf="mailServerId"
                                [iconLeft]="'mail'"
                                [variant]="ButtonVariant.SECONDARY"
                                (click)="openSendMessageDialog()">
                            {{'CLIENT-DETAILS.SEND-EMAIL'| translate}}
                        </app-5ways-button>
                        <app-5ways-button
                                [iconLeft]="'pencil-line'"
                                [variant]="ButtonVariant.SECONDARY"
                                *ngIf="+client.user_client_id > 0"
                                (click)="sendIssue()">
                            {{'CLIENT-DETAILS.SEND-ISSUE'| translate}}
                        </app-5ways-button>
                        <app-5ways-button
                            [iconLeft]="'pencil'"
                            [variant]="ButtonVariant.SECONDARY"
                            *ngIf="permissionService.checkPermission('clientsListManagement')"
                            (click)="editClientData()">
                            {{'CLIENT-DETAILS.EDIT'| translate}}
                        </app-5ways-button>
                        <app-5ways-button
                            appLucideIcons
                            [iconLeft]="'user-plus'"
                            [variant]="ButtonVariant.SECONDARY"
                            (click)="addContactPerson()">
                          {{'CONTACT-PERSONS-DETAILS.ADD-CONTACT'| translate}}
                        </app-5ways-button>
                        <app-tags
                            fxLayout
                            fxHide.lt-sm
                            objectName="issue_initiator"
                            place="client"
                            [tagsIds]="clientData?.tags"
                            [objectId]="clientData?.id">
                        </app-tags>
                    </div>
                </div>
            </ng-container>
        </mat-tab>
        <ng-container *ngIf="isAdmin">
            <mat-tab label="{{'CLIENT-DETAILS.MAIL' | translate}}">
                <div class="client-mails">
                    <div class="client-mails__list">
                        <ng-container *ngIf="+clientData.id">
                            <app-mails-list
                                    [clientId]="clientData.id"
                                    [clientEmail]="clientData.email">
                            </app-mails-list>
                        </ng-container>
                    </div>
                </div>
            </mat-tab>
        </ng-container>
        <mat-tab
            label="{{'CLIENT-DETAILS.CLIENT-ISSUES' | translate}}"
            *ngIf="status === 'loaded' && issues.length">
            <ng-container *ngIf="showIssuesTab && issues; else issuesLoading">
                <app-issue-list
                    [clientId]="+clientData.id"
                    [hideActions]="false"
                    [modal]="true"
                    [visibleFilters]="[]"
                    [userIssuesMode]="true"
                    [disableOpenUser]="true"
                    [showColumns]="clientIssuesShowColumns"
                    [status]="clientIssuesStatuses"
                ></app-issue-list>
            </ng-container>
        </mat-tab>
        <mat-tab *ngIf="status === 'loaded'">
            <ng-template matTabLabel>
                <span
                    [matBadgeHidden]="!notesCount"
                    [matBadge]="notesCount.toString()"
                    matBadgeOverlap="false"
                    matBadgeColor="primary">
                        {{ 'CLIENT-DETAILS.NOTES' | translate }}&nbsp;
                </span>
            </ng-template>
            <app-client-notes
                [clientId]="+clientData.id"
                (refreshCount)="refreshNotesCount($event)">
            </app-client-notes>
        </mat-tab>
        <ng-container *ngIf="isAdmin">
            <mat-tab label="{{'CLIENT-DETAILS.OTHER' | translate}}">
                <div class="terms-container">
                    <div class="terms-content">
                        <ng-container *ngIf="+clientData.id > 0">
                            <div class="section-header mt-24">
                                <h3>{{ 'CLIENT-DETAILS.AGREEMENTS' | translate }}</h3>
                            </div>
                            <div class="terms-wrapper">
                                <app-client-terms
                                        class="client-details-client-terms"
                                        [clientId]="+clientData.id"
                                        [internalTermsAcceptationDate]="clientData.created">
                                    <div class="client-details-client-terms-content"></div>
                                </app-client-terms>
                            </div>

                            <div class="rodo-section mt-24" *ngIf="permissionService.checkPermission('clientsListManagement')">
                                <div class="section-header">
                                    <h3>{{ 'CLIENT-DETAILS.RODO' | translate }}</h3>
                                </div>
                                <div class="d-flex row align-center ml-16" (click)="onReportOpen()" style="cursor: pointer;">
                                    <mat-icon
                                            class="client-rodo-material-icon mr-4"
                                            matTooltip="{{ 'CLIENT-DETAILS.RODO-REPORT' | translate }}">
                                            description
                                    </mat-icon>
                                    <p>{{ 'CLIENT-DETAILS.RODO-REPORT' | translate }}</p>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </div>
            </mat-tab>
        </ng-container>
    </mat-tab-group>

    <div *ngIf="status === 'loading'" fxLayoutAlign="center center" class="spinner">
        <mat-spinner></mat-spinner>
    </div>

    <div *ngIf="status === 'error'">
        {{ 'CLIENT-DETAILS.READ-ERROR' | translate }}
    </div>
</div>

<app-dialog-details
    [(isOpen)]="isDetailsOpen"
    [data]="detailsData"
    [model]="detailsModel">
</app-dialog-details>
