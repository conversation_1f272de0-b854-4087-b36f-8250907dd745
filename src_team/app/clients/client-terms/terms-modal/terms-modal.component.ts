import {Component, Inject} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatButton } from '@angular/material/button';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-terms-modal',
    template: `
    <h2 mat-dialog-title>{{'TERMS-AGREEMENT.AGREEMENT-CONTENT' | translate}}</h2>
    <mat-dialog-content class="data">
        <div [innerHTML]="termsData"></div>
    </mat-dialog-content>
    <mat-dialog-actions class="actions">
        <button mat-button mat-dialog-close>{{'CLIENT-DETAILS.CLOSE-BUTTON' | translate}}</button>
    </mat-dialog-actions>
  `,
    styleUrls: ['./terms-modal.component.scss'],
    imports: [MatDialogTitle, CdkScrollable, MatDialogContent, MatDialogActions, MatButton, MatDialogClose, TranslatePipe]
})
export class TermsModalComponent {

  constructor(@Inject(MAT_DIALOG_DATA) public termsData: string) { }

}
