<div class="card">
    <ng-container *ngIf="usersOnlineMode; else usersOfflineMode">
        <div class="card-title">
            <div class="icon-online"></div>
            <span>{{ 'DASHBOARD.USERS-ONLINE-TITLE' | translate }}</span>
            <span class="users-counter-online">{{ usersOnline.length }}</span>
        </div>

        <div class="card-body">
            <div class="card-body-item" *ngFor="let user of usersOnline">
                {{ user.firstname}} {{ user.lastname }}
            </div>
        </div>
    </ng-container>
    <ng-template #usersOfflineMode>
        <div class="card-title">
            <div class="icon-offline"></div>
            <span>{{ 'DASHBOARD.USERS-OFFLINE-TITLE' | translate }}</span>
            <span class="users-counter-offline">{{ usersOffline.length }}</span>
        </div>

        <div class="card-body">
            <div class="card-body-item" *ngFor="let user of usersOffline">
                {{ user.firstname}} {{ user.lastname }}
            </div>
        </div>
    </ng-template>
</div>
