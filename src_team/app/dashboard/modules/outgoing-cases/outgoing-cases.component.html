<div class="card" *ngIf="isModuleVisible else modulePreview;">
    <div class="card-title">
        <span>{{'DASHBOARD.OUTGOING-CASES-TITLE' | translate}}</span>
    </div>
    <mat-icon type="button" [matMenuTriggerFor]="settingsMenu" class="date-filter" svgIcon="date-filter"></mat-icon>
    <mat-menu #settingsMenu>
        <mat-form-field (click)="$event.stopPropagation()">
            <mat-label>{{'DASHBOARD.CHOOSE-DATE' | translate}}</mat-label>
            <input matInput [matDatepicker]="picker" [(ngModel)]="date" (dateChange)="getIssuesStats()">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>

        <mat-form-field (click)="$event.stopPropagation()">
            <mat-label>{{'DASHBOARD.START-TIME' | translate}}</mat-label>
            <mat-select [(value)]="hourStart">
                <mat-option *ngFor="let hour of hours" [value]="hour" (click)="getIssuesStats()">
                    {{hour}}:00
                </mat-option>
            </mat-select>
        </mat-form-field>

        <mat-form-field (click)="$event.stopPropagation()">
            <mat-label>{{'DASHBOARD.END-TIME' | translate}}</mat-label>
            <mat-select [(value)]="hourStop">
                <mat-option *ngFor="let hour of hours" [value]="hour" (click)="getIssuesStats()">
                    {{hour}}:00
                </mat-option>
            </mat-select>
        </mat-form-field>
    </mat-menu>

    <div class="card-body">
        <ngx-charts-bar-vertical
            [results]="barChartData"
            [scheme]="colorScheme"
            [xAxis]="true"
            [yAxis]="true"
            [yAxisLabel]="false"
            [tooltipDisabled]="false"
            [xAxisTickFormatting]="_dashboardService.xAxisTickFormatting"
            [yAxisTickFormatting]="_dashboardService.yAxisTickFormatting"
            *ngIf="barChartData && barChartData.length > 0; else noDataTemplate"
        >
            <ng-template #tooltipTemplate let-model="model">
                <div class="tooltip">
                    <p>{{'DASHBOARD.NUMBER-OF-ISSUES' | translate}} {{model.value }}</p>
                    <p>{{'DASHBOARD.NUMBER-OF-ISSUES-TIME' | translate}} {{_dashboardService.trimLabel(model.label)}}:00</p>
                </div>
            </ng-template>
        </ngx-charts-bar-vertical>

        <ng-template #noDataTemplate>
            <div class="no-data">
                {{'DASHBOARD.CHART-NO-DATA' | translate}}
            </div>
        </ng-template>
    </div>
</div>

<ng-template #modulePreview>
    <div class="card">
        <div class="card-title">
            <span>{{'DASHBOARD.OUTGOING-CASES-TITLE' | translate}}</span>
        </div>
        <mat-icon type="button" class="date-filter preview" svgIcon="date-filter"></mat-icon>

        <div class="card-body preview">
            <ngx-charts-bar-vertical
                    [results]="barChartDataPreview"
                    [scheme]="colorScheme"
                    [xAxis]="true"
                    [yAxis]="true"
                    [yAxisLabel]="false"
                    [tooltipDisabled]="true"
                    [xAxisTickFormatting]="_dashboardService.xAxisTickFormatting"
                    [yAxisTickFormatting]="_dashboardService.yAxisTickFormatting"
            >
            </ngx-charts-bar-vertical>
        </div>
    </div>
</ng-template>

