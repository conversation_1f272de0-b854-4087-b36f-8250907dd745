.card {
  display: flex;
  flex-direction: column;
  width: 376px;
  height: 230px;
  background-color: #FFFFFF;
  border-radius: 18px;
  position: relative;
}

.date-filter {
  position: absolute;
  width: 30px;
  height: 30px;
  right: 5px;
  top: 2px;
  cursor: pointer;
}

.card-body {
  display: flex;
  flex-direction: row;
  margin: 10px;
}

.card-title {
  display: flex;
  justify-content: center;

  span {
    color: #333132;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-top: 8px;
  }
}

.card-body {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;

  span {
    color: #005FAA;
    font-size: 77px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
}

.preview {
  filter: blur(3px);
  cursor: default;
  user-select: none;
}

@media (max-width: 1919px) {
  .card {
    width: 300px;
    height: 230px;
  }
}

@media (max-width: 1464px) {
  .card {
    width: 260px;
    height: 230px;
  }

  .card-title {
    span {
      font-size: 14px;
    }
  }
}

@media (max-width: 1464px) {
  .card {
    width: 260px;
    height: 230px;
  }

  .card-body-item {
    width: 215px;
  }

  .card-title {
    span {
      font-size: 14px;
    }
  }
}

@media (max-width: 1200px) {
  .card {
    width: 376px;
    height: 230px;
  }

  .card-body-item {
    width: 330px;
  }

  .card-title {
    span {
      font-size: 16px;
    }
  }
}
