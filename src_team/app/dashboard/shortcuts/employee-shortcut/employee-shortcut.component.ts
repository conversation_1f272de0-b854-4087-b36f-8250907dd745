import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {PermissionService} from '../../../services/permission.service';
import {UserAddComponent} from '../../../settings/users/user-add/user-add.component';
import {EmployeesComponent} from '../../../settings/employees/employees.component';
import {UserService} from '../../../services/user.service';
import {Subscription} from 'rxjs';
import {ApplicationStateService} from '../../../services/application-state.service';
import { MatIcon } from '@angular/material/icon';
import { MatTooltip } from '@angular/material/tooltip';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-employee-shortcut',
    templateUrl: './employee-shortcut.component.html',
    styleUrls: ['./employee-shortcut.component.scss'],
    imports: [MatTooltip, TranslatePipe, MatIcon]
})
export class EmployeeShortcutComponent implements OnInit, OnDestroy{
    employeesCounter: number = 0;

    isTeam = this.ApplicationStateService.getValue('customer')?.account_type === 'team';

    private _employeesSubscription: Subscription;

    conditions: Object = {
        banned: 0
    };

    constructor(
        public dialog: MatDialog,
        private _permissionService: PermissionService,
        private _userService: UserService,
        private ApplicationStateService: ApplicationStateService
    ) {}

    addEmployee(): void {
        if (!this.isTeam) {
            this.dialog.open(EmployeesComponent,
                {
                  width: '500px',
                  height: '300px'
                }
            );
        } else {
            this.dialog.open(UserAddComponent, {
                width: '500px',
                data: {userId: 0},
                panelClass: 'full-width-dialog'
            });
        }
    }

    ngOnInit() {
        this._employeesSubscription = this._userService.getUsers(this.conditions)
            .subscribe(data => {
                this.employeesCounter = data.length;
            });
    }

    ngOnDestroy() {
        this._employeesSubscription?.unsubscribe();
    }
}
