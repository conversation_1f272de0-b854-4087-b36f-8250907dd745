<div class="card" (click)="addChat()">
    <div class="card-body">
        <div class="icon-background">
            <div class="icon">
                <mat-icon svgIcon="chat-shortcut"></mat-icon>
                <mat-icon class="icon-plus" svgIcon="chat-plus-icon"></mat-icon>
            </div>
        </div>
        <div class="card-title">
            <span class="chat-title">{{'DASHBOARD.CHAT-ADD-SHORTCUT' | translate}}</span>
        </div>
    </div>
    <div class="counter" matTooltip="{{'DASHBOARD.CHAT-COUNTER' | translate}} {{ chatsCounter }}">
        <span>{{ chatsCounter }}</span>
    </div>
</div>
