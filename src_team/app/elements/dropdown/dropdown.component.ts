import {
    Component, Input, Output, EventEmitter, forwardRef, ViewChild, ElementRef,
    OnInit, OnDestroy, ViewContainerRef, TemplateRef, ViewEncapsulation, EmbeddedViewRef, Renderer2, Inject, NgZone
} from '@angular/core';
import { DOCUMENT, Ng<PERSON>lass, NgIf, NgF<PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { fromEvent, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { DefaultClassDirective, DefaultStyleDirective } from 'ngx-flexible-layout/extended';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-5ways-dropdown',
    styleUrls: ['./dropdown.component.scss'],
    encapsulation: ViewEncapsulation.None,
    template: `
        <div class="fiveways-dropdown" [ngClass]="{paginator: isPaginator, 'full-size': fullSize, 'search-enabled': searchMode}" #dropdownElement>
            <div class="select-container" (click)="toggleDropdown($event)">
                <div class="selected-value" [class.placeholder]="!hasValue()">
                    <ng-container *ngIf="!multiple && !hasValue()">
                        {{ placeholder || translate.instant('ISSUE-LIST-TOOLBAR.SELECT') }}
                    </ng-container>
                    <ng-container *ngIf="!multiple && hasValue()">
                        {{ getSelectedLabel() }}
                    </ng-container>
                    <ng-container *ngIf="multiple">
                        <div class="multi-value" *ngFor="let val of selectedValues">
                            {{ getValueLabel(val) }}
                        </div>
                        <div *ngIf="!selectedValues.length" class="placeholder">
                            {{ placeholder || translate.instant('ISSUE-LIST-TOOLBAR.SELECT') }}
                        </div>
                    </ng-container>
                </div>
                <div class="arrow" [class.open]="isOpen">
                    <mat-icon svgIcon="chevron-down" class="icon"></mat-icon>
                </div>
            </div>
        </div>

        <ng-template #optionsTemplate>
            <div class="dropdown-options-container" #optionsContainer>
                <div *ngIf="searchMode" class="dropdown-search-container">
                    <input
                            type="text"
                            class="dropdown-search-input"
                            [placeholder]="searchPlaceholder || translate.instant('COMMON.SEARCH')"
                            #searchInput
                    />
                </div>

                <div *ngIf="!multiple && !isPaginator"
                     class="dropdown-option dropdown-empty-option"
                     [class.selected]="!hasValue()"
                     (click)="selectOption(null)">
                </div>

                <div *ngIf="filteredOptions.length === 0" class="dropdown-no-results">
                    {{ translate.instant('COMMON.NO-RESULTS') }}
                </div>

                <div *ngFor="let option of filteredOptions"
                     class="dropdown-option"
                     [class.selected]="isSelected(option.value)"
                     (click)="selectOption(option.value, $event)">
                    <ng-container *ngIf="tagMode">
                        <div class="dropdown-tag-preview"
                             [ngStyle]="{'background-color': '#' + option.color_background}">
                        </div>
                    </ng-container>
                    {{ option.viewValue }}
                </div>
            </div>
        </ng-template>
    `,
    providers: [{
        provide: NG_VALUE_ACCESSOR,
        useExisting: forwardRef(() => DropdownComponent),
        multi: true
    }],
    standalone: true,
    imports: [NgClass, DefaultClassDirective, NgIf, NgFor, MatIcon, NgStyle, DefaultStyleDirective]
})
export class DropdownComponent implements OnInit, OnDestroy, ControlValueAccessor {
    @ViewChild('dropdownElement') dropdownElement!: ElementRef;
    @ViewChild('optionsTemplate') optionsTemplate!: TemplateRef<any>;
    @ViewChild('searchInput') searchInput!: ElementRef;
    @ViewChild('optionsContainer') optionsContainer!: ElementRef;

    @Input() options: { value: any; viewValue: string; color_background?: string; }[] = [];
    @Input() placeholder?: string;
    @Input() multiple = false;
    @Input() tagMode = false;
    @Input() fullSize = false;
    @Input() isPaginator = false;
    @Input() searchMode = false;
    @Input() searchPlaceholder?: string;
    @Output() selectionChange = new EventEmitter<any>();
    @Output() search = new EventEmitter<string>();

    private optionsView: EmbeddedViewRef<any> | null = null;
    private searchSubscription: Subscription | null = null;
    private clickOutsideListener: Function | null = null;

    isOpen = false;
    selectedValues: any[] = [];
    selectedValue: any = null;
    disabled = false;
    searchText: string = '';
    filteredOptions: { value: any; viewValue: string; color_background?: string; }[] = [];

    private onChange: (value: any) => void = () => {};
    private onTouched: () => void = () => {};

    constructor(
        public translate: TranslateService,
        private viewContainerRef: ViewContainerRef,
        private renderer: Renderer2,
        private zone: NgZone,
        @Inject(DOCUMENT) private document: Document
    ) {}

    ngOnInit() {
        this.filteredOptions = [...(this.options ?? [])];
    }

    toggleDropdown(event: MouseEvent) {
        event.stopPropagation();

        if (this.disabled) {
            return;
        }

        if (this.isOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
        this.onTouched();
    }

    openDropdown() {
        this.isOpen = true;

        if (!this.options) {
            this.options = [];
        }

        this.filteredOptions = [...this.options];
        this.searchText = '';

        this.optionsView = this.viewContainerRef.createEmbeddedView(this.optionsTemplate);
        this.optionsView.detectChanges();

        const optionsElement = this.optionsView.rootNodes[0] as HTMLElement;

        this.renderer.appendChild(this.document.body, optionsElement);

        const dropdownRect = this.dropdownElement.nativeElement.getBoundingClientRect();

        optionsElement.style.position = 'fixed';
        optionsElement.style.left = `${dropdownRect.left}px`;
        optionsElement.style.width = `${dropdownRect.width}px`;

        const spaceBelow = window.innerHeight - dropdownRect.bottom;
        const containerHeight = Math.min(250, optionsElement.scrollHeight);

        optionsElement.style.top = spaceBelow < containerHeight
            ? `${dropdownRect.top - containerHeight}px`
            : `${dropdownRect.bottom + 4}px`;
        optionsElement.style.zIndex = '2000';

        if (this.searchSubscription) {
            this.searchSubscription?.unsubscribe();
            this.searchSubscription = null;
        }

        this.zone.runOutsideAngular(() => {
            if (this.clickOutsideListener) {
                this.clickOutsideListener();
                this.clickOutsideListener = null;
            }

            this.clickOutsideListener = this.renderer.listen('document', 'mousedown', (event: MouseEvent) => {
                if (!this.isOpen) {
                    return;
                }

                const target = event.target as HTMLElement;
                const dropdownElement = this.dropdownElement.nativeElement;
                const clickedInDropdown = dropdownElement.contains(target);

                let clickedInOptions = false;
                if (this.optionsView) {
                    for (let i = 0; i < this.optionsView.rootNodes.length; i++) {
                        if (this.optionsView.rootNodes[i].contains(target)) {
                            clickedInOptions = true;
                            break;
                        }
                    }
                }

                if (!clickedInDropdown && !clickedInOptions) {
                    this.zone.run(() => this.closeDropdown());
                }
            });
        });

        setTimeout(() => {
            if (this.searchMode && this.searchInput) {
                this.searchInput.nativeElement.focus();

                this.searchSubscription = fromEvent(this.searchInput.nativeElement, 'input')
                    .pipe(
                        map((event: any) => event.target.value),
                        debounceTime(200),
                        distinctUntilChanged()
                    )
                    .subscribe((text: string) => {
                        this.searchText = text;
                        this.filterOptions(text);
                        this.search.emit(text);
                    });
            }
        });
    }

    closeDropdown() {
        this.isOpen = false;

        if (this.searchSubscription) {
            this.searchSubscription?.unsubscribe();
            this.searchSubscription = null;
        }

        if (this.clickOutsideListener) {
            this.clickOutsideListener();
            this.clickOutsideListener = null;
        }

        if (this.optionsView) {
            this.optionsView.rootNodes.forEach((node: HTMLElement) => {
                if (node.parentNode) {
                    this.renderer.removeChild(this.document.body, node);
                }
            });

            this.optionsView.destroy();
            this.optionsView = null;
        }
    }

    filterOptions(searchText: string) {
        if (!searchText || !searchText.trim()) {
            this.filteredOptions = [...this.options];
            return;
        }

        const term = searchText.toLowerCase().trim();

        this.filteredOptions = this.options.filter(option =>
            option.viewValue && option.viewValue.toLowerCase().includes(term)
        );
    }

    selectOption(value: any, event?: MouseEvent) {
        if (event) {
            event.stopPropagation();
        }

        if (this.disabled) {
            return;
        }

        if (this.multiple) {
            const index = this.selectedValues.indexOf(value);

            if (index === -1) {
                this.selectedValues = [...this.selectedValues, value];
            } else {
                this.selectedValues = this.selectedValues.filter(v => v !== value);
            }

            this.onChange(this.selectedValues);
            this.selectionChange.emit(this.selectedValues);
        } else {
            this.selectedValue = value;
            this.onChange(value);
            this.selectionChange.emit(value);
            this.closeDropdown();
        }
    }

    isSelected(value: any): boolean {
        return this.multiple ? this.selectedValues.includes(value) : this.selectedValue === value;
    }

    hasValue(): boolean {
        return this.multiple
            ? this.selectedValues.length > 0
            : (
                this.selectedValue !== null &&
                this.selectedValue !== undefined &&
                this.selectedValue !== -1
            );
    }

    getSelectedLabel(): string {
        if (this.selectedValue === null || this.selectedValue === undefined) {
            return this.placeholder || '';
        }

        const option = this.options.find(opt => opt.value === this.selectedValue);

        return option ? option.viewValue : '';
    }

    getValueLabel(value: any): string {
        if (value === null || value === undefined) {
            return '';
        }
        const option = this.options.find(opt => opt.value === value);
        return option ? option.viewValue : '';
    }

    writeValue(value: any): void {
        if (this.multiple) {
            this.selectedValues = value || [];
        } else {
            this.selectedValue = value;
        }
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.disabled = isDisabled;
    }

    ngOnDestroy() {
        if (this.clickOutsideListener) {
            this.clickOutsideListener();
        }

        if (this.optionsView) {
            this.optionsView.destroy();
            this.optionsView = null;
        }

        if (this.searchSubscription) {
            this.searchSubscription?.unsubscribe();
            this.searchSubscription = null;
        }
    }
}
