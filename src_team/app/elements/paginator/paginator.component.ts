import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges} from '@angular/core';
import {PageEvent} from '@angular/material/paginator';
import { NgIf } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { DropdownComponent } from '../dropdown/dropdown.component';
import { FormsModule } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-5ways-paginator',
    templateUrl: './paginator.component.html',
    styleUrls: ['./paginator.component.scss'],
    imports: [NgIf, MatIcon, DropdownComponent, FormsModule, TranslatePipe]
})
export class PaginatorComponent implements OnInit, OnChanges {
    @Input()
    pageIndex: number = 0;

    @Input()
    length: number = 0;

    @Input()
    pageSize: number = 10;

    @Input()
    showFirstLastButtons: boolean = true;

    @Output() page = new EventEmitter<PageEvent>();

    pageSizeOptions: number[] = [5, 10, 20, 40, 100];

    options = [];
    totalPages: number = 0;
    startIndex: number = 0;
    endIndex: number = 0;

    ngOnInit() {
        this.pageSizeOptions.forEach(page => {
            this.options.push({value: page, viewValue: page})
        })
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.updatePaginationState();
    }

    private updatePaginationState(): void {
        this.totalPages = Math.ceil(this.length / this.pageSize);

        this.startIndex = this.pageIndex * this.pageSize;
        this.endIndex = Math.min(this.startIndex + this.pageSize, this.length);

        this.preventLastPageEmpty();
    }

    onPageSizeChange(newPageSize: number): void {
        const firstItemIndex = this.pageIndex * this.pageSize;
        
        this.pageSize = newPageSize;
        this.pageIndex = Math.floor(firstItemIndex / this.pageSize);

        this.updatePaginationState();
        this.emitPageEvent();
    }

    onPageChange(newPageIndex: number): void {
        this.pageIndex = newPageIndex;
        this.updatePaginationState();
        this.emitPageEvent();
    }

    private emitPageEvent(): void {
        this.page.emit({
            pageIndex: this.pageIndex,
            pageSize: this.pageSize,
            length: this.length
        });
    }

    private preventLastPageEmpty() {
        const isNotTheFirstPage = this.pageIndex > 0;
        const isNotEmpty = this.totalPages > 0;
        const isPageIndexExceed = this.totalPages === this.pageIndex;
        if (isNotTheFirstPage && isNotEmpty && isPageIndexExceed) {
            --this.pageIndex;
            this.startIndex = this.startIndex - this.pageSize;
            this.emitPageEvent();
        }
    }
}
