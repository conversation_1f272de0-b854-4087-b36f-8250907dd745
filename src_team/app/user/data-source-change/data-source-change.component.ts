import {Component, Inject, Input, <PERSON><PERSON>hang<PERSON>, OnDestroy, OnInit, SimpleChanges} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogActions } from '@angular/material/dialog';
import {UserService} from '../../services/user.service';
import {Subscription} from 'rxjs';
import { NgIf } from '@angular/common';
import { MatFormField, MatLabel } from '@angular/material/select';
import { MatInput } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-data-source-change',
    templateUrl: './data-source-change.component.html',
    styleUrls: ['./data-source-change.component.scss'],
    imports: [NgIf, MatDialogTitle, MatFormField, MatLabel, MatInput, FormsModule, MatDialogActions, MatButton, TranslatePipe]
})
export class DataSourceChangeComponent implements OnInit, OnDestroy, OnChanges {

  rodoContent = '';
  clientId = '';
  userClientId = '';
  contentType = '';

  @Input() selectedTabIndex: number;
  @Input() tabLabel: string;
  @Input() inputClientId: string;
  @Input() inputContent: string;
  @Input() inputClientRodoId: number;

  private _addInfoSub: Subscription;
  private _updateInfoSub: Subscription;

  constructor(
      private userService: UserService,
      @Inject(MAT_DIALOG_DATA) public data,
      public dialogRef: MatDialogRef<DataSourceChangeComponent>
  ) { }

  ngOnInit(): void {


    this.assignRodoData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.inputContent) {
      this.assignRodoData();
    }
  }

  ngOnDestroy() {
    if (this._addInfoSub) {
      this._addInfoSub?.unsubscribe();
    }
    if (this._updateInfoSub) {
      this._updateInfoSub?.unsubscribe();
    }
  }

  assignRodoData() {
    this.rodoContent = this.data?.content || this.inputContent || '';
    this.clientId = this.data?.id || this.inputClientRodoId || '';
    this.userClientId = this.data?.userClientId || this.inputClientId || '';
    this.contentType = this.data?.dataIndex || this.selectedTabIndex || 0;
  }

  createRodoObj() {
    return {
      user_id: this.userClientId,
      type: this.contentType,
      content: this.rodoContent
    };
  }

  addOtherInfo() {
    this._addInfoSub = this.userService.addOtherInfo(this.createRodoObj()).subscribe();
  }

  updateOtherInfo() {
    const id = this.clientId;
    this._updateInfoSub = this.userService.updateOtherInfo(id, this.createRodoObj()).subscribe();
  }

  onSaveClick() {
    !this.clientId ? this.addOtherInfo() : this.updateOtherInfo();
    if (!this.selectedTabIndex) {
      this.closeDialog();
    }
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

}
