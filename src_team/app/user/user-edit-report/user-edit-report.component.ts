import {Component, Inject, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {IssueInitiator} from '../../common/interfaces/issue-initiator.interface';
import { MAT_DIALOG_DATA, MatDialog, MatDialogTitle } from '@angular/material/dialog';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {UserService} from '../../services/user.service';
import {DataSourceChangeComponent} from '../data-source-change/data-source-change.component';
import {forkJoin, Subscription} from 'rxjs';
import {tap} from 'rxjs/operators';
import { MatTabGroup, MatTab } from '@angular/material/tabs';
import {PdfExportService} from '../../services/pdf-export.service';
import {environment} from '../../../../src_respondo-client/environments/environment';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription } from '@angular/material/expansion';
import { ChangeHistoryComponent } from '../change-history/change-history.component';

@Component({
    selector: 'app-user-edit-report',
    templateUrl: './user-edit-report.component.html',
    styleUrls: ['./user-edit-report.component.scss'],
    imports: [MatDialogTitle, MatButton, MatIcon, MatTabGroup, MatTab, NgFor, NgIf, MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, ChangeHistoryComponent, DataSourceChangeComponent, DatePipe, TranslatePipe]
})
export class UserEditReportComponent implements OnInit, OnDestroy {

    currentDate: Date = new Date();
    @Input() userEditReportData: IssueInitiator;
    @ViewChild('tabGroup') tabGroup: MatTabGroup;
    private _userDataSub: Subscription;
    panelOpenState: boolean = false;
    changingUserData;
    changingClientData;
    userHistoryChanges;
    clientId: string;
    rodoId: number; // id wpisu z rodo_other_info
    rodoContent: string;
    selectedTabIndex: number = 0;
    rodoTabs = [
        {
            index: 2,
            name: 'share',
            label: this.translate.instant('USER-EDIT-REPORT.SHARING-HISTORY'),
            dataIndex: 1
        },
        {
            index: 3,
            name: 'complaint',
            label: this.translate.instant('USER-EDIT-REPORT.PROTEST-HISTORY'),
            dataIndex: 2
        },
        {
            index: 4,
            name: 'other',
            label: this.translate.instant('USER-EDIT-REPORT.OTHER'),
            dataIndex: 3
        }
    ];

    userData: { [key: string]: any } = {};

    fields = [
        { key: 'first_name', label: 'USER-EDIT-REPORT.FIRST-NAME' },
        { key: 'last_name', label: 'USER-EDIT-REPORT.LAST-NAME' },
        { key: 'name', label: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.COMPANY-NAME' },
        { key: 'nip', label: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.NIP' },
        { key: 'email', label: 'USER-EDIT-REPORT.E-MAIL' },
        { key: 'phone', label: 'USER-EDIT-REPORT.PHONE' },
        { key: 'city', label: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.CITY' },
        { key: 'zip', label: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.ZIP' },
        { key: 'street', label: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.ADDRESS' }
    ];

    constructor(
        private userService: UserService,
        public translate: TranslateService,
        private pdfExportService: PdfExportService,
        private datePipe: DatePipe,
        private dialog: MatDialog,
        @Inject(MAT_DIALOG_DATA) public data: any
    ) {
        this.setClientId();
        this.setUserData();
    }

    ngOnInit(): void {
        if (this.isOpenedFromClients) {
            this.fetchHistoryChanges('IssueInitiator,Contractor');
        }
        if (this.isOpenedFromSettings) {
            this.fetchHistoryChanges('User');
        }
    }

    private setClientId(): void {
        if (this.isOpenedFromClients) {
            this.clientId = this.data.userData.IssueInitiator?.id || '';
        } else if (this.isOpenedFromSettings) {
            this.clientId = this.data.userData.id || '';
        } else {
            this.clientId = '';
        }
    }
    private setUserData(): void {
        const data = this.isOpenedFromClients
            ? this.data.userData.IssueInitiator
            : this.isOpenedFromSettings
                ? this.data.userData
                : null;
        this.userData = data
            ? {
                first_name: data.first_name || data.firstname || null,
                last_name: data.last_name || data.lastname || null,
                name: data.name || null,
                nip: data.nip || null,
                email: data.email || null,
                phone: data.phone || null,
                city: data.city || null,
                zip: data.zip || null,
                street: data.street || null
            }
            : {};
    }

    private assignDataByDataIndex(dataIndex: number, content: any): void {
        switch (dataIndex) {
            case 1:
                this.userData.sharingHistory = content;
                break;
            case 2:
                this.userData.protestHistory = content;
                break;
            case 3:
                this.userData.otherImportantEntries = content;
                break;
            case 4:
                this.userData.changeDataSources = content;
                break;
        }
    }

    ngOnDestroy() {
        if (this._userDataSub) {
            this._userDataSub?.unsubscribe();
        }
    }

    fetchHistoryChanges(type: 'User' | 'IssueInitiator,Contractor'): void {
        this.userService.getUserHistoryChanges(type).subscribe({
            next: value => {
                const filteredResults = value.results.filter(change => {
                    const modificationHistory = change['ModificationHistoryChange'];
                    const afterModification = JSON.parse(modificationHistory.after_modification);
                    if (type !== 'User') {
                        return afterModification.IssueInitiator?.id === this.clientId;
                    }
                    const beforeModification = JSON.parse(modificationHistory.before_modification);
                    const isChanged =
                        beforeModification?.User?.firstname !== afterModification?.User?.firstname ||
                        beforeModification?.User?.lastname !== afterModification?.User?.lastname ||
                        beforeModification?.User?.phone !== afterModification?.User?.phone ||
                        beforeModification?.User?.email !== afterModification?.User?.email ||
                        beforeModification?.User?.mentor_id !== afterModification?.User?.mentor_id;
                    return isChanged && afterModification.User?.id === this.clientId;
                });

                this.userHistoryChanges = { ...value, results: filteredResults };

                filteredResults.forEach(result => {
                    const changingUserId = +result['ModificationHistoryChange']['changing_user_id'];
                    const userId = +result['ModificationHistoryChange']['user_id'];

                    if (changingUserId === userId) {
                        result.changingClientData = this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.CLIENT');
                    } else {
                        this.userService.getUser(changingUserId).subscribe(userData => {
                            result.changingClientData = `${userData.firstname} ${userData.lastname}`;
                        });
                    }
                });
            },
            error: err => console.error(err)
        });
    }

    exportPdf(): void {
        const requests = this.rodoTabs.map(tab => this.fetchTabInfoReport(tab.dataIndex));

        if (!this.rodoTabs.some(tab => tab.dataIndex === 4)) {
            requests.push(this.fetchTabInfoReport(4));
        }

        forkJoin(requests).subscribe({
            next: (responses) => {
                responses.forEach((val, index) => {
                    const tab = this.rodoTabs[index];
                    if (tab) {
                        this.assignDataByDataIndex(tab.dataIndex, val.results[0]?.RodoOtherInfo?.content);
                    } else {
                        this.assignDataByDataIndex(4, val.results[0]?.RodoOtherInfo?.content);
                    }
                });

                const formattedDate = this.datePipe.transform(this.currentDate, 'dd-MM-yyyy');
                const currentDateFormatted = this.translate.instant('USER-EDIT-REPORT.DAILY-REPORT-TITLE-5WAYS', { day: formattedDate });
                const watermarkText = this.translate.instant('ISSUE-VIEW-MENU.ISSUE-PRINT');
                const user = this.userData;
                const userDetails = this.isOpenedFromSettings ? this.createUserDetails(user) : this.createClientDetails(user);
                const dataSource = responses[3]?.results[0]?.RodoOtherInfo?.content || this.translate.instant('USER-EDIT-REPORT.OWN');
                const texts = {
                    title: currentDateFormatted,
                    author: environment.appName,
                    rodo: watermarkText,
                    footer: watermarkText,
                    fileName: currentDateFormatted + '.pdf'
                };

                this.pdfExportService.initPdf(texts);
                this.addTitleAndSource(currentDateFormatted, dataSource);
                this.addUserDetailsTable(userDetails);
                this.addChangeHistoryTables(this.userHistoryChanges.results);
                this.addHistorySection(this.translate.instant('USER-EDIT-REPORT.SHARING-HISTORY'), this.userData.sharingHistory);
                this.addHistorySection(this.translate.instant('USER-EDIT-REPORT.PROTEST-HISTORY'), this.userData.protestHistory);
                this.addHistorySection(this.translate.instant('USER-EDIT-REPORT.OTHER'), this.userData.otherImportantEntries);

                this.pdfExportService.download();
            },
            error: (err) => console.error('Błąd podczas pobierania danych', err)
        });
    }

    private createUserDetails(user): any[] {
        const notEnteredText = this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.DEFAULT-VALUE');

        return [
            { label: this.translate.instant('USER-EDIT-REPORT.FIRST-NAME'), value: user.first_name || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.LAST-NAME'), value: user.last_name || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.E-MAIL'), value: user.email || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.PHONE'), value: user.phone || notEnteredText },
        ];
    }

    private createClientDetails(user: any): any[] {
        const notEnteredText = this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.DEFAULT-VALUE');
        return [
            { label: this.translate.instant('USER-EDIT-REPORT.FIRST-NAME'), value: user.first_name || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.LAST-NAME'), value: user.last_name || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.COMPANY-NAME'), value: user.name || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.NIP'), value: user.nip || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.E-MAIL'), value: user.email || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.PHONE'), value: user.phone || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.CITY'), value: user.city || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.ZIP'), value: user.zip || notEnteredText },
            { label: this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.ADDRESS'), value: user.street || notEnteredText }
        ];
    }

    private addTitleAndSource(currentDateFormatted: string, dataSource: string): void {
        this.pdfExportService.pdf.add({text: currentDateFormatted, style: 'rodo', alignment: 'center'});
        this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(1));

        const dataSourceText = this.translate.instant('USER-EDIT-REPORT.SOURCE-DATA') + ' - ' + dataSource;
        this.pdfExportService.pdf.add({text: dataSourceText, style: 'rodo', alignment: 'center'});
        this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(2));
    }

    private addUserDetailsTable(userDetails: any[]): void {
        const tableBody = [
            [{text: this.translate.instant('USER-EDIT-REPORT.CURRENT-DATA'), colSpan: 2, style: 'tableHeader', alignment: 'center'}, {}],
            ...userDetails.map(detail => [
                {text: detail.label, style: 'tableCell', alignment: 'left'},
                {text: detail.value, style: 'tableCell', alignment: 'right'}
            ])
        ];

        this.pdfExportService.pdf.add({text: this.translate.instant('USER-EDIT-REPORT.ACTUALS-DATA'), style: 'rodo'});
        this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(1));

        this.pdfExportService.pdf.add({
            table: {
                widths: ['50%', '50%'],
                body: tableBody
            },
            layout: {
                fillColor: (rowIndex: number) => {
                    if (rowIndex === 0) {
                        return '#b4b1b1';
                    }
                    return (rowIndex % 2 !== 0) ? '#F5F5F5' : null;
                },
                hLineWidth: () => 0.5,
                vLineWidth: () => 0.5,
                hLineColor: () => '#CCCCCC',
                vLineColor: () => '#CCCCCC'
            }
        });
        this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(1));
    }

    private addChangeHistoryTables(changeHistory: any[]): void {
        this.pdfExportService.pdf.add({text: this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY'), style: 'rodo'});

        if (!changeHistory || changeHistory.length === 0) {
            this.pdfExportService.pdf.add({ text: this.translate.instant('USER-EDIT-REPORT.NO-ENTRIES'), style: 'smallText', alignment: 'left' });
            this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(1));
            return;
        }

        this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(1));

        changeHistory.forEach(change => {
            const beforeModificationData = JSON.parse(change['ModificationHistoryChange']['before_modification']);
            const afterModificationData = JSON.parse(change['ModificationHistoryChange']['after_modification']);
            const notEnteredText = this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.DEFAULT-VALUE');

            const modificationDate = this.datePipe.transform(change['ModificationHistoryChange']['created'], 'dd MMMM yyyy, HH:mm:ss');
            const clientData = change.changingClientData ? change.changingClientData : this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.CLIENT');

            this.pdfExportService.pdf.add({
                text: `${this.translate.instant('USER-EDIT-REPORT.MODIFICATION-DATE')}: ${modificationDate} - ${clientData}`,
                style: 'date',
                alignment: 'left'
            });

            this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(1));

            const comparisonDetails = this.createComparisonDetails(beforeModificationData, afterModificationData, notEnteredText);

            this.pdfExportService.pdf.add({
                table: {
                    widths: ['30%', '35%', '35%'],
                    body: comparisonDetails
                },
                layout: {
                    fillColor: (rowIndex: number) => {
                        if (rowIndex === 0) {
                            return '#b4b1b1';
                        }
                        return (rowIndex % 2 !== 0) ? '#F5F5F5' : null;
                    },
                    hLineWidth: () => 0.5,
                    vLineWidth: () => 0.5,
                    hLineColor: () => '#CCCCCC',
                    vLineColor: () => '#CCCCCC'
                }
            });

            this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(2));
        });
    }

    private createComparisonDetails(beforeData: any, afterData: any, notEnteredText: string): any[] {
        const commonFieldMap = [
            { field: 'first_name', labelKey: 'USER-EDIT-REPORT.FIRST-NAME' },
            { field: 'last_name', labelKey: 'USER-EDIT-REPORT.LAST-NAME' },
            { field: 'email', labelKey: 'USER-EDIT-REPORT.E-MAIL' },
            { field: 'phone', labelKey: 'USER-EDIT-REPORT.PHONE' }
        ];
        const additionalFields = [
            { field: 'name', labelKey: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.COMPANY-NAME' },
            { field: 'nip', labelKey: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.NIP' },
            { field: 'city', labelKey: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.CITY' },
            { field: 'zip', labelKey: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.ZIP' },
            { field: 'street', labelKey: 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.ADDRESS' }
        ];

        const isUserData = this.isOpenedFromSettings;
        const fieldMap = isUserData ? commonFieldMap : [...commonFieldMap, ...additionalFields];
        const tableHeader = [
            { text: this.translate.instant('USER-EDIT-REPORT.FIELD'), style: 'tableHeader', alignment: 'left' },
            { text: this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.OLD-VALUE'), style: 'tableHeader', alignment: 'center' },
            { text: this.translate.instant('USER-EDIT-REPORT.CHANGE-HISTORY-TAB.NEW-VALUE'), style: 'tableHeader', alignment: 'center' }
        ];
        const tableRows = fieldMap.map(field => {
            const label = this.translate.instant(field.labelKey);
            const before = this.getFieldValue(beforeData, field.field, notEnteredText);
            const after = this.getFieldValue(afterData, field.field, notEnteredText);
            return [
                { text: label, style: 'tableCell', alignment: 'left' },
                { text: before, style: 'tableCell', alignment: 'center' },
                { text: after, style: 'tableCell', alignment: 'center' }
            ];
        });
        return [tableHeader, ...tableRows];
    }

    private addHistorySection(title: string, historyData: any[]): void {
        this.pdfExportService.pdf.add({text: this.translate.instant(title), style: 'rodo', alignment: 'left'});

        if (historyData?.length > 0) {
            this.pdfExportService.pdf.add({text: historyData, style: 'tableCell', alignment: 'left'});
            this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(1));
        } else {
            this.pdfExportService.pdf.add({text: this.translate.instant('USER-EDIT-REPORT.NO-ENTRIES'), style: 'smallText', alignment: 'left'});
            this.pdfExportService.pdf.add(this.pdfExportService.pdf.ln(1));
        }
    }

    private getFieldValue(data: any, field: string, notEnteredText: string): string {
        const userFieldMapping = {
            first_name: 'firstname',
            last_name: 'lastname'
        };
        let value = data?.IssueInitiator?.[field] ?? data?.Contractor?.[field];
        if (!value && data?.User) {
            const userField = userFieldMapping[field] || field;
            value = data.User[userField];
        }
        return value !== undefined && value !== null && value !== '' ? value : notEnteredText;
    }

    onTabChange(event: any): void {
        this.selectedTabIndex = event.index;
        this.rodoContent = '';
        this.rodoId = undefined;

        const selectedTab = this.rodoTabs.find(tab => tab.index === this.selectedTabIndex);

        if (selectedTab) {
            this._userDataSub = this.fetchOtherInfo(selectedTab.dataIndex).subscribe();
        }
    }

    onDataSourceChange(): void {
        const userInfoDataSrcIndex = 4;
        this.tabGroup.selectedIndex = 0;
        this._userDataSub = this.fetchOtherInfo(userInfoDataSrcIndex).pipe(
            tap(() => {
                this.dialog.open(DataSourceChangeComponent, {
                    width: '840px',
                    height: '380px',
                    autoFocus: false,
                    panelClass: 'full-width-dialog',
                    disableClose: true,
                    data: {
                        id: this.rodoId,
                        dataIndex: userInfoDataSrcIndex,
                        userClientId: this.clientId,
                        content: this.rodoContent
                    }
                });
            })
        ).subscribe({
            error: (err) => console.error('Błąd podczas pobierania danych', err)
        });
    }

    fetchInfo(userInfoDataSrcIndex: number, assignContent: boolean) {
        return this.userService.getOtherInfo(`?user_id=${this.clientId}&type=${userInfoDataSrcIndex}`).pipe(
            tap(response => {
                if (response.results && response.results.length > 0) {
                    const rodoInfo = response.results[0].RodoOtherInfo;
                    this.rodoId = rodoInfo.id;

                    if (assignContent) {
                        this.rodoContent = rodoInfo.content;
                    }
                }
            })
        );
    }

    fetchOtherInfo(userInfoDataSrcIndex: number) {
        return this.fetchInfo(userInfoDataSrcIndex, true);
    }

    fetchTabInfoReport(userInfoDataSrcIndex: number) {
        return this.fetchInfo(userInfoDataSrcIndex, false);
    }

    get isOpenedFromClients(): boolean {
        return this.data.openedFrom === 'clients';
    }
    get isOpenedFromSettings(): boolean {
        return this.data.openedFrom === 'settings';
    }

}
