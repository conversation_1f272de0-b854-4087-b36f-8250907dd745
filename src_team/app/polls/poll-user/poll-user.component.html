<div class="poll-user">
    <h2 mat-dialog-title>{{ 'POLL-USER.TITLE' | translate }} {{data}}</h2>

    <mat-dialog-content>
        <div class="table-wrapper" *ngIf="pollAnswers?.length">
            <mat-accordion>
                <mat-expansion-panel class="mat-elevation-z0" *ngFor="let pollAnswer of pollAnswers">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <a class="poll-title" (click)="navigateToPoll(pollAnswer.poll_url)">{{ pollAnswer.poll_name }}</a>
                        </mat-panel-title>
                    </mat-expansion-panel-header>

                    <div class="panel-details">
                        <div *ngIf="pollAnswer.firstname">
                            <strong>{{ 'POLL-USER.FIRSTNAME' | translate }}:</strong> {{ pollAnswer.firstname }}
                        </div>
                        <div *ngIf="pollAnswer.lastname">
                            <strong>{{ 'POLL-USER.LASTNAME' | translate }}:</strong> {{ pollAnswer.lastname }}
                        </div>
                        <div *ngIf="pollAnswer.company">
                            <strong>{{ 'POLL-USER.COMPANY' | translate }}:</strong> {{ pollAnswer.company }}
                        </div>
                        <div *ngIf="pollAnswer.phone">
                            <strong>{{ 'POLL-USER.PHONE' | translate }}:</strong> {{ pollAnswer.phone }}
                        </div>
                        <div *ngIf="pollAnswer.created">
                            <strong>{{ 'POLL-USER.CREATED' | translate }}:</strong> {{ pollAnswer.created }}
                        </div>
                    </div>

                </mat-expansion-panel>
            </mat-accordion>
        </div>

        <div *ngIf="pollAnswers && pollAnswers.length === 0" class="no-data-font no-data" fxLayoutAlign="center center">
            {{ 'POLL-USER.NO-RESULTS' | translate }}
        </div>
    </mat-dialog-content>

    <mat-dialog-actions class="actions justify-end">
        <button mat-button [mat-dialog-close]="false">
            {{'POLL-USER.CANCEL' | translate}}
        </button>
    </mat-dialog-actions>
</div>
