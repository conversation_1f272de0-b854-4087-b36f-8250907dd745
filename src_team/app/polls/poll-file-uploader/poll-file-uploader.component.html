<mat-card-content fxLayoutAlign="center">
    <input
        #fileInput
        type="file"
        ngFileSelect
        [options]="options"
        (uploadOutput)="onUploadOutput($event)"
        [uploadInput]="uploadInput"
        style="display: none"
        (click)="$event.target.value=null"
    >
    <ng-container *ngIf="imageSrc || inputCommonFile.common_file_object_id; else noImage">
        <div class="image-container" fxLayout="column" fxLayoutAlign="start center">
            <ng-container>
                <img [src]="imageSrc ? imageSrc : inputCommonFile.image_src" alt="{{ 'POLL-FILE-UPLOADER.IMAGE' | translate }}" class="image-preview" crossorigin="anonymous">
            </ng-container>

            <div *ngIf="this.files.length">
                <button mat-icon-button type="button" color="primary" (click)="startUpload()" matTooltip="{{ 'POLL-FILE-UPLOADER.ACCEPT' | translate }}">
                    <mat-icon>check</mat-icon>
                </button>
                <button mat-icon-button type="button" (click)="removeAllFiles()" matTooltip="{{ 'POLL-FILE-UPLOADER.CANCEL' | translate }}">
                    <mat-icon color="accent">close</mat-icon>
                </button>
            </div>

            <div class="push-top" *ngIf="this.commonFileObjectId">
                <button mat-icon-button type="button" (click)="removeAttachment()" matTooltip="{{ 'POLL-FILE-UPLOADER.DELETE' | translate }}">
                    <mat-icon color="accent">close</mat-icon>
                </button>
            </div>
        </div>
    </ng-container>

    <ng-template #noImage>
        <div class="d-flex col justify-center align-center">
            <button
                    mat-stroked-button
                    class="button-rounded primary add-img-btn"
                    color="primary"
                    type="button"
                    (click)="addFile()">
                <mat-icon>image</mat-icon>
                {{ 'POLL-FILE-UPLOADER.ADD-IMAGE' | translate }}
            </button>
            <span class="text-10 grey-text mt-8"><i>{{ 'POLL-FILE-UPLOADER.FORMAT' | translate }}:{{ availableImgTypes }}. {{ 'POLL-FILE-UPLOADER.MAX-SIZE' | translate }}</i></span>
        </div>
    </ng-template>
</mat-card-content>
