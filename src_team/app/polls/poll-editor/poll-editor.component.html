<div class="poll-editor-container d-flex col pt-10 pb-20 pl-20 pr-20">
    <div class="poll-editor-columns">
        <div class="preview" tourAnchor="poll.preview">
            <div class="header-wrapper" *ngIf="questions.length">
                <ng-container *ngTemplateOutlet="headerPreview; context: { $implicit: getHeader() }"></ng-container>
            </div>
            <ng-container #previewList *ngFor="let question of questions">
                <mat-card *ngIf="question.type !== 'header' && question.type !== 'contact'"
                        [@fadeInOut]
                        class="mat-elevation-z0 question-preview"
                        id="preview-{{question.uuid}}"
                        [ngClass]="{'active': activeQuestionUuid === question.uuid}">
                    <ng-container [ngSwitch]="question.type">
                        <ng-container *ngSwitchCase="'shortQuestion'">
                            <ng-container *ngTemplateOutlet="shortQuestionPreview; context: { $implicit: question }"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'longQuestion'">
                            <ng-container *ngTemplateOutlet="longQuestionPreview; context: { $implicit: question }"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'oneOptionQuestion'">
                            <ng-container *ngTemplateOutlet="oneOptionQuestionPreview; context: { $implicit: question }"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'multipleOptionQuestion'">
                            <ng-container *ngTemplateOutlet="multipleOptionQuestionPreview; context: { $implicit: question }"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'lineScaleQuestion'">
                            <ng-container *ngTemplateOutlet="lineScaleQuestionPreview; context: { $implicit: question }"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'selectQuestion'">
                            <ng-container *ngTemplateOutlet="selectQuestionPreview; context: { $implicit: question }"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'gridOneQuestion'">
                            <ng-container *ngTemplateOutlet="gridOneQuestionPreview; context: { $implicit: question }"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'gridQuestion'">
                            <ng-container *ngTemplateOutlet="gridQuestionPreview; context: { $implicit: question }"></ng-container>
                        </ng-container>
                        <ng-container *ngSwitchCase="'fileSection'">
                            <ng-container *ngTemplateOutlet="fileSectionPreview; context: { $implicit: question }"></ng-container>
                        </ng-container>
                    </ng-container>
                </mat-card>
            </ng-container>

            <ng-container *ngIf="lastSavedCustomTerms?.length > 0">
                <ng-container *ngTemplateOutlet="termsPreview;"></ng-container>
            </ng-container>

            <ng-template #headerPreview let-header>
                <mat-card *ngIf="header.data" class="header mat-elevation-z0">
                    <h2 [ngStyle]="{'font-size': pollStyles.titleFontSize + 'px'}">{{ header.data.title }}</h2>
                    <p>{{ header.data.description }}</p>
                    <p class="validation-label mt-40" aria-label="{{ 'POLL-EDITOR.REQUIRED' | translate }}">* {{ 'POLL-EDITOR.REQUIRED-QUESTION' | translate }}</p>
                </mat-card>
            </ng-template>

            <ng-template #termsPreview let-terms>
                <mat-card class="mt-12 mat-elevation-z0">
                    <div *ngFor="let term of lastSavedCustomTerms" class="ml-12 mb-12">
                        <mat-checkbox color="primary" [labelPosition]="'after'">
                          <span class="ml-12 term-content">
                            {{ term.content | stripHtml }}
                              <span style="color:red">&nbsp;*</span>
                          </span>
                        </mat-checkbox>
                    </div>
                </mat-card>
            </ng-template>

            <ng-template #shortQuestionPreview let-question>
                <h3 class="question-font-weight" [ngStyle]="{'font-size': pollStyles.questionFontSize + 'px'}">{{ question.data.title }}
                    <ng-container *ngIf="question.data.required"><span class="validation-star" aria-label="{{ 'POLL-EDITOR.REQUIRED' | translate }}">*</span></ng-container>
                </h3>
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>{{ 'POLL-EDITOR.ANSWER' | translate }}</mat-label>
                    <textarea disabled matInput></textarea>
                </mat-form-field>
            </ng-template>

            <ng-template #longQuestionPreview let-question>
                <h3 class="question-font-weight" [ngStyle]="{'font-size': pollStyles.questionFontSize + 'px'}">{{ question.data.title }}
                    <ng-container *ngIf="question.data.required"><span class="validation-star" aria-label="{{ 'POLL-EDITOR.REQUIRED' | translate }}">*</span></ng-container>
                </h3>
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>{{ 'POLL-EDITOR.ANSWER' | translate }}</mat-label>
                    <textarea disabled matInput></textarea>
                </mat-form-field>
            </ng-template>

            <ng-template #oneOptionQuestionPreview let-question>
                <h3 class="question-font-weight" [ngStyle]="{'font-size': pollStyles.questionFontSize + 'px'}">{{ question.data.title }}
                    <ng-container *ngIf="question.data.required"><span class="validation-star" aria-label="{{ 'POLL-EDITOR.REQUIRED' | translate }}">*</span></ng-container>
                </h3>
                <div *ngFor="let option of question.data.options; let i = index"
                     class="options-left">
                    <mat-radio-button disabled class="options options-left" color="primary">
                        <span [ngStyle]="{'font-size': pollStyles.answerFontSize + 'px'}" class="option"> {{question.data.options[i].value}}</span>
                    </mat-radio-button>
                </div>
            </ng-template>

            <ng-template #multipleOptionQuestionPreview let-question>
                <h3 class="question-font-weight" [ngStyle]="{'font-size': pollStyles.questionFontSize + 'px'}">{{ question.data.title }}
                    <ng-container *ngIf="question.data.required"><span class="validation-star" aria-label="{{ 'POLL-EDITOR.REQUIRED' | translate }}">*</span></ng-container>
                </h3>
                <div class="options-left" *ngFor="let option of question.data.options; let i = index">
                    <mat-checkbox disabled class="options" color="primary">
                        <span [ngStyle]="{'font-size': pollStyles.answerFontSize + 'px'}" class="option"> {{question.data.options[i].value}}</span>
                    </mat-checkbox>
                </div>
            </ng-template>

            <ng-template #lineScaleQuestionPreview let-question>
                <h3 class="question-font-weight" [ngStyle]="{'font-size': pollStyles.questionFontSize + 'px'}">{{ question.data.title }}
                    <ng-container *ngIf="question.data.required"><span class="validation-star" aria-label="{{ 'POLL-EDITOR.REQUIRED' | translate }}">*</span></ng-container>
                </h3>
                <div class="options-left line-scale">
                    <span [ngStyle]="{'font-size': pollStyles.answerFontSize + 'px'}" class="wrapping line-scale-value-grey"> {{question.data.line_scale[0].label_from.text}}</span>
                    <ng-container class="d-flex flex-column" *ngFor="let number of range(question.data.line_scale[0].label_from.value, question.data.line_scale[1].label_to.value + 1)">
                        <mat-radio-button [disabled]="true" [value]="number"><span [ngStyle]="{'font-size': pollStyles.answerFontSize + 'px'}" class="line-scale-value-grey">{{ number }}</span></mat-radio-button>
                    </ng-container>
                    <span [ngStyle]="{'font-size': pollStyles.answerFontSize + 'px'}" class="line-scale-last wrapping line-scale-value-grey"> {{question.data.line_scale[1].label_to.text}}</span>
                </div>
            </ng-template>

            <ng-template #selectQuestionPreview let-question>
                <h3 class="question-font-weight" [ngStyle]="{'font-size': pollStyles.questionFontSize + 'px'}">{{ question.data.title }}
                    <ng-container *ngIf="question.data.required"><span class="validation-star" aria-label="{{ 'POLL-EDITOR.REQUIRED' | translate }}">*</span></ng-container>
                </h3>
                <mat-form-field class="options-question">
                    <mat-label>{{ 'POLL-EDITOR.ANSWER-QUESTION' | translate }}</mat-label>
                    <mat-select placeholder="typ">
                        <mat-option [disabled]="true" *ngFor="let option of question.data.options; let i = index" [value]="option.value">
                            {{ i + 1 }}. {{ option.value }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </ng-template>

            <ng-template #gridOneQuestionPreview let-question>
                <h3 class="question-title question-font-weight"
                    [ngStyle]="{'font-size': pollStyles.questionFontSize + 'px'}">
                    {{ question.data.title }}
                    <ng-container *ngIf="question.data.required"><span class="validation-star" aria-label="{{ 'POLL-EDITOR.REQUIRED' | translate }}">*</span></ng-container>
                </h3>
                <div class="scrollable-container">
                    <div class="table-wrapper">
                        <div class="table-wrapper__header" *ngIf="question.data.columns.length > 0">
                            <div class="table-wrapper__row">
                                <div class="table-wrapper__cell-data"></div>
                                <div class="table-wrapper__cell" *ngFor="let column of question.data.columns; let j = index"
                                     [ngStyle]="{'font-size': pollStyles.answerFontSize + 'px'}">
                                    {{ column.value }}
                                </div>
                            </div>
                        </div>
                        <div class="table-wrapper__row-group table-wrapper__single-row"
                             *ngFor="let option of question.data.rows; let i = index">
                            <div class="table-wrapper__row-container">
                                <div class="table-wrapper__cell-data">
                                    {{ option.value }}
                                </div>
                                <ng-container *ngFor="let column of question.data.columns; let j = index">
                                    <div class="table-wrapper__cell">
                                        <mat-radio-button
                                                [disabled]="true"
                                                class="grid-question"
                                                color="primary"
                                                [name]="'grid_question' + '_' + i + '_' + question.uuid">
                                        </mat-radio-button>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-template>

            <ng-template #gridQuestionPreview let-question>
                <h3 class="question-title question-font-weight" [ngStyle]="{'font-size': pollStyles.questionFontSize + 'px'}">
                    {{ question.data.title }}
                    <ng-container *ngIf="question.data.required"><span class="validation-star" aria-label="{{ 'POLL-EDITOR.REQUIRED' | translate }}">*</span></ng-container>
                </h3>
                <div class="scrollable-container">
                    <div class="table-wrapper">
                        <div class="table-wrapper__header" *ngIf="question.data.columns.length > 0">
                            <div class="table-wrapper__row">
                                <div class="table-wrapper__cell-data"></div>
                                <div class="table-wrapper__cell" *ngFor="let column of question.data.columns; let j = index"
                                     [ngStyle]="{'font-size': pollStyles.answerFontSize + 'px'}">
                                    {{ column.value }}
                                </div>
                            </div>
                        </div>
                        <div class="table-wrapper__row-group table-wrapper__single-row"
                             *ngFor="let option of question.data.rows; let i = index">
                            <div class="table-wrapper__row-container">
                                <div class="table-wrapper__cell-data">
                                    {{ option.value }}
                                </div>
                                <ng-container *ngFor="let column of question.data.columns; let j = index">
                                    <div class="table-wrapper__cell">
                                        <mat-checkbox
                                                disabled
                                                class="grid-question"
                                                color="primary"
                                                [name]="'grid_question' + '_' + i + '_' + question.uuid">
                                        </mat-checkbox>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-template>

            <ng-template #fileSectionPreview let-question>
                <div class="preview-image-wrapper">
                    <a [href]="question.data.href" target="_blank" *ngIf="question.data.href">
                        <img [src]="question.data.image_src" alt="" crossorigin="anonymous">
                    </a>

                    <img [src]="question.data.image_src" alt="" *ngIf="!question.data.href" crossorigin="anonymous">
                </div>
            </ng-template>
        </div>

        <div class="config">
            <div class="sticky-tabs">
                <mat-tab-group class="mat-elevation-z0" [selectedIndex]="tabIndex" (selectedTabChange)="changeTab($event)">
                    <mat-tab>
                        <ng-template mat-tab-label>
                            <div tourAnchor="poll.edit">{{ 'POLL-EDITOR.EDIT' | translate }}</div>
                        </ng-template>
                        <ng-container *ngFor="let question of questions">
                            <mat-card
                                    *ngIf="question.type !== 'contact'"
                                    class="mat-elevation-z0 question"
                                    id="question-{{question.uuid}}"
                                    [@fadeInOut]
                                    (click)="activeQuestion(question.uuid)"
                            >
                            <ng-container *ngIf="question.type !== 'header' && question.type !== 'fileSection'">
                                <div class="options-question">
                                    <mat-form-field class="question-input" appearance="outline">
                                        <mat-label>{{ 'POLL-EDITOR.QUESTION' | translate }}</mat-label>
                                        <input
                                            matInput
                                            matTooltip="Modyfikacja tytułu sekcji pytań spowoduję utratę wcześniejszych odpowiedzi."
                                            matTooltipPosition="before"
                                            [matTooltipDisabled]="!hasAnswers"
                                            [(ngModel)]="question.data.title"
                                            maxlength="1024"
                                            required
                                        >
                                        <mat-error *ngIf="!question.data.title">{{ 'POLL-EDITOR.QUESTION-TITLE-REQUIRED' | translate }}</mat-error>
                                    </mat-form-field>
                                </div>
                            </ng-container>
                                <ng-container [ngSwitch]="question.type">
                                    <ng-container *ngSwitchCase="'header'">
                                        <ng-container *ngTemplateOutlet="header; context: { $implicit: question }"></ng-container>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'shortQuestion'">
                                        <ng-container *ngTemplateOutlet="shortQuestion; context: { $implicit: question }"></ng-container>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'longQuestion'">
                                        <ng-container *ngTemplateOutlet="longQuestion; context: { $implicit: question }"></ng-container>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'oneOptionQuestion'">
                                        <ng-container *ngTemplateOutlet="oneOptionQuestion; context: { $implicit: question }"></ng-container>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'fileSection'">
                                        <app-poll-file-uploader id="file-question-{{question.uuid}}"
                                                                [widgetId]="widgetId"
                                                                [inputCommonFile]="question.data"
                                                                [uuid] = "question.uuid"
                                                                (uploadedCommonFile)="handleUploadImage($event, question.uuid)"
                                        ></app-poll-file-uploader>
                                        <mat-form-field appearance="outline" class="full-width">
                                            <mat-label>{{ 'POLL-EDITOR.IMAGE-LINK' | translate }}</mat-label>
                                            <input matInput [(ngModel)]="question.data.href">
                                        </mat-form-field>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'multipleOptionQuestion'">
                                        <ng-container *ngTemplateOutlet="multipleOptionQuestion; context: { $implicit: question }"></ng-container>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'lineScaleQuestion'">
                                        <ng-container *ngTemplateOutlet="lineScaleQuestion; context: { $implicit: question }"></ng-container>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'selectQuestion'">
                                        <ng-container *ngTemplateOutlet="selectQuestion; context: { $implicit: question }"></ng-container>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'gridOneQuestion'">
                                        <ng-container *ngTemplateOutlet="gridOneQuestion; context: { $implicit: question }"></ng-container>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'gridQuestion'">
                                        <ng-container *ngTemplateOutlet="gridQuestion; context: { $implicit: question }"></ng-container>
                                    </ng-container>
                                </ng-container>

                                <div class="card-control" *ngIf="question.type !== 'header'">
                                    <div class="left">
                                        <mat-icon [ngClass]="{'disabled': isMoveUpDisabled(question.uuid)}" class="mr-4 move-up" (click)="moveQuestionUp(question.uuid)" matTooltip="{{ 'POLL-EDITOR.MOVE-SECTION-UP' | translate }}">expand_less</mat-icon>
                                        <mat-icon [ngClass]="{'disabled': isMoveDownDisabled(question.uuid)}" class="mr-4 move-down" (click)="moveQuestionDown(question.uuid)" matTooltip="{{ 'POLL-EDITOR.MOVE-SECTION-DOWN' | translate }}">expand_more</mat-icon>
                                    </div>

                                    <div class="right">
                                        <mat-icon class="mr-4" (click)="removeQuestion(question.uuid)" matTooltip="{{ 'POLL-EDITOR.DELETE-SECTION' | translate }}">delete</mat-icon>
                                        <mat-slide-toggle *ngIf="question.type !== 'fileSection'" [(ngModel)]="question.data.required" color="primary" matTooltip="{{ 'POLL-EDITOR.REQUIRED-ANSWER' | translate }}">
                                            <span>{{ 'POLL-EDITOR.REQUIRED' | translate }}</span>
                                        </mat-slide-toggle>
                                    </div>
                                </div>
                            </mat-card>
                        </ng-container>
                        <ng-template #header let-question>
                            <mat-form-field appearance="outline" class="full-width">
                                <mat-label>{{ 'POLL-EDITOR.POLL-TITLE' | translate }}</mat-label>
                                <input matInput [(ngModel)]="question.data.title" [maxLength]="100" required>
                                <mat-error *ngIf="!question.data.title">{{ 'POLL-EDITOR.POLL-TITLE-REQUIRED' | translate }}</mat-error>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="full-width">
                                <mat-label>{{ 'POLL-EDITOR.POLL-DESCRIPTION' | translate }}</mat-label>
                                <textarea matInput [(ngModel)]="question.data.description" maxlength="1024" required></textarea>
                                <mat-error *ngIf="!question.data.description">{{ 'POLL-EDITOR.POLL-DESCRIPTION-REQUIRED' | translate }}</mat-error>
                            </mat-form-field>
                        </ng-template>

                        <ng-template #shortQuestion let-question></ng-template>

                        <ng-template #longQuestion let-question></ng-template>

                        <ng-template #oneOptionQuestion let-question>
                            <div *ngFor="let option of question.data.options; let i = index">
                                <div class="container p-0">
                                    <div class="row appearance hover-close-icon">
                                        <div class="col-8">
                                            <mat-radio-button [disabled]="true" class="options options-radio" color="primary"></mat-radio-button>
                                            <mat-form-field appearance="outline" class="field-option field-option-x field-option-one">
                                                <input id="{{question.uuid}}" #inputField class="question-{{i}}" matInput [(ngModel)]="question.data.options[i].value" [maxlength]="1024" (keydown.enter)="onEnterPressAddQuestion('option', question)" (keydown.backspace)="onBackspacePressDeleteQuestion($event, question, option.uuid, option.value, 'option')">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-4 x-container">
                                            <img class="x-icon" src="../../../assets/images/icons/x-icon.svg" alt="Ikona zamknięcia" width="15" height="15" (click)="removeOption(question, option.uuid)">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="ml-4 field-option field-option-add" (click)="addOption(question)">
                                <mat-icon class="options options-plus" svgIcon="survey-plus-icon"></mat-icon>
                                <p class="field-option-add-option">{{ 'POLL-EDITOR.ADD-OPTION' | translate }}</p>
                            </div>
                        </ng-template>

                        <ng-template #multipleOptionQuestion let-question>
                            <div *ngFor="let option of question.data.options; let i = index">
                                <div class="container p-0">
                                    <div class="row appearance hover-close-icon">
                                        <div class="col-8">
                                            <mat-checkbox [disabled]="true" class="options options-radio" color="primary"></mat-checkbox >
                                            <mat-form-field appearance="outline" class="field-option field-option-x">
                                                <input id="{{question.uuid}}" #inputField matInput value="" [(ngModel)]="question.data.options[i].value" [maxlength]="1024" (keydown.enter)="onEnterPressAddQuestion('option', question)" (keydown.backspace)="onBackspacePressDeleteQuestion($event, question, option.uuid, option.value, 'option')">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-4 x-container">
                                            <img class="x-icon" src="../../../assets/images/icons/x-icon.svg" alt="Ikona zamknięcia" width="15" height="15" (click)="removeOption(question, option.uuid)">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="ml-4 field-option field-option-add" (click)="addOption(question)">
                                <mat-icon class="options options-plus options-plus-multiple" svgIcon="survey-plus-icon"></mat-icon>
                                <p class="field-option-add-option">{{ 'POLL-EDITOR.ADD-OPTION' | translate }}</p>
                            </div>
                        </ng-template>

                        <ng-template #selectQuestion let-question>
                            <div *ngFor="let option of question.data.options; let i = index">
                                <div class="container p-0">
                                    <div class="row appearance hover-close-icon">
                                        <div class="col-8">
                                            <span class="options-numbers">{{i + 1 + '.'}}</span>
                                            <mat-form-field appearance="outline" class="field-option field-option-x field-option-list">
                                                <input id="{{question.uuid}}" #inputField matInput value="" [(ngModel)]="question.data.options[i].value" [maxlength]="1024" (keydown.enter)="onEnterPressAddQuestion('option', question)" (keydown.backspace)="onBackspacePressDeleteQuestion($event, question, option.uuid, option.value, 'option')">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-4 x-container x-container-list">
                                            <img class="x-icon mt-6" src="../../../assets/images/icons/x-icon.svg" alt="Ikona zamknięcia" width="15" height="15" (click)="removeOption(question, option.uuid)">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="ml-4 field-option field-option-add" (click)="addOption(question)">
                                <mat-icon class="options options-plus options-plus-multiple" svgIcon="survey-plus-icon"></mat-icon>
                                <p class="field-option-add-option">{{ 'POLL-EDITOR.ADD-OPTION' | translate }}</p>
                            </div>
                        </ng-template>

                        <ng-template #lineScaleQuestion let-question>
                            <div class="row">
                                <div class="col-12 d-flex">
                                    <mat-form-field class="line-scale-question">
                                        <mat-select [(ngModel)]="question.data.line_scale[0].label_from.value" placeholder="od">
                                            <mat-option *ngFor="let number of [0, 1]" [value]="number">
                                                {{ number }}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <div class="separator"> -</div>
                                    <mat-form-field class="line-scale-question">
                                        <mat-select [(ngModel)]="question.data.line_scale[1].label_to.value" placeholder="do">
                                            <mat-option *ngFor="let number of [2, 3, 4, 5, 6, 7, 8, 9, 10]"
                                                        [value]="number">
                                                {{ number }}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                </div>
                            </div>
                            <div class="row mt-5 line-scale-container">
                                <div class="col-12">
                                    <span class="line-scale-value">{{ question.data.line_scale[0].label_from.value }}</span>
                                    <mat-form-field class="line-scale-field" appearance="outline">
                                        <mat-label>{{ 'POLL-EDITOR.OPTIONAL-LABEL' | translate }}</mat-label>
                                        <input matInput [(ngModel)]="question.data.line_scale[0].label_from.text" [maxlength]="1024">
                                    </mat-form-field>
                                    <br class="break">
                                    <span class="line-scale-value">{{ question.data.line_scale[1].label_to.value }}</span>
                                    <mat-form-field class="line-scale-field" appearance="outline">
                                        <mat-label>{{ 'POLL-EDITOR.OPTIONAL-LABEL' | translate }}</mat-label>
                                        <input matInput [(ngModel)]="question.data.line_scale[1].label_to.text" [maxlength]="1024">
                                    </mat-form-field>
                                </div>
                            </div>
                        </ng-template>

                        <ng-template #gridOneQuestion let-question>
                            <div class="d-flex column-layout">
                                <div class="w-50 full-width">
                                    <span class="d-flex line-scale-value">{{ 'POLL-EDITOR.ROWS' | translate }}</span>
                                    <div *ngFor="let option of question.data.rows; let i = index">
                                        <div class="container p-0">
                                            <div class="row appearance hover-close-icon">
                                                <div class="col-4">
                                                    <span class="options-numbers">{{i + 1 + '.'}}</span>
                                                    <mat-form-field appearance="outline" class="field-option field-option-x field-option-list field-option-grid">
                                                        <input id="{{question.uuid}}" #rowField matInput value="" [(ngModel)]="question.data.rows[i].value" [maxlength]="1024" (keydown.enter)="onEnterPressAddQuestion('row', question)" (keydown.backspace)="onBackspacePressDeleteQuestion($event, question, option.uuid, option.value, 'row')">
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-4 x-container x-container-list x-container-list-grid">
                                                    <img class="x-icon mb-6" src="../../../assets/images/icons/x-icon.svg" alt="Ikona zamknięcia" width="15" height="15" (click)="removeRow(question, option.uuid)">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="ml-4 field-option field-option-add" (click)="addRow(question)">
                                        <mat-icon class="options options-plus options-plus-multiple" svgIcon="survey-plus-icon"></mat-icon>
                                        <p class="field-option-add-option">{{ 'POLL-EDITOR.ADD-OPTION' | translate }}</p>
                                    </div>
                                </div>

                                <div class="w-50 full-width">
                                    <span class="d-flex line-scale-value line-scale-value-column margin-left">{{ 'POLL-EDITOR.COLUMNS' | translate }}</span>
                                    <div *ngFor="let option of question.data.columns; let i = index">
                                        <div class="container p-0">
                                            <div class="row appearance appearance-column margin-left hover-close-icon">
                                                <div class="col-4">
                                                    <span class="options-numbers">{{i + 1 + '.'}}</span>
                                                    <mat-form-field appearance="outline"
                                                                    class="field-option field-option-x field-option-list field-option-grid">
                                                        <input id="{{question.uuid}}" #colField matInput value="" [(ngModel)]="question.data.columns[i].value" [maxlength]="1024" (keydown.enter)="onEnterPressAddQuestion('column', question)" (keydown.backspace)="onBackspacePressDeleteQuestion($event, question, option.uuid, option.value, 'column')">
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-4 x-container x-container-list x-container-list-grid">
                                                    <img class="x-icon mb-6" src="../../../assets/images/icons/x-icon.svg" alt="Ikona zamknięcia" width="15" height="15" (click)="removeColumn(question, option.uuid)">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="ml-4 field-option field-option-add field-option-add-grid margin-left" (click)="addColumn(question)">
                                        <mat-icon class="options options-plus options-plus-multiple" svgIcon="survey-plus-icon"></mat-icon>
                                        <p class="field-option-add-option">{{ 'POLL-EDITOR.ADD-OPTION' | translate }}</p>
                                    </div>
                                </div>
                            </div>
                        </ng-template>


                        <ng-template #gridQuestion let-question>
                            <div class="d-flex column-layout">
                                <div class="w-50 full-width">
                                    <span class="d-flex line-scale-value">{{ 'POLL-EDITOR.ROWS' | translate }}</span>
                                    <div *ngFor="let option of question.data.rows; let i = index">
                                        <div class="container p-0">
                                            <div class="row appearance hover-close-icon">
                                                <div class="col-4">
                                                    <span class="options-numbers">{{i + 1 + '.'}}</span>
                                                    <mat-form-field appearance="outline" class="field-option field-option-x field-option-list field-option-grid">
                                                        <input id="{{question.uuid}}" #rowField matInput value="" [(ngModel)]="question.data.rows[i].value" [maxlength]="1024" (keydown.enter)="onEnterPressAddQuestion('row', question)" (keydown.backspace)="onBackspacePressDeleteQuestion($event, question, option.uuid, option.value, 'row')">
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-4 x-container x-container-list x-container-list-grid">
                                                    <img class="x-icon mb-6" src="../../../assets/images/icons/x-icon.svg" alt="Ikona zamknięcia" width="15" height="15" (click)="removeRow(question, option.uuid)">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="ml-4 field-option field-option-add" (click)="addRow(question)">
                                        <mat-icon class="options options-plus options-plus-multiple" svgIcon="survey-plus-icon"></mat-icon>
                                        <p class="field-option-add-option">{{ 'POLL-EDITOR.ADD-OPTION' | translate }}</p>
                                    </div>
                                </div>

                                <div class="w-50 full-width">
                                    <span class="d-flex line-scale-value line-scale-value-column margin-left">{{ 'POLL-EDITOR.COLUMNS' | translate }}</span>
                                    <div *ngFor="let option of question.data.columns; let i = index">
                                        <div class="container p-0">
                                            <div class="row appearance appearance-column margin-left hover-close-icon">
                                                <div class="col-4">
                                                    <span class="options-numbers">{{i + 1 + '.'}}</span>
                                                    <mat-form-field appearance="outline"
                                                                    class="field-option field-option-x field-option-list field-option-grid">
                                                        <input id="{{question.uuid}}" #colField matInput value="" [(ngModel)]="question.data.columns[i].value" [maxlength]="1024" (keydown.enter)="onEnterPressAddQuestion('column', question)" (keydown.backspace)="onBackspacePressDeleteQuestion($event, question, option.uuid, option.value, 'column')">
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-4 x-container x-container-list x-container-list-grid">
                                                    <img class="x-icon mb-6" src="../../../assets/images/icons/x-icon.svg" alt="Ikona zamknięcia" width="15" height="15" (click)="removeColumn(question, option.uuid)">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="ml-4 field-option field-option-add field-option-add-grid margin-left" (click)="addColumn(question)">
                                        <mat-icon class="options options-plus options-plus-multiple" svgIcon="survey-plus-icon"></mat-icon>
                                        <p class="field-option-add-option">{{ 'POLL-EDITOR.ADD-OPTION' | translate }}</p>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </mat-tab>

                    <mat-tab>
                        <ng-template mat-tab-label>
                            <div tourAnchor="poll.report">{{ 'POLL-EDITOR.REPORT' | translate }}</div>
                        </ng-template>
                        <app-poll-report *ngIf="tabIndex === 1" [widgetUuid]="widgetUuid" [questions]="questions" [isAnonymous]="isAnonymous"
                                         (exportPdfFn)="onExportGeneralPdf()" (exportXlsxFn)="onExportXlsxAll()">
                        </app-poll-report>
                    </mat-tab>

                    <mat-tab>
                        <ng-template mat-tab-label>
                            <div tourAnchor="poll.settings">{{ 'POLL-EDITOR.SETTINGS' | translate }}</div>
                        </ng-template>
                        <mat-accordion>
                            <mat-expansion-panel (opened)="onSubmitCheck()" [expanded]="true" style="box-shadow: none;" class="mb-8 mt-8">
                                <mat-expansion-panel-header [collapsedHeight]="'60px'" [expandedHeight]="'60px'">
                                    <div class="pt-10 pb-8 section-text-color">{{ 'POLL-EDITOR.DATA-COLLECTION' | translate }}</div>
                                </mat-expansion-panel-header>
                                <hr>
                                <div class="config-block m-20 d-flex col w-50">
                                    <div class="d-flex row justify-between">
                                        <div class="pt-6 pb-8">{{ 'POLL-EDITOR.LIMIT-ONE-ANSWER' | translate }}</div>
                                        <mat-slide-toggle
                                                color="primary"
                                                [(ngModel)]="isSingleSubmit">
                                        </mat-slide-toggle>
                                    </div>
                                    <mat-form-field class="mt-10">
                                        <mat-label>{{ 'POLL-EDITOR.POLL-TYPE' | translate }}</mat-label>
                                        <mat-select [(ngModel)]="pollType"
                                                    (ngModelChange)="onAnonymousChange($event)"
                                                    [disabled]="isReadOnly"
                                                    matTooltip="{{ isReadOnly ? ('POLL-EDITOR.POLL-TOOLTIP-TYPE' | translate) : '' }}">
                                            <mat-option *ngFor="let option of pollTypes" [value]="option.value">
                                                {{ option.label }}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <mat-form-field>
                                        <mat-label>{{ 'POLL-EDITOR.REQUIRED-FIELDS' | translate }}</mat-label>
                                        <mat-select [disabled]="disableSelect"
                                                    multiple
                                                    (selectionChange)="addContactData($event)"
                                                    [(ngModel)]="selectedContactData">
                                            <ng-container *ngFor="let item of contactData">
                                                <mat-option [disabled]="isFieldDisabled(item.value)"
                                                            [value]="item.value">
                                                    {{ item.label }}
                                                </mat-option>
                                            </ng-container>
                                        </mat-select>
                                    </mat-form-field>
                                </div>
                            </mat-expansion-panel>
                            <mat-expansion-panel style="box-shadow: none;" class="mb-8 mt-8">
                                <mat-expansion-panel-header [collapsedHeight]="'60px'" [expandedHeight]="'60px'">
                                    <div class="pt-10 pb-8 section-text-color">{{ 'POLL-EDITOR.TEXT-STYLE' | translate }}</div>
                                </mat-expansion-panel-header>
                                <hr>
                                <div class="config-block mt-10 mb-20 d-flex col">
                                    <mat-form-field appearance="outline" class="w-80 ml-20">
                                        <mat-label>{{ 'POLL-EDITOR.HEADER-FONT-SIZE' | translate }}</mat-label>
                                        <input type="number" matInput required
                                               [pattern]="'^(?:1[4-9]|2[0-9]|3[0-9]|40)$'"
                                               [(ngModel)]="pollStyles.titleFontSize"
                                               [min]="14" [max]="40" step="1"
                                               (ngModelChange)="onFontChange(14, 40, 'titleFontSize')">
                                        <mat-error *ngIf="fontSizeError">
                                            {{ 'POLL-EDITOR.FONT-SIZE-RANGE-14-40' | translate }}
                                        </mat-error>
                                    </mat-form-field>
                                    <mat-form-field appearance="outline" class="w-80 ml-20">
                                        <mat-label>{{ 'POLL-EDITOR.QUESTION-FONT-SIZE' | translate }}</mat-label>
                                        <input type="number" matInput required
                                               [pattern]="'^(?:1[2-9]|2[0-9]|30)$'"
                                               [(ngModel)]="pollStyles.questionFontSize"
                                               [min]="12" [max]="30" step="1"
                                               (ngModelChange)="onFontChange(12, 30, 'questionFontSize')">
                                        <mat-error *ngIf="fontSizeError">
                                            {{ 'POLL-EDITOR.FONT-SIZE-RANGE-12-30' | translate }}
                                        </mat-error>
                                    </mat-form-field>
                                    <mat-form-field appearance="outline" class="w-80 ml-20">
                                        <mat-label>{{ 'POLL-EDITOR.ANSWER-FONT-SIZE' | translate }}</mat-label>
                                        <input type="number" matInput required
                                               [pattern]="'^(?:1[2-9]|20)$'"
                                               [(ngModel)]="pollStyles.answerFontSize"
                                               [min]="12" [max]="20" step="1"
                                               (ngModelChange)="onFontChange(12, 20, 'answerFontSize')">
                                        <mat-error *ngIf="fontSizeError">
                                            {{ 'POLL-EDITOR.FONT-SIZE-RANGE-12-20' | translate }}
                                        </mat-error>
                                    </mat-form-field>
                                </div>
                            </mat-expansion-panel>
                            <mat-expansion-panel style="box-shadow: none;" class="mb-8 mt-8">
                                <mat-expansion-panel-header [collapsedHeight]="'60px'" [expandedHeight]="'60px'">
                                    <div class="pt-10 pb-8 section-text-color">{{ 'POLL-EDITOR.BACKGROUND-COLOR' | translate }}</div>
                                </mat-expansion-panel-header>
                                <hr>
                                <div class="config-block mt-10 mb-20 d-flex col justify-around align-start">
                                    <div class="d-flex align-center ml-22">
                                        <div>{{ 'POLL-EDITOR.SELECTED-COLOR' | translate }}</div>
                                        <div *ngIf="pollStyles.backgroundColor"
                                             class="selected-color-display"
                                             [style.background-color]="pollStyles.backgroundColor">
                                        </div>
                                    </div>
                                        <color-circle
                                                [colors]="backgroundColors"
                                                [width]="300"
                                                [circleSize]="30"
                                                [circleSpacing]="15"
                                                (onChange)="changeColor($event)">
                                        </color-circle>
                                </div>
                            </mat-expansion-panel>
                            <mat-expansion-panel *ngIf="!isAnonymous" (opened)="onSubmitCheck()" style="box-shadow: none;" class="mb-8 mt-8">
                                <mat-expansion-panel-header [collapsedHeight]="'60px'" [expandedHeight]="'60px'">
                                    <div class="pt-10 pb-8 section-text-color">{{ 'FORM-WIDGET.TERMS-ACCEPTATION' | translate }}</div>
                                </mat-expansion-panel-header>
                                <hr>
                                <div class="d-grid grid-cols-1 grid-row-gap-8 p-12 mt-10 mb-20 ml-12">
                                    <div> {{ 'POLL-EDITOR.ADD-TERM' | translate }}
                                        <button
                                                [disabled]="isReadOnly"
                                                matTooltip="{{ isReadOnly ? ('POLL-EDITOR.POLL-TOOLTIP-BTN' | translate) : '' }}"
                                                mat-icon-button
                                                color="primary"
                                                (click)="addQuillEditor()">
                                                    <mat-icon color="primary">add_circle</mat-icon>
                                        </button>
                                    </div>
                                    <ng-container *ngFor="let term of customTerms; let i = index">
                                        <div
                                            class="quill-editor"
                                            matTooltip="{{ isReadOnly ? ('POLL-EDITOR.POLL-TOOLTIP' | translate) : '' }}"
                                            [matTooltipDisabled]="!isReadOnly"
                                            [attr.id]="'container' + i">
                                            <ng-container *ngIf="!isReadOnly">
                                                <button class="quill-editor__close" (click)="removeCustomTerm(i)"><mat-icon>close</mat-icon></button>
                                            </ng-container>
                                            <quill-editor
                                                    [(ngModel)]="term.content"
                                                    (ngModelChange)="handleContentChange($event)"
                                                    [modules]="editorModules"
                                                    [formats]="editorFormats"
                                                    [readOnly]="isReadOnly"
                                                    [ngClass]="{ 'read-only-editor': isReadOnly }"
                                                    [id]="'firstTermData' + i"
                                                    placeholder="{{ 'POLL-EDITOR.QUILL-PLACEHOLDER' | translate }}">
                                            </quill-editor>
                                            <mat-error class="quill-error" *ngIf="contentLength < 20 && (term.content | stripHtml | trimWhitespaces).length < 20">
                                                {{ 'POLL-EDITOR.MIN-LENGTH' | translate: { num: 20 } }}
                                            </mat-error>
                                            <mat-error class="quill-error" *ngIf="contentLength > 10000 && (term.content | stripHtml | trimWhitespaces).length > 10000">
                                                {{ 'POLL-EDITOR.MAX-LENGTH' | translate: { num: 10000 } }}
                                            </mat-error>
                                        </div>
                                    </ng-container>
                                </div>
                            </mat-expansion-panel>
                        </mat-accordion>
                    </mat-tab>

                    <mat-tab>
                        <ng-template mat-tab-label>
                            <div tourAnchor="poll.share">
                                {{ 'POLL-EDITOR.SHARE' | translate }}
                            </div>
                        </ng-template>
                        <mat-expansion-panel [expanded]="true" style="box-shadow: none;" class="mb-8 mt-8">
                            <mat-expansion-panel-header [collapsedHeight]="'60px'" [expandedHeight]="'60px'">
                                <div class="pt-10 pb-8 section-text-color">{{ 'POLL-EDITOR.LINK' | translate }}</div>
                            </mat-expansion-panel-header>
                            <hr>
                            <div class="config-block mt-10 mb-20 d-flex justify-around align-center">
                                <mat-form-field appearance="outline" class="link-form-field">
                                    <mat-label></mat-label>
                                    <input matInput [value]="pollWidgetLink" readonly rows="1">
                                </mat-form-field>
                                <mat-icon class="opacity-6 hover:opacity-9 transition-opacity cursor-pointer mb-12"
                                    matTooltip="{{ 'WIDGET.LINK-TOOLTIP' | translate }}"
                                    ngxClipboard [cbContent]="pollWidgetLink"
                                    (click)="onCopyIconClick()">content_copy</mat-icon>
                            </div>
                        </mat-expansion-panel>
                        <mat-expansion-panel style="box-shadow: none;">
                            <mat-expansion-panel-header [collapsedHeight]="'60px'" [expandedHeight]="'60px'">
                                <div class="pt-10 pb-8 section-text-color">{{ 'POLL-EDITOR.PLACE-HTML-CODE' | translate }}</div>
                            </mat-expansion-panel-header>
                            <hr>
                            <div class="config-block mt-10 mb-20 d-flex justify-around align-center">
                                <mat-form-field appearance="outline" class="link-form-field">
                                    <input matInput [value]="pollWidgetScript" readonly rows="2">
                                </mat-form-field>
                                <mat-icon class="opacity-6 hover:opacity-9 transition-opacity cursor-pointer mb-12"
                                    matTooltip="{{ 'WIDGET.LINK-TOOLTIP' | translate }}"
                                    ngxClipboard [cbContent]="pollWidgetScript"
                                          (click)="onCopyIconClick()">content_copy</mat-icon>
                            </div>
                        </mat-expansion-panel>
                    </mat-tab>

                    <mat-tab *ngIf="devMode" label="Generowany obiekt">
                        <mat-card class="mb-8 mt-8">
                            {{questions | json}}
                        </mat-card>
                    </mat-tab>
                </mat-tab-group>
            </div>
        </div>
        <div class="float-control">
            <div id="addQuestionBtn" class="poll-menu-rounded-btn poll-add-question d-flex justify-center align-center" tourAnchor="poll.add-section">
                <mat-icon [matMenuTriggerFor]="menu" matTooltip="{{ 'POLL-EDITOR.ADD-SECTION' | translate }}">add</mat-icon>
                <mat-menu #menu="matMenu" xPosition="before">
                    <button *ngFor="let questionType of questionTypes" mat-menu-item (click)="addQuestion(questionType.value)">{{questionType.name}}</button>
                </mat-menu>
            </div>
            <div id="showPreviewBtn" (click)="onPollPreview()" class="poll-menu-rounded-btn poll-preview d-flex justify-center align-center mt-8">
                <mat-icon matTooltip="{{ 'POLL-EDITOR.PREVIEW' | translate }}" svgIcon="outline-visibility-icon"></mat-icon>
            </div>
            <ng-container *ngIf="tabIndex === 1">
                <div class="poll-menu-rounded-btn poll-preview d-flex justify-center align-center mt-8"
                     [matMenuTriggerFor]="generateMenu">
                    <mat-icon matTooltip="{{ 'POLL-EDITOR.GENERATE' | translate }}" class="material-symbols-outlined">download</mat-icon>
                </div>
                <mat-menu #generateMenu="matMenu" xPosition="before">
                    <button mat-menu-item (click)="onExportGeneralPdf()">{{ 'POLL-EDITOR.GENERATE-PDF-REPORT' | translate }}</button>
                    <button mat-menu-item (click)="onExportXlsxAll()">{{ 'POLL-EDITOR.GENERATE-XLSX-REPORT' | translate }}</button>
                </mat-menu>
            </ng-container>
        </div>
    </div>
</div>
