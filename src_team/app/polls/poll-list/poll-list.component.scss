@use '../../../variables' as *;

.list {
    display: flex;
    gap: 29px;
    justify-content: flex-start;
    margin: 0 auto;
    max-width: 1180px;

    .card-item {
        cursor: pointer;

        .preview,
        .add {
            aspect-ratio: 1 / 1;
            background: #fff;
            height: 130px;
            width: 175px;

            &:hover {
                background-color: $hover;
                transition: background-color 0.3s;
            }
        }

        .preview {
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding-left: 10px;
            padding-right: 10px;
            font-size: 14px;
            flex-direction: column;
            position: relative;
            font-weight: 400;
            line-height: 22px;
            overflow-wrap: break-word;
            word-break: break-all;

            .action {
                position: absolute;
                bottom: 5px;
                right: 10px;

                .remove {
                    color: $accent !important;
                }
            }
        }

        .add {
            align-items: center;
            display: flex;
            justify-content: center;

            .add-icon {
                color: $green;
                font-size: 80px;
                height: 80px;
                width: 80px;
            }
        }

        .title {
            font-size: 14px;
            font-weight: 500;
            margin-top: 15px;
        }
    }

    .counter {
        display: flex;
        position: absolute;
        right: -15px;
        top: -12px;
        width: 31px;
        height: 31px;
        border-radius: 25px;
        justify-content: center;
        align-items: center;

        &.new-answer-background {
            background-color: $new-answers-color;
            color: $new-answers-text-color;
        }

        &.default-background {
            background-color: $default-color;
            color: $default-text-color
        }
    }
}

.poll-menu {
    background: #E4F5E5;
    overflow-x: auto;
    padding: 40px 130px;
    width: 100%;

    .menu-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0 auto 30px auto;
        max-width: 1180px;
    }

    &.is-mobile {
        padding: 20px 40px;
    }
}

.section-title {
    padding: 0 130px;

    &.is-mobile {
        padding: 20px 40px;
    }

    .title {
        font-size: 16px;
        font-weight: 500;
        margin: 30px auto 15px auto;
        max-width: 1180px;
    }
}

.poll-list-container {
    height: calc(100% - 370px);
    overflow-y: auto;
    padding: 40px 130px;
    width: 100%;

    .list.all {
        flex-wrap: wrap;
    }

    &.is-mobile {
        padding: 20px 40px;
    }
}

.deactivated-poll {
    background-color: $grey !important;
}

.green-icon {
    color: $green;
}
