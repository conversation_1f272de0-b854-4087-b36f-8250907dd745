<div class="poll-menu" ngClass.xs="is-mobile">
    <div class="menu-title">{{ 'POLL-LIST.CREATE-NEW-POLL' | translate }}</div>

    <div class="last list">
        <div class="card-item add" (click)="addPoll()">
            <div class="add">
                <mat-icon color="primary" class="add-icon">add_circle_outlined</mat-icon>
            </div>
            <div class="title">{{ 'POLL-LIST.NEW-POLL' | translate }}</div>
        </div>

        <ng-container *ngFor="let poll of lastPollsList">
            <div class="card-item last" (click)="editPoll(poll)" [@fadeInOut]>
                <div [ngClass]="{'deactivated-poll': !isPollCurrentlyActive(poll.id)}"
                     [matTooltip]="!isPollCurrentlyActive(poll.id) ? ('POLL-LIST.INACTIVE-POLL' | translate) : ''"
                     class="preview">
                        {{poll.url}}
                    <div class="action"></div>
                </div>
            </div>
        </ng-container>
    </div>
</div>

<div class="section-title" ngClass.xs="is-mobile">
    <div class="title">
        {{ 'POLL-LIST.ALL-POLLS' | translate }}
    </div>
</div>

<div class="poll-list-container" ngClass.xs="is-mobile">
    <div class="all list">
        <ng-container *ngFor="let poll of pollsList; let i = index">
            <div class="card-item last"
                 (click)="editPoll(poll)"
                 [@fadeInOut]>
                <div class="preview"
                     [ngClass]="{'deactivated-poll': !isPollCurrentlyActive(poll.id)}"
                     [matTooltip]="!isPollCurrentlyActive(poll.id) ? ('POLL-LIST.INACTIVE-POLL' | translate) : ''">
                        {{poll.url}}
                    <ng-container *ngIf="pollUsersCounter[i]?.total <= 99; else maxAnswers">
                        <div class="counter"
                             matTooltip="{{ 'POLL-LIST.ANSWERS-COUNT' | translate }}: {{ pollUsersCounter[i]?.total }}"
                             [ngClass]="{
                                'new-answer-background': pollUsersCounter[i]?.hasNewAnswers,
                                'default-background': !pollUsersCounter[i]?.hasNewAnswers}">
                            <span>{{ pollUsersCounter[i]?.total }}</span>
                        </div>
                    </ng-container>
                    <ng-template #maxAnswers>
                        <div class="counter"
                             matTooltip="{{ 'POLL-LIST.ANSWERS-COUNT' | translate }}: {{ pollUsersCounter[i]?.total }}"
                             [ngClass]="{
                                'new-answer-background': pollUsersCounter[i]?.hasNewAnswers,
                                'default-background': !pollUsersCounter[i]?.hasNewAnswers}">
                            <span>99+</span>
                        </div>
                    </ng-template>

                    <div class="action">
                        <mat-icon color="primary"
                                  [matMenuTriggerFor]="menu"
                                  #menuTrigger="matMenuTrigger"
                                  (click)="$event.stopPropagation()">more_vert</mat-icon>

                        <mat-menu #menu="matMenu" xPosition="before">
                            <button mat-menu-item class="d-none"></button>
                            <button mat-menu-item (click)="openPoll(poll.external_hash); $event.stopPropagation()"><mat-icon>open_in_new</mat-icon>{{ 'POLL-LIST.OPEN-NEW-WINDOW' | translate }}</button>
                            <button mat-menu-item *ngIf="isPollCurrentlyActive(poll.id)" (click)="deactivatePoll(poll.id, menuTrigger); $event.stopPropagation()"><mat-icon color="accent">block</mat-icon>{{ 'POLL-LIST.DEACTIVATE-POLL' | translate }}</button>
                            <button mat-menu-item *ngIf="!isPollCurrentlyActive(poll.id)" (click)="activatePoll(poll.id, menuTrigger); $event.stopPropagation()"><mat-icon class="green-icon">refresh</mat-icon>{{ 'POLL-LIST.ACTIVATE-POLL' | translate }}</button>
                            <button mat-menu-item (click)="removePoll(poll.id, poll.url); $event.stopPropagation()"><mat-icon color="accent">close</mat-icon>{{ 'POLL-LIST.DELETE' | translate }}</button>
                        </mat-menu>
                    </div>
                </div>
            </div>
        </ng-container>
    </div>
</div>
