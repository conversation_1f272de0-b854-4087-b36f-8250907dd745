import {AfterViewInit, Component, OnInit, ViewChild} from '@angular/core';
import {PollAnswerService} from '../../services/poll-answer.service';
import {MatPaginator, PageEvent} from '@angular/material/paginator';
import {UserInputStorageService} from '../../services/user-input-storage.service';
import { MatTableDataSource, MatTable, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow } from '@angular/material/table';
import {PollAnswerInterface} from '../../common/interfaces/poll-answer.interface';
import {MatDialog} from '@angular/material/dialog';
import {PollUserComponent} from '../poll-user/poll-user.component';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { NgIf } from '@angular/common';
import { DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { ListPaginatorComponent } from '../../shared/list-paginator/list-paginator.component';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-poll-user-list',
    templateUrl: './poll-user-list.component.html',
    styleUrls: ['./poll-user-list.component.scss'],
    imports: [DefaultClassDirective, NgIf, MatTable, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow, DefaultLayoutAlignDirective, ListPaginatorComponent, TranslatePipe]
})
export class PollUserListComponent implements OnInit {

    @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;

    displayedColumns = ['email', 'firstname', 'lastname', 'company', 'phone'];

    dataSource = null;

    users;

    limit: number = 10;

    page: number = 1;

    totalUsers: number;

    constructor(
        private _pollAnswerService: PollAnswerService,
        private _userInputStorageService: UserInputStorageService,
        private _dialog: MatDialog
    ) { }

    ngOnInit(): void {
        this.getPaginationLimit();

        this.getAnswerData();
    }

    private get requestConditions() {
        return `?fields=${this.displayedColumns.join(',')}&limit=${this.limit}&page=${this.page}`;
    }

    onPageEvent(event: PageEvent): void {
        this.page = event.pageIndex + 1;
        this.limit = event.pageSize;

        this.setPaginationLimit(this.limit);
        this.getAnswerData();
    }

    private getPaginationLimit() {
        this.limit = +this._userInputStorageService.getValue('pollUserList_paginationLimit') || 10;
    }

    private setPaginationLimit(limit = 10) {
        this.limit = limit;
        this._userInputStorageService.setValue('pollUserList_paginationLimit', limit);
    }

    getAnswerData() {
        this._pollAnswerService.getPollUsers(this.requestConditions)
            .subscribe(
                (response: {results, total: number}) => {
                    this.users = response.results;
                    this.totalUsers = response.total;

                    this.dataSource = new MatTableDataSource<PollAnswerInterface>(this.users);
                    this.dataSource.paginator = this.paginator;
                },
                error => {
                    console.error('Wystąpił błąd przy wyświetlaniu listy klientów:', error);
                }
            );
    }

    openPollUserDetails(row) {
        this._dialog.open(PollUserComponent, {
            width: '690px',
            autoFocus: false,
            disableClose: false,
            panelClass: 'full-width-dialog',
            data: row.email
        });
    }

}
