<div class="user-list-card" ngClass.xs="is-mobile">
    <div class="table-wrapper" *ngIf="users?.length">
        <table mat-table [dataSource]="users" class="mat-elevation-z0">
            <ng-container matColumnDef="firstname">
                <th mat-header-cell *matHeaderCellDef>{{ 'POLL-USER-LIST.FIRSTNAME' | translate }}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.firstname}}
                </td>
            </ng-container>

            <ng-container matColumnDef="lastname">
                <th mat-header-cell *matHeaderCellDef>{{ 'POLL-USER-LIST.LASTNAME' | translate }}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.lastname}}
                </td>
            </ng-container>

            <ng-container matColumnDef="company">
                <th mat-header-cell *matHeaderCellDef>{{ 'POLL-USER-LIST.COMPANY' | translate }}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.company}}
                </td>
            </ng-container>

            <ng-container matColumnDef="phone">
                <th mat-header-cell *matHeaderCellDef>{{ 'POLL-USER-LIST.PHONE' | translate }}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.phone}}
                </td>
            </ng-container>

            <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>{{ 'POLL-USER-LIST.EMAIL' | translate }}</th>
                <td mat-cell *matCellDef="let element">
                    {{element.email}}
                </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                class="user-list-row"
                (click)="openPollUserDetails(row)">
            </tr>
        </table>
    </div>

    <div *ngIf="users && users.length === 0" class="no-data-font no-data" fxLayoutAlign="center center">
        {{ 'POLL-USER-LIST.NO-RESULTS' | translate }}
    </div>
</div>

<div class="paginator-container mat-elevation-z2">
    <app-list-paginator
            [pageSize]="limit"
            [pageIndex]="page - 1"
            [length]="totalUsers"
            (changePage)="onPageEvent($event)"
            showFirstLastButtons="true">
    </app-list-paginator>
</div>
