@use '../../../variables' as *;

.no-data {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 150px;
}

table {
    margin-top: 15px;
    width: 100%;
    padding-bottom: 26px;
    border-radius: $container-border-radius;

    mat-icon {
        font-size: 18px;
    }

    th {
        border: none;
        border-top-left-radius: $container-border-radius;
        border-top-right-radius: $container-border-radius;
        position: relative;
        font-size: 16px;
        font-weight: 400;
        color: $hard-dark-grey;

        &:after {
            background: $dark-blue;
            bottom: -1px;
            content: "";
            height: 1px;
            left: 8px;
            position: absolute;
            width: calc(100% - 16px);
        }
    }

    td {
        border: none;
        position: relative;

        &:after {
            background: rgba(0, 95, 170, 0.4);
            bottom: 0;
            content: "";
            height: 0.5px;
            left: 8px;
            position: absolute;
            width: 100%;
        }

        &:first-of-type {
            &:after {
                left: 8px;
                width: 100%;
            }
        }

        &:last-of-type {
            &:after {
                right: 10px;
                width: 90%;
            }
        }
    }

    td,
    th {
        padding-right: 19px;
        padding-left: 19px;
    }

    .disabled-row {
        td {
            color: $grey;
        }
    }
}

.table-wrapper {
    height: 80vh;
    overflow: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.user-list-row:hover {
    cursor: pointer;

    td {
        color: $primary;
        font-size: 14px;
    }
}

.table-wrapper {
    overflow: auto;
}

.paginator-container {
    position: absolute;
    bottom: 0;
    left: 0;
    flex-flow: wrap;
    box-sizing: border-box;
    display: flex;
    background-color: var(--background-color);
    min-height: 64px;
    width: 100%;
    justify-content: flex-end;
    margin-bottom: -67px;
}
