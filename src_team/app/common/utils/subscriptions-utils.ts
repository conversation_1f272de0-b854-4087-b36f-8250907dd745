import dayjs from 'dayjs';
import { Subscription } from 'rxjs';

export function isClientSubscriptionExpired(expirationDate: string | Date) {
    const endOfToday = dayjs(Date.now()).endOf('day');

    return dayjs(expirationDate).isBefore(endOfToday);
}

export function isClientSubscriptionCategoryActive(expirationDate: string | Date, issuesLimit: number, issuesUsed: number) {
    return issuesUsed < issuesLimit && !isClientSubscriptionExpired(expirationDate);
}

export function isSubscriptionUndefinedOrClosed(subscription: Subscription) {
    return !subscription || subscription.closed;
}
