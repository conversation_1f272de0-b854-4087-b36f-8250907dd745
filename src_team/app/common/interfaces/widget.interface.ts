export interface WidgetInterface {
    uid?: any;
    id?: number;
    name?: string;
    settings?: {};
    type?: string;
    theme?: string;
    header?: HeaderWidgetInterface;
    content?: ContentWidgetInterface;
    actions?: ActionsWidgetInterface;
    button?: ButtonOpenWidgetInterface;
    onInit?: OnInitWidgetInterface;
}

export interface WidgetTableInterface {
    index: number;
    id: number;
    name: string;
    active: boolean;
    actions: null;
}

export interface UserCustomTerm {
    id: number;
    content: string;
}

export interface HeaderWidgetInterface {
    formLabel: string;
    chatLabel: string;
    background: string;
    labelColor: string;
    iconColor: string;
    labelSize: number;
    formSubtitle: string;
    formLabelColor: string;
    formLabelFontSize: number;
    formSubtitleColor: string;
    formSubtitleFontSize: number;
    textAlign: string;
}
export interface ContentWidgetInterface {
    textareaBorderColor?: string;
    placeholderText?: string;
    backgroundColor?: string;
    supportBackground?: string;
    clientBackground?: string;
    fontSize?: number;
    contentBackground?: string;
    supportColor?: string;
    clientColor?: string;
    termsAndConditionsLink?: string;
    privacyStatementLink?: string;
    inputsTextColor?: string;
    inputsBorderColor?: string;
    formLinksAndCloseIconColor?: string;
    customUserTerms?: UserCustomTerm[];
    placeholderTextColor?: string;
    formBackground?: string;
}

export interface ActionsWidgetInterface {
    buttonBackground: string;
    fontSizeButton: number;
    attachmentIconColor: string;
    actionsBackground: string;
    textAreaBackground: string;
    buttonLabelColor: string;
    showAttachmentIcon: boolean;
    color?: string;
    showPhoneInput: boolean;
    showLastnameInput: boolean;
    sendBtnLabelColor: string;
    defaultButtonBackground: string;
}

export interface ButtonOpenWidgetInterface {
    iconSvg?: string;
    formIconSvg?: string;
    radius: number;
    positionOnPage: {right: number; bottom: number};
    background: string;
    color: string;
    label?: string;
    size: number;
    fontSize: number;
}

export interface OnInitWidgetInterface {
    text: string;
    logo: string;
    logoSrc?: string;
    requiredRegister: boolean;
    requiredSetFirstName: boolean;
    textColor: string;
    fontText: number;
    buttonBackground: string;
    buttonColorText: string;
    buttonFontSize: number;
    autoOpenChat: boolean;
    autoOpenChatDelay: number;
    showTooltipMessage?: boolean;
    tooltipMessageDelay?: number;
    tooltipMessageText?: string;
    tooltipBackground?: string;
    tooltipTextColor?: string;
    inputPlaceholder?: string;
    buttonLabel: string;
    footerBackground: string;
}

