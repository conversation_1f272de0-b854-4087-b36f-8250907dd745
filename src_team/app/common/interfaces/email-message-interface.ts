import {QueueStatus} from '../enums/email-integration.enum';

export interface EmailMessageInterface {
    id: number;
    mail_name: string;
    mail_subject: string;
    mail_body: string;
    mail_address: string;
    sender_mail_address: string;
    receiver_mail_address: string;
    cc_mail_addresses: string;
    bcc_mail_addresses: string;
    timestamp: string;
    created: string;
    modified: string;
    status: string;
    is_read: string;
    tags: string;
    commonFileId: string;
    commonFileName: string;
    commonFileType: string;
    mail_sent_mysql_messages_ids: string;
    priority: number;
    user_id: number;
    client_id: string;
}

export interface MailSend {
    mail_id: number;
    mail_server_id: number;
    mail_to_address: string;
    mail_subject: string;
    mail_to_name: string;
    mail_body: string;
    status: number;
}

export interface MailPriorityResponse {
    status?: string;
    success?: boolean;
    message?: string;
    results?: {
        MailReceivedMysqlMessage?: {
            id: number | string;
            priority: number;
        };
        MailSentMysqlMessage?: {
            id: number | string;
            priority: number;
        };
    }[];
}

export interface MailThreadResponse {
    results: {
        MailSentMysqlMessage: EmailMessageInterface;
    }[];
}
