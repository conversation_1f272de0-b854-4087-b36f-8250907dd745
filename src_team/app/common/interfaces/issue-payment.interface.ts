export interface IssuePaymentOfferInterface {
    'IssuePaymentOffer': {
        id?: number;
        customer_id?: number;
        user_client_id?: number;
        issue_id?: number;
        name?: string;
        hourly_rate?: number;
        man_hours?: number;
        work_time?: number;
        description?: string;
        amount?: number;
        is_chosen?: number;
        is_recommended?: number;
        modified?: string;
        created?: string;
        currency?: string | null; // waluta - przekazane tutaj jest key currency
    };
    'Customer': {
        vat_tax_rate?: number;
    };
}
