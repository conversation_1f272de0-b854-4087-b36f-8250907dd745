export interface UserInterface {
    id: number;
    customer_id?: number;
    user_role_id?: number;
    firstname?: string;
    lastname?: string;
    company_name?: string | null;
    active?: number;
    banned?: number;
    email?: string;
    mentor_id?: number | null;
    phone?: string;
    description?: string;
    has_avatar?: number;
    has_user_access?: number;
    ip?: string;
    is_public?: number;
    has_chat_access?: number;
    has_poll_access?: number;
    is_admin?: number;
    afternoon_duty?: string;
    created?: string;
    independent_choice?: string;
    is_client?: string;
    is_manual?: string;
    is_send_remind?: string;
    language?: string;
    role?: string;
    modified?: string;
    morning_duty?: string;
    referer?: string | null;
    register_page_name?: string;
    removed?: string;
    sso_invitation_send?: string;
    sso_user_id?: string | null;
    tags?: string;
    user_created_id?: string | null;
    work_time?: string;
    isOnline?: boolean;
    lastActiveTimePassed?: number;
    hasDuty?: boolean;
}
