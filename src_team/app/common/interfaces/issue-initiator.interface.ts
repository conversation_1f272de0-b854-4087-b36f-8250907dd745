export interface IssueInitiator {
    client_info?: string | null;
    created?: string;
    customer_id?: string | number;
    email: string;
    id?: string;
    external_user_id?: string | number;
    first_name: string;
    last_name?: string | null;
    city?: string;
    nip?: string;
    street?: string;
    web?: string;
    zip?: string;
    modified?: string;
    name?: string;
    phone?: string;
    user_client_id?: string | number;
    tags?: string;
}

interface IssueInitiatorItem {
    IssueInitiator: IssueInitiator;
}

export interface IssueInitiatorResponse {
    total: number;
    links: {
        self: string;
        first: string;
        previous: string;
        next: string;
        last: string;
    };
    results: IssueInitiatorItem[];
}

export interface ClientPaginationResponse {
    items: IssueInitiator[];
    totalCount: number;
}
