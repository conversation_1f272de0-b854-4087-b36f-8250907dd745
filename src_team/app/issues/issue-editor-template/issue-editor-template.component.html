<div class="templates">
    <h2 mat-dialog-title>{{'ISSUE-EDITOR.TEMPLATE' | translate}}</h2>

    <div class="templates-content">
        <mat-accordion [multi]="false">
            <mat-expansion-panel *ngFor="let templateGroup of templateGroups;">
                <mat-expansion-panel-header>
                    <mat-panel-title>
                        {{templateGroup.TemplateGroup.name}}
                    </mat-panel-title>
                </mat-expansion-panel-header>
                <mat-list>
                    <mat-list-item *ngFor="let template of templateGroup.Template">
                        <span
                            class="template-link"
                            mat-list-item
                            (click)="clickTemplate(template.id, template.body)"
                            matTooltip="{{template.body | stripHtml}}"
                            matTooltipPosition="above"
                        >
                            {{template.name}}
                        </span>
                    </mat-list-item>
                </mat-list>
            </mat-expansion-panel>
        </mat-accordion>
    </div>

    <div class="templates-actions">
        <button mat-button mat-dialog-close>{{'ISSUE-EDITOR.CLOSE' | translate}}</button>
    </div>
</div>
