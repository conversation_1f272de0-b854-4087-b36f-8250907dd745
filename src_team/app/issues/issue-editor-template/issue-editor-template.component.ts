import {Component, OnInit} from '@angular/core';
import {TemplateGroupService} from '../../services/template-group.service';
import {TemplateService} from '../../services/template.service';
import { MatDialogRef, MatDialogTitle, MatDialogClose } from '@angular/material/dialog';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle } from '@angular/material/expansion';
import { NgFor } from '@angular/common';
import { MatList, MatListItem } from '@angular/material/list';
import { MatTooltip } from '@angular/material/tooltip';
import { MatButton } from '@angular/material/button';
import { StripHtmlPipe } from '../../shared/pipes/strip-html.pipe';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-issue-editor-template',
    templateUrl: './issue-editor-template.component.html',
    styleUrls: ['./issue-editor-template.component.scss'],
    imports: [MatDialogTitle, MatAccordion, NgFor, MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle, MatList, MatListItem, MatTooltip, MatButton, MatDialogClose, StripHtmlPipe, TranslatePipe]
})
export class IssueEditorTemplateComponent implements OnInit {
    templateGroups = [];

    constructor(
        private templateGroupService: TemplateGroupService,
        private templateService: TemplateService,
        public dialogRef: MatDialogRef<IssueEditorTemplateComponent>
    ) {
    }

    ngOnInit() {
        this.getTemplatesWithGroups();
    }

    getTemplatesWithGroups() {
        this.templateGroupService.getAllGroupTemplatesWithTemplates().subscribe(result => {
            this.templateGroups = (result || []).filter((templateGroup: any) => templateGroup.Template.length);
        });
    }

    clickTemplate(id, text) {
        this.templateService.getCommonFileObjectsByTemplateId(id).subscribe(result => {
            const ids = result.map(obj => obj.CommonFileObject.common_file_id);

            this.dialogRef.close({text, id, files: ids});
        });
    }
}
