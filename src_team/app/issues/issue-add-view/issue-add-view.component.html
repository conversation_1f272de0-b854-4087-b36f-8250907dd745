<div class="new-issue-view" ngClass.xs="is-mobile">
    <form [formGroup]="newIssueForm" (ngSubmit)="onSubmit()" fxLayout="wrap row">
        <mat-card class="mat-elevation-z0 panel" fxFlex="calc(100% - 20px)" fxFlex.xs="100" fxFlexFill>
            <mat-card-title>{{ 'ISSUE-ADD-VIEW.ADD-ISSUE' | translate }}</mat-card-title>
            <mat-card-content>
                <div fxFlexFill>
                    <div fxLayout="wrap row" fxLayoutGap.gt-xs="20px">

                        <mat-form-field appearance="outline" class="dropdown">
                            <mat-label>{{ 'USER-EDIT-REPORT.CHANGE-HISTORY-TAB.CLIENT' | translate }}</mat-label>
                            <mat-select (selectionChange)="onClientSelect($event.value)" required formControlName="client">
                                <ngx-mat-select-search
                                    [placeholderLabel]="searchLabel"
                                    [noEntriesFoundLabel]="noEntriesFoundLabel"
                                    [formControl]="searchCtrl">
                                </ngx-mat-select-search>

                                <mat-option *ngIf="validClients?.length === 0" [value]="null">
                                    {{ noEntriesFoundLabel }}
                                </mat-option>

                                <ng-container *ngFor="let client of validClients">
                                    <mat-option [value]="client.email">
                                        {{ client.first_name }} {{ client.last_name }}
                                    </mat-option>
                                </ng-container>
                            </mat-select>
                            <mat-error
                                    *ngIf="validClients?.length > 0 && formCtrlClient.invalid && (formCtrlClient.dirty || formCtrlClient.touched)">
                                {{ 'ISSUE-ADD-VIEW.REQUIRED' | translate }}
                            </mat-error>
                            <mat-error *ngIf="validClients?.length <= 0">
                                {{ 'ISSUE-ADD-VIEW.EMPTY' | translate }}
                            </mat-error>

                        </mat-form-field>

                        <div fxFlex="calc(50% - 20px)" fxFlex.xs="100" fxFlexFill>
                        </div>

                        <div fxFlex="calc(100% - 20px)" fxFlexFill fxLayoutGap.gt-xs="10px" fxLayoutAlign="start center">
                            <div class="w-50">
                                <mat-form-field appearance="outline" fxFlex="calc(100% - 40px)">
                                    <mat-label>{{ 'ISSUE-ADD-VIEW.ISSUE-TITLE' | translate }}</mat-label>
                                    <input matInput formControlName="title" autocomplete="off">
                                    <mat-error *ngIf="formCtrlTitle.invalid && (formCtrlTitle.dirty || formCtrlTitle.touched)">
                                        <div *ngIf="formCtrlTitle.errors.minChars">
                                            {{ 'ISSUE-ADD-VIEW.TITLE-REQUIRED' | translate }} {{ formCtrlTitle.errors.minChars }} {{ 'ISSUE-ADD-VIEW.CHARACTERS' | translate }}
                                        </div>
                                    </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                </div>
            </mat-card-content>
        </mat-card>

        <div class="editor" fxFlex="calc(100% - 20px)" fxFlex.xs="100" fxFlexFill>
            <div class="editor-content" fxLayout="column" fxLayoutAlign="start" fxFill>
                <div class="quill editor">
                    <quill-editor
                            placeholder="{{ 'ISSUE-ADD-VIEW.ISSUE-CONTENT' | translate }}"
                            [bounds]="'.quill'"
                            formControlName="body"
                            [formats]="editorFormats"
                            (paste)="onPaste($event)">
                        <div quill-editor-toolbar>
                            <span class="ql-formats">
                                <button class="ql-bold" title="{{ 'ISSUE-ADD-VIEW.BOLD' | translate }}"></button>
                                <button class="ql-italic" title="{{ 'ISSUE-ADD-VIEW.ITALICS' | translate }}"></button>
                                <button class="ql-underline" title="{{ 'ISSUE-ADD-VIEW.UNDERLINE' | translate }}"></button>
                                <button class="ql-strike" title="{{ 'ISSUE-ADD-VIEW.CROSSING' | translate }}"></button>
                            </span>
                            <span class="ql-formats">
                                <button class="ql-list" value="bullet" title="{{ 'ISSUE-ADD-VIEW.BULLET' | translate }}"></button>
                                <button class="ql-list" value="ordered" title="{{ 'ISSUE-ADD-VIEW.NUMBERED' | translate }}"></button>
                                <button class="ql-link" title="{{ 'ISSUE-ADD-VIEW.LINK' | translate }}"></button>
                            </span>
                            <span class="ql-formats">
                                <button class="ql-clean" title="{{ 'ISSUE-ADD-VIEW.CLEAR' | translate }}"></button>
                            </span>
                        </div>
                    </quill-editor>
                </div>

                <mat-error class="error" *ngIf="formCtrlBody.invalid && (formCtrlBody.dirty || formCtrlBody.touched)">
                    <div *ngIf="formCtrlBody.errors.minChars">
                        {{ 'ISSUE-ADD-VIEW.CONTENT-ISSUE' | translate }} {{ formCtrlBody.errors.minChars }} {{ 'ISSUE-ADD-VIEW.CHARACTERS' | translate }}
                    </div>
                </mat-error>
                <mat-toolbar class="editor-toolbar mat-elevation-z0">
                    <ng-container #fileUploaderContainer></ng-container>
                    <div fxFlex></div>
                    <ng-container *ngIf="!sending">
                        <button
                            mat-stroked-button
                            class="button-rounded primary"
                            color="primary"
                            [disabled]="!newIssueForm.valid || isUploading">
                                <mat-icon>send</mat-icon>
                                {{ 'ISSUE-ADD-VIEW.SEND' | translate }}
                        </button>
                    </ng-container>
                    <ng-container *ngIf="sending">
                        <button
                            mat-stroked-button
                            color="primary"
                            type="button"
                            class="button-rounded primary">
                                <mat-spinner diameter="20" color="primary" [ngStyle]="{display: 'inline-block', 'margin-right': '4px'}"></mat-spinner>
                                {{'ISSUE-ACTION-BUTTON.WAIT' | translate}}
                        </button>
                    </ng-container>
                </mat-toolbar>
            </div>
        </div>
    </form>
</div>
