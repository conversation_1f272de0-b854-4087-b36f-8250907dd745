import Quill from 'quill';
import InlineBlot from 'quill/blots/inline';

const Inline = Quill.import('blots/inline');

export class CommentBlot extends Inline {
    static create(value) {
        const node = super.create();
        // Tylko dla komentarzy ustawiamy specjalne atrybuty
        if (value && value.toString().startsWith('group')) {
            node.setAttribute('href', 'javascript:void(0)');
            node.setAttribute('id', value);
            node.classList.add('comment');
        }
        return node;
    }

    static formats(node) {
        return node.getAttribute('id') || true;
    }

    format(name, value) {
        if (name === 'comment' && value) {
            (this as any).domNode.setAttribute('id', value);
            (this as any).domNode.classList.add('comment');
            // Dla komentarzy zawsze ustawiamy href="javascript:void(0)"
            if (value.toString().startsWith('group')) {
                (this as any).domNode.setAttribute('href', 'javascript:void(0)');
            }
        } else {
            super.format(name, value);
        }
    }
}

(CommentBlot as any).blotName = 'comment';
(CommentBlot as any).tagName = 'a';

Quill.register('formats/comment', CommentBlot);
