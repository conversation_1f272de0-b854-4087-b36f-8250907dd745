<div class="editor" [ngClass]="(isHandset$ | async) ? 'is-mobile' : ''">
    <div class="editor-header" *ngIf="isSimplifiedView && isUnacceptedPage">
        <app-user-avatar-display
                class="settings-avatar"
                [size]="28"
                [userId]="+issueData.owner_id">
        </app-user-avatar-display>
        <span class="cursor-pointer">
            {{ issueOwnerName }}
        </span>
    </div>
    <div class="editor-content" fxLayout="column" fxLayoutAlign="start" fxFill>
        <div fxFlex>
            <div class="quill" fxFlex [ngClass]="(expandedEditor$ | async) ? 'expanded-editor' : ''">
                <quill-editor
                    placeholder="{{'ISSUE-EDITOR.START-WRITING' | translate}}"
                    [(ngModel)]="actualHtml"
                    [readOnly]="disabled || sending"
                    [bounds]="'.quill'"
                    (onContentChanged)="onContentChanged($event)"
                    (onEditorCreated)="onEditorCreated($event)"
                    (onSelectionChanged)="onSelectionChanged()"
                    [modules]="editorModules"
                    [formats]="formats"
                    (click)="onEditorClick($event)"
                    (onBlur)="clickingOutsideTheEditor()"
                    (paste)="onPaste($event)"
                    (keyup)="onKeyUp()"
                    (keydown.control.s)="onShortcutSave($event)"
                >
                    <div quill-editor-toolbar (click)="onEditorToolbarClick($event)">
                        <span class="ql-formats">
                            <button class="ql-bold" title="{{'ISSUE-EDITOR.BOLD' | translate}}"></button>
                            <button class="ql-italic" title="{{'ISSUE-EDITOR.ITALICS' | translate}}"></button>
                            <button class="ql-underline" title="{{'ISSUE-EDITOR.UNDERLINE' | translate}}"></button>
                            <button class="ql-strike" title="{{'ISSUE-EDITOR.CROSSING' | translate}}"></button>
                        </span>
                        <span class="ql-formats">
                            <button class="ql-list" value="bullet" title="{{'ISSUE-EDITOR.BULLET' | translate}}"></button>
                            <button class="ql-list" value="ordered" title="{{'ISSUE-EDITOR.NUMBERED' | translate}}"></button>
                            <button class="ql-link" title="{{'ISSUE-EDITOR.LINK' | translate}}"></button>
                            <button class="ql-image" title="{{'ISSUE-EDITOR.IMAGE' | translate}}"></button>
                        </span>
                        <span class="ql-formats">
                            <button (click)="commentDialog($event)" id="new-comment-button" title="{{'ISSUE-EDITOR.ADD-COMMENTS' | translate}}"><mat-icon>add_comment</mat-icon></button>
                        </span>
                        <span class="ql-formats" *ngIf="!disabled && issueActionAllowedService.isActionAllowed('useTemplate', issueData)">
                            <button (click)="showTemplates()" id="use-template-button" title="{{'ISSUE-EDITOR.USE-TEMPLATE' | translate}}"><mat-icon>library_books</mat-icon></button>
                        </span>
                    </div>
                </quill-editor>
            </div>
        </div>

        <div fxFlexAlign="end" fxFlex="65px">
            <mat-toolbar>
                <button
                    *ngIf="!disabled"
                    class="save-icon"
                    mat-icon-button
                    #draftTooltip="matTooltip"
                    matTooltipPosition="after"
                    [matTooltip]="saveTooltipName(savedChanges)"
                    (click)="saveDraft()"
                    [disabled]="savedChanges || !actualHtml"
                >
                    <mat-icon>save</mat-icon>
                </button>

                <ng-container #fileUploaderContainer></ng-container>

                <span fxFlex></span>

                <ng-container *ngIf="issueActionAllowedService.isActionAllowed('acceptIssue', this.issueData); else navigation">
                    <div *ngIf="!(isHandset$ | async); else mobileButtons">
                        <div class="button-row">
                            <button mat-stroked-button
                                    class="button-rounded primary"
                                    color="primary" (click)="reject()">
                                <i-lucide class="icon" name="reply"></i-lucide>
                                {{'ISSUE-EDITOR.REJECT' | translate}}
                            </button>
                            <button mat-stroked-button
                                    class="button-rounded primary"
                                    color="primary" (click)="acceptAndSend(true)">
                                <i-lucide class="icon" name="check"></i-lucide>
                                {{'ISSUE-EDITOR.AMENDMENT' | translate}}
                            </button>
                            <button mat-stroked-button
                                    class="button-rounded primary"
                                    color="primary" (click)="acceptAndSend(false)">
                                <i-lucide class="icon" name="send"></i-lucide>
                                {{'ISSUE-EDITOR.ACCEPT' | translate}}
                            </button>
                        </div>
                    </div>

                    <ng-template #mobileButtons>
                        <div class="button-row">
                            <button mat-mini-fab (click)="reject()">
                                <mat-icon>reply</mat-icon>
                            </button>
                            <button mat-mini-fab color="primary" (click)="acceptAndSend(true)">
                                <mat-icon>done</mat-icon>
                            </button>
                            <button mat-mini-fab color="primary" (click)="acceptAndSend(false)">
                                <mat-icon>send</mat-icon>
                            </button>
                        </div>
                    </ng-template>
                </ng-container>

                <ng-template #navigation>
                    <div class="button-row">
                        <ng-container *ngIf="'userGroups' | checkPermissionName">
                            <span matTooltip="{{'ISSUE-EDITOR.CHECK' | translate}}">
                                <app-issue-action-button
                                        actionType="sendToAccept"
                                        [issue]="issueData"
                                        [disabled]="isSaving || !actualHtml"
                                        [sending]="sendingToReview"
                                        (buttonClicked)="sendToReview()"
                                ></app-issue-action-button>
                            </span>
                        </ng-container>

                        <app-issue-action-button
                            actionType="sendIssue"
                            [issue]="issueData"
                            [sending]="sending"
                            [disabled]="isSaving || !actualHtml"
                            (buttonClicked)="send()"
                        ></app-issue-action-button>

                        <span class="mat-small issue-waiting" *ngIf="issueData.status==='unaccepted'"> {{'ISSUE-EDITOR.ISSUE-WAITING' | translate}} </span>

                        <ng-container *ngIf="'userGroups' | checkPermissionName">
                            <app-issue-action-button
                                actionType="undoSendToAccept"
                                [issue]="issueData"
                                [sending]="sending"
                                (buttonClicked)="undoSendToReview()"
                            ></app-issue-action-button>
                        </ng-container>
                    </div>
                </ng-template>
            </mat-toolbar>
        </div>
    </div>
</div>
