import {
    After<PERSON>iewInit,
    Component,
    ElementRef,
    EventEmitter,
    HostListener,
    inject,
    Inject,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    Renderer2,
    SecurityContext,
    SimpleChanges,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import {AsyncPipe, DOCUMENT, NgClass, NgIf} from '@angular/common';
import {MatDialog} from '@angular/material/dialog';
import {MatTooltip} from '@angular/material/tooltip';
import {DomSanitizer} from '@angular/platform-browser';
import {IssueSentenceService} from '../../services/issue-sentence.service';
import {IssueInterface} from '../../common/interfaces/issue.interface';
import {concatMap, concatMapTo, debounceTime, filter, first, map, shareReplay, switchMap, switchMapTo, take, tap} from 'rxjs/operators';
import {Observable, of, Subject, Subscription} from 'rxjs';
import {IssueService} from '../../services/issue.service';
import {CommentService} from '../../services/comment.service';
import {AuthService} from '../../services/auth/auth.service';
import {IssueCommentDialogComponent, NewCommentDialogInterface} from '../issue-comment-dialog/issue-comment-dialog.component';
import {CommentBlot} from './quill-comment';
import Quill from 'quill';
import {IssueActionAllowedService} from '../../services/issue-action-allowed.service';
import {IsMobileService} from '../../services/is-mobile.service';
import {TemplateFileUploaderComponent} from '../../shared/template-file-uploader/template-file-uploader.component';
import {AttachmentsManagementService} from '../../services/attachments-management.service';
import {IssueChecklistService} from '../../services/issue-checklist.service';
import {IssueEditorTemplateComponent} from '../issue-editor-template/issue-editor-template.component';
import {activateLinks} from '../../common/utils/activate-links';
import {SocketIssueLockService} from '../../services/sockets/socket-issue-lock.service';
import {SocketIssueSentencesService} from '../../services/sockets/socket-issue-sentences.service';
import {FileInputCreationService} from '../../services/file-input-creation.service';
import {IssuePaymentService} from '../../services/issue-payment.service';
import {ConfirmDialogComponent} from '../../shared/confirm-dialog/confirm-dialog.component';
import {PermissionService} from '../../services/permission.service';
import {UserInputStorageService} from '../../services/user-input-storage.service';
import {ActivatedRoute, NavigationStart, Router} from '@angular/router';
import {IssueInitiatorService} from '../../services/issue-initiator.service';
import * as _ from 'underscore';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {environment} from '../../../environments/environment';
import {DialogService} from '../../services/dialog-details.service';
import {UserService} from '../../services/user.service';
import {DefaultClassDirective} from 'ngx-flexible-layout/extended';
import {UserAvatarDisplayComponent} from '../../shared/user-avatar-display/user-avatar-display.component';
import {DefaultFlexAlignDirective, DefaultFlexDirective, DefaultLayoutAlignDirective, DefaultLayoutDirective, FlexFillDirective} from 'ngx-flexible-layout/flex';
import {QuillEditorComponent} from 'ngx-quill';
import {FormsModule} from '@angular/forms';
import {MatIcon} from '@angular/material/icon';
import {MatToolbar} from '@angular/material/toolbar';
import {MatButton, MatIconButton, MatMiniFabButton} from '@angular/material/button';
import {IssueActionButtonComponent} from '../issue-action-button/issue-action-button.component';
import {CheckPermissionNamePipe} from '../../shared/pipes/check-permission-name.pipe';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';
import {LucideAngularModule} from 'lucide-angular';
const Parchment = Quill.import('parchment');

@Component({
    selector: 'app-issue-editor',
    templateUrl: './issue-editor.component.html',
    styleUrls: ['./issue-editor.component.scss'],
    imports: [NgClass, DefaultClassDirective, NgIf, UserAvatarDisplayComponent, DefaultLayoutDirective, DefaultLayoutAlignDirective, FlexFillDirective, DefaultFlexDirective, QuillEditorComponent, FormsModule, MatIcon, DefaultFlexAlignDirective, MatToolbar, MatIconButton, MatTooltip, MatButton, MatMiniFabButton, IssueActionButtonComponent, AsyncPipe, CheckPermissionNamePipe, TranslatePipe, LucideAngularModule]
})
export class IssueEditorComponent implements OnChanges, OnDestroy, OnInit, AfterViewInit {
    // TODO Przenieść do serwisu settingśów
    SAVE_TO_DRAFT_TIMER = 10000;
    DRAFT_TOOLTIP_HIDE_AFTER = 1000;

    @Input()
    issueData: IssueInterface;

    @Input()
    showSplitEditor: boolean;

    @Input()
    lastSentence;

    @Input()
    disabled = false;

    @Input()
    isSimplifiedView = false;

    @Input()
    isEditorOpen: boolean;

    @Output()
    isEditorOpenChange = new EventEmitter<boolean>();

    @Output()
    isCommentSidebarOpen: EventEmitter<boolean> = new EventEmitter();

    @Output()
    issueLocked: EventEmitter<boolean> = new EventEmitter();

    @ViewChild('fileUploaderContainer', {read: ViewContainerRef, static: true})
    fileUploaderContainer: ViewContainerRef;

    @ViewChild('draftTooltip')
    draftTooltip: MatTooltip;

    Quill: any; // instancja Quill dla API

    actualHtml;
    templateId;
    expandedEditor$ = this.issueService.expandedEditor;
    expandedFullEditor$ = this.issueService.expandedFullEditor;
    userId;
    savedChanges = true;
    lastSentenceId;
    timeoutHandler;
    commentFormatInit = false;
    sending = false;
    sendingToReview = false;
    cursorPosition = null;
    fileUploaderComponentInstance: TemplateFileUploaderComponent;
    fileUploadingStatusSubscription: Subscription;
    isSaving = false;
    contentChangedByMe = false;
    lastSavedContent = '';
    autoSave = true;
    issueOwnerName: string;
    isUnacceptedPage: boolean = false;

    editorModules = {
        toolbar: {
            container: [
                ['bold', 'italic', 'underline', 'strike'],
                [{'color': []}, {'background': []}],
                [{'list': 'ordered'}, {'list': 'bullet'}],
                [{'align': []}],
                ['link', 'image'],
                ['clean']
            ]
        },
        clipboard: {
            matchVisual: false
        }
    };

    formats = [
        'bold', 'italic', 'underline', 'strike',
        'color', 'background',
        'list', 'bullet',
        'align',
        'link', 'image',
        'comment'
    ];

    newComment: NewCommentDialogInterface = {
        body: ''
    };
    issueCommentSelectedGroupSubscription: Subscription;
    isHandset$: Observable<boolean>;
    isIntern: boolean;
    checklistChecked = true;
    subscription = new Subscription();
    private createFileInputComponentSubject = new Subject();
    private createFileInputComponent$ = this.createFileInputComponentSubject.pipe(debounceTime(700));
    private userCache: { [key: number]: Observable<any> } = {};
    isSend = false;

    @HostListener('window:beforeunload', ['$event'])
    blockFunction($event: any) {
        return this.savedChanges;
    }

    /**
     * w przypadku zamknięcia lub przeładowania strony wykonujemy oczyszczenie blokady.
     * @param event
     */
    @HostListener('window:unload', [ '$event' ])
    unloadHandler(event) {
        this.issueLocked.emit(false);
    }

    private alertService: AlertService = inject(AlertService);

    constructor(
        private issueService: IssueService,
        private issueSentenceService: IssueSentenceService,
        private commentService: CommentService,
        private authService: AuthService,
        public dialog: MatDialog,
        private renderer: Renderer2,
        private el: ElementRef,
        public issueActionAllowedService: IssueActionAllowedService,
        private isMobileService: IsMobileService,
        private attachmentsManagementService: AttachmentsManagementService,
        public issueChecklistService: IssueChecklistService,
        private _socketIssueLockService: SocketIssueLockService,
        private socketIssueSentencesService: SocketIssueSentencesService,
        private fileInputCreationService: FileInputCreationService,
        private domSanitizer: DomSanitizer,
        private issuePaymentService: IssuePaymentService,
        private permissionService: PermissionService,
        public userInputStorageService: UserInputStorageService,
        private router: Router,
        private route: ActivatedRoute,
        private _issueInitiatorService: IssueInitiatorService,
        public translate: TranslateService,
        @Inject(DOCUMENT) private document: Document,
        private dialogService: DialogService,
        private user: UserService
    ) {
        this.isHandset$ = this.isMobileService.isMobileView;
        this.isIntern = !issueActionAllowedService.isSpecialistOrExpert() && environment.internalMode;
    }

    ngOnInit() {
        this.subscription.add(
            this.router.events.subscribe((routerEvent) => {
                if (routerEvent instanceof NavigationStart) {
                    this.ngOnDestroy();
                }
            })
        );

        this.subscription.add(
            this.route.data.subscribe(data => {
                this.isUnacceptedPage = data.name === 'unaccepted';
            })
        );
    }

    async ngAfterViewInit(): Promise<void> {
        this.userId = this.authService.getUserId();
        this.expandedFullEditor$.next(false);

        this.setSocketSubscription();
        this.setCreateFileInputSubscription();
        this.createFileInputComponent();
    }

    ngOnDestroy() {
        this.issueLocked.emit(false);
        if (this.issueCommentSelectedGroupSubscription) {
            this.issueCommentSelectedGroupSubscription?.unsubscribe();
        }

        this.subscription?.unsubscribe();

        if (this.fileUploadingStatusSubscription) {
            this.fileUploadingStatusSubscription?.unsubscribe();
        }

        this.saveDraft();
        this.actualHtml = '';
    }

    ngOnChanges(changes: SimpleChanges) {
        let refreshFileInputComponent = false;
        const previousBody = _.chain(changes['lastSentence'])
            .get('previousValue')
            .get('IssueSentence')
            .get('body')
            .value();
        const currentValue = _.chain(changes['lastSentence'])
            .get('currentValue')
            .get('IssueSentence')
            .get('body')
            .value();

        if (!_.isEqual(currentValue, previousBody)) {
            refreshFileInputComponent = true;

            if (this.lastSentence) {
                this.lastSentenceId = this.lastSentence.IssueSentence.id;

                this.actualHtml = '';
                this._updateEditorContent();
            } else {
                this.lastSentenceId = null;
                this.actualHtml = '';
                this._updateEditorContent();
            }
        }

        if (changes['disabled']) {
            refreshFileInputComponent = true;
        }

        if (refreshFileInputComponent) {
            this.createFileInputComponent();
        }

        if (changes['issueData'] && +this.issueData?.owner_id) {
            this.getUserWithCache(this.issueData.owner_id)
                .pipe(take(1))
                .subscribe(user => {
                    this.issueOwnerName = user.firstname + ' ' + user.lastname;
                });
        }
    }

    private getUserWithCache(ownerId: number): Observable<any> {
        if (!this.userCache[ownerId]) {
            this.userCache[ownerId] = this.user.getUser(ownerId).pipe(
                shareReplay(1)
            );
        }
        return this.userCache[ownerId];
    }

    toggleEditor() {
        this.isEditorOpen = !this.isEditorOpen;
        this.isEditorOpenChange.emit(this.isEditorOpen);
    }

    private setCursorPosition() {
        if (this.Quill.getSelection()?.index !== 0) {
            this.cursorPosition = this.Quill.getSelection();
        }
    }

    private getCursorPosition() {
        return this.cursorPosition;
    }

    private clearBody(body: string) {
        if (!body || body.length === 0) {
            return body;
        }

        return body.replace(/(<([^>]+)>)/gi, '');
    }

    private handleError(message: string, err) {
        this.alertService.showAlert(message, AlertType.ERROR, AlertDuration.MEDIUM);
    }

    private hasContentChanged(currentContent: string, lastSavedContent: string) {
        return currentContent !== lastSavedContent;
    }

    private issueHasOwner() {
        return +this.issueData.owner_id !== 0;
    }

    private markMyLastChangedIssueSentenceId() {
        if (this.lastSentenceId) {
            this.socketIssueSentencesService.myLastChangedIssueSentenceId = this.lastSentenceId;
        }
    }

    private setSocketSubscription() {
        this.subscription.add(
            this._socketIssueLockService.issueLockSubject.pipe(
                filter(data => +data.issueId === +this.issueData.id),
                filter(data => data.type === 'lock')
            ).subscribe()
        );
    }

    private setUploadingStatusSubscription() {
        if (this.fileUploadingStatusSubscription) {
            this.fileUploadingStatusSubscription?.unsubscribe();
        }

        this.fileUploadingStatusSubscription = this.attachmentsManagementService.status
            .pipe(
                map(status => status.toLowerCase().includes('error') ? 'error' : status)
            )
            .subscribe((status: string) => {
                switch (status) {
                    case 'error':
                        this.createFileInputComponent();

                        break;

                    case 'uploadingStart':
                        this.isSaving = true;

                        break;

                    case 'uploadingEnd':
                        this.isSaving = false;

                        break;
                }
            });
    }

    private _createFileInputComponent() {
        this.fileUploaderComponentInstance = this.fileInputCreationService.createFileInputComponent(
            this.fileUploaderContainer, !this.disabled && this.actualHtml, 'issueSentence', this.lastSentenceId, +this.issueData.user_client_id
        );
        this.fileUploaderComponentInstance.userType = 'isExpert';
        this.setUploadingStatusSubscription();
    }

    private createFileInputComponent() {
        this.createFileInputComponentSubject.next(null);
    }

    private setCreateFileInputSubscription() {
        this.subscription.add(
            this.createFileInputComponent$.subscribe(() => this._createFileInputComponent())
        );
    }

    private showSaveTooltip() {
        if (!this.draftTooltip) {
            return;
        }

        this.draftTooltip.hide();

        setTimeout(() => {
            if (!this.draftTooltip || !this.el.nativeElement.isConnected) {
                return;
            }

            this.draftTooltip.show();
            setTimeout(() => this.draftTooltip.hide(), this.DRAFT_TOOLTIP_HIDE_AFTER);
        }, 0);
    }

    private saveDraftAndAttachFiles(filesToPaste: any[]) {
        const addIssueSentence$ = this.issueSentenceService.addIssueSentence(
                this.issueData.id,
                +this.issueData.owner_id ? this.issueData.owner_id : this.userId,
                'draft',
                this.actualHtml,
                'owner',
                this.templateId,
                +this.issueData.user_client_id
            ),
            updateIssueSentence$ = this.issueSentenceService.updateIssueSentence(this.lastSentenceId, {body: this.actualHtml, template_id: this.templateId}),
            saveDraft$ = this.lastSentenceId ? updateIssueSentence$ : addIssueSentence$;

        this.attachmentsManagementService.removeAllFiles$
            .pipe(
                concatMapTo(saveDraft$),
                concatMap(response => {
                    if (response.id) {
                        this.lastSentenceId = response.id;
                    }

                    return filesToPaste.length
                        ? this.issueSentenceService.addAttachmentsFromTemplatesByFileIds(this.lastSentenceId, filesToPaste)
                        : of(null);
                })
            )
            .subscribe(
                () => {
                    this.savedChanges = true;
                    this.isSaving = false;
                    this.createFileInputComponent();
                },
                err => {
                    this.createFileInputComponent();
                    this.isSaving = false;
                    this.handleError(this.translate.instant('ISSUE-EDITOR.ERROR'), err);
                }
            );
    }


    private getAddIssueSentence$(status: 'sent' | 'draft' | 'unaccepted') {
        return this.issueSentenceService.addIssueSentence(this.issueData.id, this.userId, status, this.actualHtml, 'owner', null, +this.issueData.user_client_id).pipe(
            map(resp => {
                if (resp && resp.id) {
                    return resp.id;
                } else {
                    throw new Error(this.translate.instant('ISSUE-EDITOR.ERROR-SERVER'));
                }
            }),
            tap(newSentenceId => {
                this.lastSentenceId = newSentenceId;
                this.createFileInputComponent();
            })
        );
    }

    private getUpdateIssueSentence$(data) {
        return this.issueSentenceService.updateIssueSentence(this.lastSentenceId, data).pipe(
            map(resp => {
                if (resp && resp.status === 'OK') {
                    return resp;
                } else {
                    throw new Error(this.translate.instant('ISSUE-EDITOR.ERROR-SERVER'));
                }
            })
        );
    }

    private getSendObservable(sendStatus: 'sent' | 'unaccepted') {
        // Jeżeli sprawa nie ma właściciela to go ustawiamy
        const start$ = this.issueHasOwner() ? of('') : this.issueService.setOwnership(this.issueData.id, this.userId);

        return start$.pipe(
            switchMap(() => {
                // Jeżeli nie ma drafta to go dodajemy (z odpowiednim statusem (w tym ze statusem 'sent', przy wysyłce do klienta))
                if (!this.lastSentenceId) {
                    return this.getAddIssueSentence$(sendStatus);
                }

                // Jeśli treść została zmieniona to trzeba najpierw zapisać draft żeby serwer dodał odpowiednie logi, a dopiero potem wysłać
                if (this.hasContentChanged(this.actualHtml, this.lastSavedContent)) {
                    return this.getUpdateIssueSentence$({status: 'draft', body: this.actualHtml})
                        .pipe(
                            switchMap(() => this.getUpdateIssueSentence$({status: sendStatus}))
                        );
                }

                // Jeżeli treść nie została zmieniona to wystarczy zmienić status na sent - wysyłającym zostaje ten kto zapisał draft odpowiedzi
                return this.getUpdateIssueSentence$({status: sendStatus, body: this.actualHtml});
            })
        );
    }

    private resetSaveTimer() {
        if (!this.isContentReadyForSaving(this.actualHtml, this.lastSavedContent)) {
            return;
        }

        this.savedChanges = false;

        if (this.autoSave) {
            clearTimeout(this.timeoutHandler);
            this.timeoutHandler = setTimeout(() => this.saveDraft(), this.SAVE_TO_DRAFT_TIMER);
        }
    }

    private resetSavingFlags() {
        this.savedChanges = true;
        this.sending = false;
        this.sendingToReview = false;
        this.contentChangedByMe = false;
    }

    private unlockIssue() {
        this.issueLocked.emit(false);
    }

    private checkPaymentStatus() {
        if (this.permissionService.checkPermission('valuateIssuePayment')) {
            this.checkIssuePaymentOffer()
                .pipe(filter(res => res.length > 0), switchMap(() => this.openConfirmPaymentOffer()))
                .subscribe(() => {
                    this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.QUOTATION'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                });
        }
    }

    private openConfirmPaymentOffer() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '400px',
                data: {
                    header: this.translate.instant('ISSUE-EDITOR.VALUATION')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => !!result));

        return confirm$.pipe(switchMapTo(this.getUpdateIssueSentence$({object_name: 'IssuePaymentOffer'})));
    }

    private checkIssuePaymentOffer() {
        return +this.issueData.offer_send ? of([]) : this.issuePaymentService.getIssuePaymentOffers(this.issueData.id);
    }

    public isContentReadyForSaving(currentContent: string, lastSavedContent: string) {
        return this.contentChangedByMe && currentContent && this.hasContentChanged(currentContent, lastSavedContent);
    }

    private _updateEditorContent(): void {
        const _lastSentence = _.chain(this.lastSentence)
            .get('IssueSentence')
            .get('body')
            .value();

        if (!_.isEqual(this.clearBody(this.actualHtml), this.clearBody(_lastSentence)) && !this.isSend) {
            if (_.isEmpty(this.actualHtml)) {
                this.actualHtml = _lastSentence;
            } else {
                const _actualHtmlCache = this.actualHtml;
                this.actualHtml = this._replaceContentBodyHelper(_lastSentence, _actualHtmlCache);
            }
            this.lastSavedContent = this.actualHtml;
        }

        this.isSend = false;
    }

    private _replaceContentBodyHelper(sentence: string, searchInString: string): string {
        if (sentence.includes(searchInString)) {
            const slimming = sentence.replace(searchInString, '');
            return slimming + searchInString;
        } else {
            return sentence + searchInString;
        }
    }

    pasteFromTemplate(obj) {
        this.actualHtml = obj.text;
        this.templateId = obj.id;

        const filesToPaste = obj.files.length ? obj.files : [];

        this.saveDraftAndAttachFiles(filesToPaste);
    }

    onEditorCreated(instance) {
        this.Quill = instance;

        // Obsługa wklejania
        this.Quill.root.addEventListener('paste', (e: ClipboardEvent) => {
            if (e.clipboardData) {
                e.preventDefault();

                const text = e.clipboardData.getData('text/plain');

                if (!text) return;

                const selection = this.Quill.getSelection(true); // true wymusza fokus
                const insertIndex = selection ? selection.index : this.Quill.getLength() - 1;
                console.log('Pozycja kursora:', selection, 'insertIndex:', insertIndex);

                const urlRegex = /^https?:\/\/[^\s]+$/;
                const isLink = urlRegex.test(text.trim());

                if (isLink) {
                    this.Quill.insertText(insertIndex, text, 'user');
                    this.Quill.formatText(insertIndex, text.length, 'link', text, 'user');
                    this.Quill.insertText(insertIndex + text.length, ' ', 'user');
                } else {
                    this.Quill.insertText(insertIndex, text, 'user');
                }

                // Ustaw kursor na końcu całego tekstu w edytorze (po wklejeniu)
                // Zapisz pozycję końca tekstu do cursorPosition i użyj getCursorPosition
                this.cursorPosition = { index: this.Quill.getLength() - 1, length: 0 };
                console.log('Ustawiam cursorPosition na:', this.cursorPosition);

                setTimeout(() => {
                    this.Quill.setSelection(this.getCursorPosition());
                    console.log('Użyłem getCursorPosition():', this.getCursorPosition());
                    this.Quill.focus();
                }, 0);
            }
        });

        this.Quill.root.addEventListener('click', (event) => {
            const target = event.target as HTMLElement;

            const isComment = target.classList.contains('comment') || target.closest('.comment');

            if (!isComment && !this.disabled && target.tagName === 'A') {
                const href = target.getAttribute('href');

                if (href) {
                    event.preventDefault();
                    window.open(href, '_blank');
                }
                return;
            }

            const comment = Parchment.find(target);

            if (comment instanceof CommentBlot) {
                const groupId = event.target.id.match(/group([0-9]*)/)[1];

                this.isCommentSidebarOpen.emit(true);

                this.commentService.selectGroupComments(groupId);

                this.commentService.selectCommentGroup(groupId, this.el, this.renderer, () => this.isCommentSidebarOpen.emit(true));
            }
        });

        this.issueCommentSelectedGroupSubscription = this.commentService.selectGroup.subscribe(groupId => {
            const commentClass: any = this.el.nativeElement.querySelectorAll('.comment');

            if (!groupId) {
                return;
            }

            this.commentService.selectCommentGroup(groupId, this.el, this.renderer, () => this.isCommentSidebarOpen.emit(true));
        });
    }

    onContentChanged(event) {
        if (event.source === 'api' && !this.commentFormatInit) {
            return this.commentFormatInit = true;
        }

        if (event.source === 'api') {
            this.actualHtml = event.html;
            this.saveDraft();
        }

        this.issueCommentSelectedGroupSubscription?.unsubscribe();
        this.issueCommentSelectedGroupSubscription = this.commentService.selectGroup.subscribe(groupId => {
            const commentClass: any = this.el.nativeElement.querySelectorAll('.comment');

            if (!groupId) {
                return;
            }

            this.commentService.selectCommentGroup(groupId, this.el, this.renderer, () => this.isCommentSidebarOpen.emit(true));
        });
    }

    /**
     * Wykonanie po klinięciu poza edytor.
     * rezygnujemy w tym przypadku z edycji i odblokowujemy pole
     */
    clickingOutsideTheEditor(): void {
        this.saveDraft();

        _.delay(() => {
            this.issueLocked.emit(false);
        }, 1000);
    }

    onEditorClick(event: MouseEvent) {
        event.stopImmediatePropagation();

        const target = event.target as HTMLElement;

        if (this.disabled && target.tagName === 'A') {
            return;
        }

        if (!this.disabled) {
            this.issueLocked.emit(true);
        } else {
            event.preventDefault();
        }
    }

    onEditorToolbarClick(event) {
        event.stopImmediatePropagation();
    }

    onSelectionChanged() {
        if (this.Quill.hasFocus()) {
            this.issueService.setExpandedEditor(true);
        }
    }

    sendToReview() {
        clearTimeout(this.timeoutHandler);
        this.sendingToReview = true;

        this.getSendObservable('unaccepted').subscribe(
            () => {
                this.resetSavingFlags();
                this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.APPROVAL'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.unlockIssue();
            },
            err => {
                this.resetSavingFlags();
                this.handleError(this.translate.instant('ISSUE-EDITOR.APPROVAL-ACCEPT'), err);
            }
        );
    }

    send() {
        clearTimeout(this.timeoutHandler);
        this.sending = true;

        this.getSendObservable('sent').subscribe(
            () => {
                this.resetSavingFlags();
                this.commentFormatInit = false;
                this.lastSentenceId = null;
                this.issueService.setExpandedEditor(false);
                this.createFileInputComponent();
                this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.SENT-CLIENT'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.toggleEditor();
                this.unlockIssue();
                this.isSend = true;
            },
            err => {
                this.resetSavingFlags();
                this.handleError(this.translate.instant('ISSUE-EDITOR.SENT-CLIENT-ERROR'), err);
            },
            () => {
                this.unlockIssue();
                this.actualHtml = '';
                this.isSend = true;
                setTimeout(() => {
                    this.dialogService.closeDialog();
                }, 2000);
            }
        );

        this.checkPaymentStatus();
    }

    acceptAndSend(corrected: boolean) {
        clearTimeout(this.timeoutHandler);
        this.sending = true;

        const sendData = corrected
            ? {status: 'sent', body: this.actualHtml, corrected: 1}
            : {status: 'sent', body: this.actualHtml};

        this.getUpdateIssueSentence$(sendData).subscribe(
            () => {
                this.resetSavingFlags();
                this.commentFormatInit = false;
                this.actualHtml = '';
                this.lastSentenceId = null;
                this.issueService.setExpandedEditor(false);
                this.createFileInputComponent();
                this.toggleEditor();
                this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.SENT-CLIENT'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.unlockIssue();
            },
            err => {
                this.resetSavingFlags();
                this.handleError(this.translate.instant('ISSUE-EDITOR.SENT-CLIENT'), err);
            },
            () => {
                setTimeout(() => {
                    this.dialogService.closeDialog();
                }, 2000);
            }
        );

        this.checkPaymentStatus();
    }

    undoSendToReview() {
        this.sending = true;

        this.getUpdateIssueSentence$({status: 'draft'}).subscribe(
            () => {
                this.resetSavingFlags();
                this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.DISPATCH'), AlertType.SUCCESS, AlertDuration.MEDIUM);
            },
            err => {
                this.resetSavingFlags();
                this.handleError(this.translate.instant('ISSUE-EDITOR.DISPATCH-ERROR'), err);
            }
        );
    }

    reject() {
        clearTimeout(this.timeoutHandler);
        this.sending = true;

        this.getUpdateIssueSentence$({body: this.actualHtml, status: 'draft', rejected: +this.lastSentence.IssueSentence.rejected + 1}).subscribe(
            () => {
                this.resetSavingFlags();
                this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.REJECT-RESPONSE'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.unlockIssue();
            },
            err => {
                this.sending = false;
                this.handleError(this.translate.instant('ISSUE-EDITOR.REJECT-RESPONSE-ERROR'), err);
            },
            () => {
                this.dialogService.closeDialog();
            }
        );
    }

    /**
     * zapis kopii roboczej
     * return bool wykonuje się tylko w przypadku warunku na początku, funkcja nie zwraca żadnych parametrów.
     * warunek ma za zadanie wyjść z funckcji w przypadku gdy if jest true.
     */
    saveDraft(): boolean|void {
        this.setCursorPosition();
        clearTimeout(this.timeoutHandler);

        if (!this.isContentReadyForSaving(this.actualHtml, this.lastSavedContent)) {
            return this.savedChanges = true;
        }

        this.sending = true;
        this.markMyLastChangedIssueSentenceId();

        const saveDraft$ = this.lastSentenceId ? this.getUpdateIssueSentence$({body: this.actualHtml}) : this.getAddIssueSentence$('draft');

        saveDraft$.subscribe(
            () => {
                this.lastSavedContent = this.actualHtml;

                const activeElement = this.document.activeElement;

                if (activeElement && this.cursorPosition && (activeElement.tagName.toLowerCase() === 'body' || activeElement.classList.contains('ql-editor'))) {
                    setTimeout(() => {
                        this.Quill.setSelection(this.getCursorPosition());
                    }, 600);
                }

                this.resetSavingFlags();
                this.showSaveTooltip();
            },
            err => {
                this.resetSavingFlags();
                this.handleError(this.translate.instant('ISSUE-EDITOR.SAVE-DRAFT'), err);
            }
        );
    }

    addCommentGroup(): void {
        this.commentService.setCommentGroup(this.issueData.id, this.authService.getUserId())
            .pipe(first())
            .subscribe({
                next: (groupId) => {
                    this.addComment(groupId);
                },
                error: (err) => {
                    console.error(err);
                    this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.ERROR-COMMENTS'), AlertType.ERROR, AlertDuration.MEDIUM);
                },
                complete: () => {
                    this.unlockIssue();
                }
            });
    }

    /**
     * tworzenie komentarza, przesłanie odpowiednich danych na backend.
     * @param commentGroupId
     */
    addComment(commentGroupId): void {
        this.contentChangedByMe = true;

        // Upewniamy się, że edytor ma fokus
        this.Quill.focus();

        // Pobieramy zaznaczenie z fokusem
        const range = this.Quill.getSelection(true);
        console.log('Zaznaczenie przed dodaniem komentarza:', range);

        if (!range) {
            console.warn('Brak zaznaczenia tekstu!');
            return;
        }

        this.commentService.addComment(commentGroupId, this.authService.getUserId(), this.newComment.body).pipe(first()).subscribe({
            next: () => {
                this.commentService.refreshComments();

                // Przywracamy fokus i zaznaczenie
                this.Quill.focus();
                console.log('Przywracam zaznaczenie:', range);
                this.Quill.setSelection(range.index, range.length, 'user');

                // Formatujemy zaznaczony tekst jako komentarz
                console.log('Formatowanie komentarza dla grupy:', commentGroupId);
                this.Quill.format('comment', 'group' + commentGroupId, 'user');

                // Sprawdźmy czy formatowanie zostało zastosowane
                const formats = this.Quill.getFormat(range);
                console.log('Zastosowane formaty:', formats);

                // Sprawdźmy zawartość edytora
                const editorContent = this.Quill.root.innerHTML;
                console.log('Zawartość edytora:', editorContent);

                this.isCommentSidebarOpen.emit(true);
                this.commentService.selectGroupComments(commentGroupId);

                this.commentService.selectCommentGroup(commentGroupId, this.el, this.renderer, () => this.isCommentSidebarOpen.emit(true));

                this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.ADD-COMMENT'), AlertType.SUCCESS, AlertDuration.MEDIUM);
            },
            error: (err) => {
                console.error('Błąd podczas dodawania komentarza:', err);
                if (err.statusText === 'Unknown Error') {
                    this.commentService.refreshComments();
                    this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.ADD-COMMENT'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                } else {
                    this.alertService.showAlert(this.translate.instant('ISSUE-EDITOR.ADD-COMMENT-ERROR'), AlertType.ERROR, AlertDuration.MEDIUM);
                }
            },
            complete: () => {
                this.newComment.body = '';
                this.commentFormatInit = true;
            }
        });
    }

    public async commentDialog($event): Promise<void> {
        $event.stopPropagation();
        const dialogRef = this.dialog.open(IssueCommentDialogComponent, {
            width: '400px',
        });
        dialogRef.afterClosed().pipe(
            first()).subscribe({
            next: (value) => {
                if (!_.isEmpty(value)) {
                    this.newComment.body = value;
                    this._issueInitiatorService.refreshIssueData();
                    this.isCommentSidebarOpen.emit(true);

                    requestAnimationFrame(() => {
                        this.addCommentGroup();
                    });
                }
            },
            error: (err) => console.error(err)
        });
    }

    openChecklist(): void {
        this.issueChecklistService.openChecklist.next(true);
    }

    showTemplates(): void {
        this.dialog.open(IssueEditorTemplateComponent, {
            width: '650px',
            height: '550px'
        }).afterClosed().pipe(first()).subscribe((template: any) => {
            if (template) {
                this.pasteFromTemplate(template);
            } else {
                _.noop();
            }
        });
    }

    toggleExpandedFullEditor() {
        const isExpanded = this.expandedFullEditor$.getValue();

        this.expandedFullEditor$.next(!isExpanded);
    }

    onKeyUp() {
        this.contentChangedByMe = true;
        this.resetSaveTimer();
    }

    onShortcutSave(event) {
        event.preventDefault();
        this.saveDraft();
    }

    saveTooltipName(savedChanges) {
        return savedChanges ? this.translate.instant('ISSUE-EDITOR.WORK-COPY-SAVED') : this.translate.instant('ISSUE-EDITOR.WORK-COPY-SAVE');
    }


}
