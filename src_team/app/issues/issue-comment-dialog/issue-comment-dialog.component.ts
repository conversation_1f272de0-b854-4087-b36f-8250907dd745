import {Component, EventEmitter, Inject} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions } from '@angular/material/dialog';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatFormField, MatLabel, MatError } from '@angular/material/select';
import { MatInput } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { MinCharsValidatorDirective } from '../../shared/validators/min-chars-validator.directive';
import { NgIf } from '@angular/common';
import { MatButton } from '@angular/material/button';
import { TranslatePipe } from '@ngx-translate/core';

export interface NewCommentDialogInterface {
    body: string;
}

@Component({
    selector: 'app-issue-comment-dialog',
    templateUrl: './issue-comment-dialog.component.html',
    styleUrls: ['./issue-comment-dialog.component.scss'],
    imports: [MatDialogTitle, MatDialogContent, MatFormField, MatLabel, MatInput, FormsModule, MinCharsValidatorDirective, NgIf, MatError, MatDialogActions, MatButton, TranslatePipe]
})
export class IssueCommentDialogComponent {
    public commentBody: string = '';

    constructor(public dialogRef: MatDialogRef<IssueCommentDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: NewCommentDialogInterface) {
    }

    public cancelAddComment(): void {
        this.dialogRef.close();
    }

    public addComment(): void {
        this.dialogRef.close(this.commentBody);
    }
}
