<h1 mat-dialog-title> {{'ISSUE-COMMENT-DIALOG.NEW-COMMENT' | translate}}</h1>

<div mat-dialog-content id="new-comment-form">
    <mat-form-field focused="true">
        <mat-label>{{'ISSUE-COMMENT-DIALOG.CONTENT' | translate}}"</mat-label>
        <textarea #commentTextArea="ngModel" matInput matTextareaAutosize="true"
                  matAutosizeMinRows="1" placeholder="{{'ISSUE-COMMENT-DIALOG.CONTENT' | translate}}"
                  autofocus="true"
                  [(ngModel)]="commentBody" minChars="3"></textarea>
        <mat-error *ngIf="commentTextArea.invalid && (commentTextArea.touched || commentTextArea.dirty)">
            <mat-error *ngIf="commentTextArea.errors.minChars">{{'ISSUE-COMMENT-DIALOG.FIELD' | translate}} {{commentTextArea.errors.minChars}} {{'ISSUE-COMMENT-DIALOG.CHARS' | translate}}</mat-error>
        </mat-error>
    </mat-form-field>
</div>

<div mat-dialog-actions class="new-comment-actions">
    <button mat-button (click)="cancelAddComment()">{{'ISSUE-COMMENT-DIALOG.CANCEL' | translate}}</button>
    <button mat-button color="primary" [disabled]="commentTextArea.invalid"
            class="test__add-modal-comment"
            (click)="addComment()">{{'ISSUE-COMMENT-DIALOG.ADD' | translate}}</button>
</div>
