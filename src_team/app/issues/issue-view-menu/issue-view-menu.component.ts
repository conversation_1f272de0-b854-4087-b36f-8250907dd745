import {Component, EventEmitter, inject, Input, OnInit, Output} from '@angular/core';
import {Router} from '@angular/router';
import {BreakpointObserver, Breakpoints} from '@angular/cdk/layout';
import {Observable} from 'rxjs';
import {filter, first, map, switchMap, switchMapTo} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {IssueService} from '../../services/issue.service';
import {MatDialog} from '@angular/material/dialog';
import {AuthService} from '../../services/auth/auth.service';
import {ConfirmDialogComponent} from '../../shared/confirm-dialog/confirm-dialog.component';
import {IssueInterface} from '../../common/interfaces/issue.interface';
import {MessagingService} from '../../services/messaging.service';
import {IssueActionAllowedService} from '../../services/issue-action-allowed.service';
import {IssueSuggestModalComponent} from '../issue-suggest-modal/issue-suggest-modal.component';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {environment} from '../../../environments/environment';
import {ApplicationStateService} from '../../services/application-state.service';
import {MatToolbar} from '@angular/material/toolbar';
import {DefaultClassDirective} from 'ngx-flexible-layout/extended';
import {AsyncPipe, NgClass, NgIf} from '@angular/common';
import {MatButton, MatIconButton} from '@angular/material/button';
import {MatTooltip} from '@angular/material/tooltip';
import {MatIcon} from '@angular/material/icon';
import {DefaultLayoutAlignDirective, DefaultLayoutDirective} from 'ngx-flexible-layout/flex';
import {IssueActionButtonDirective} from '../directives/issue-action-button.directive';
import {MatMenu, MatMenuItem, MatMenuTrigger} from '@angular/material/menu';
import {MatSlideToggle} from '@angular/material/slide-toggle';
import {CheckPermissionNamePipe} from '../../shared/pipes/check-permission-name.pipe';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';

@Component({
    selector: 'app-issue-view-menu',
    templateUrl: './issue-view-menu.component.html',
    styleUrls: ['./issue-view-menu.component.scss'],
    imports: [MatToolbar, DefaultClassDirective, NgClass, MatIconButton, MatTooltip, MatIcon, NgIf, DefaultLayoutDirective, DefaultLayoutAlignDirective, MatButton, IssueActionButtonDirective, MatMenu, MatMenuItem, MatMenuTrigger, MatSlideToggle, AsyncPipe, CheckPermissionNamePipe, TranslatePipe]
})
export class IssueViewMenuComponent implements OnInit {
    @Input()
        issueData: IssueInterface;

    @Input()
        initiatorData;

    @Input()
        entryList: string;

    @Input()
        lastSentenceId: number;

    @Output()
        refreshList: EventEmitter<any> = new EventEmitter();

    @Output()
        showLogsChange: EventEmitter<boolean> = new EventEmitter();

    userId;

    isHandset$: Observable<boolean> = this.breakpointObserver.observe([Breakpoints.Handset, Breakpoints.TabletPortrait]).pipe(map(result => result.matches));

    isVerifiedUser = false;

    public suggestCategory: boolean = false;

    isTeam: boolean = false;

    protected readonly environment = environment;

    private alertService: AlertService = inject(AlertService);

    constructor(
        private breakpointObserver: BreakpointObserver,
        private issueService: IssueService,
        private authService: AuthService,
        private router: Router,
        private dialog: MatDialog,
        private messagingService: MessagingService,
        public issueActionAllowedService: IssueActionAllowedService,
        public translate: TranslateService,
        private ApplicationStateService: ApplicationStateService
    ) {
    }

    ngOnInit() {
        const experimentalModeStorage = localStorage.getItem('userSettings.experimental.suggestCategory');

        if (experimentalModeStorage) {
            this.suggestCategory = JSON.parse(experimentalModeStorage);
        }

        this.isTeam = this.ApplicationStateService.getValue('customer')?.account_type === 'team';
        this.userId = this.authService.getUserId();
    }

    clickBack() {
        this.entryList
            ? this.router.navigateByUrl('issue/list/' + this.entryList)
            : this.router.navigateByUrl('issue/list/my');
    }

    public openSuggestCategoryChange(): void {
        this.dialog.open(IssueSuggestModalComponent, {
            width: '500px',
            data: this.issueData
        }).afterClosed()
            .subscribe(change => {
                this.issueService.changeIssueType(this.issueData.id, change)
                    .pipe(
                        first()
                    )
                    .subscribe();
            });
    }


    clickTake() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '400px',
                data: {
                    header: this.translate.instant('ISSUE-VIEW-MENU.TAKE-ISSUE1')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => !!result));

        confirm$.pipe(
            switchMapTo(this.issueService.setOwnership(this.issueData.id, this.userId))
        ).subscribe(result => {
                if (!result || result.status !== 'OK') {
                    return;
                }

                this.refreshList.emit(null);
                this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.ISSUE-TAKEN') + ' ' + '"' +'('  + this.issueData.id + ')' + ' ' + this.issueData.subject + '"', AlertType.SUCCESS, AlertDuration.MEDIUM);
            },
            () => this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.ISSUE-CANNOT-TAKEN') + ' ' + '"' +'('  + this.issueData.id + ')' + ' ' + this.issueData.subject + '"', AlertType.ERROR, AlertDuration.MEDIUM)
        );
    }

    clickDelegate(event) {
        this.issueService.startDelegateMode(this.issueData.id);

        event.stopPropagation();
    }

    clickAddPriority() {
        this.issueService.setPriority(this.issueData.id).subscribe(
            () => {
                this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.PRIORITY-GIVEN') + ' ' + '"' + '('  + this.issueData.issue_internal_id + ')' + ' ' + this.issueData.subject + '"', AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.issueData.priority = 1;
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.PRIORITY-CANNOT') + ' ' + '"' + '('  + this.issueData.issue_internal_id + ')' + ' ' + this.issueData.subject + '"', AlertType.ERROR, AlertDuration.MEDIUM);
                console.error(httpErrorResponse.error.errorMessages);
            }
        );
    }

    clickDelPriority() {
        this.issueService.unsetPriority(this.issueData.id).subscribe(
            () => {
                this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.PRIORITY-RECEIVED') + ' ' + '"' + '('  + this.issueData.issue_internal_id + ')' + ' ' + this.issueData.subject + '"', AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.issueData.priority = 0;
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.PRIORITY-CANNOT-RECEIVED') + ' ' + '"' + '('  + this.issueData.issue_internal_id + ')' + ' ' + this.issueData.subject + '"', AlertType.ERROR, AlertDuration.MEDIUM);
                console.error(httpErrorResponse.error.errorMessages);
            }
        );
    }

    clickClose() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: 'auto',
                data: {
                    header: this.translate.instant('ISSUE-VIEW-MENU.CLOSE-ISSUE-NOTIFICATION')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => !!result));

        confirm$.pipe(
            switchMapTo(this.issueService.closeIssue(this.issueData.id))
        ).subscribe(
            () => {
                this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.ISSUE-CLOSED') + ' ' + '"' +'('  + this.issueData.id + ')' + ' ' + this.issueData.subject + '"', AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.refreshList.emit(null);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.ISSUE-CANNOT-CLOSED') + ' ' + '"' +'('  + this.issueData.id + ')' + ' ' + this.issueData.subject + '"', AlertType.ERROR, AlertDuration.MEDIUM);
                console.error(httpErrorResponse.error.errorMessages);
            }
        );
    }

    clickOpen() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: 'auto',
                data: {
                    header: this.translate.instant('ISSUE-VIEW-MENU.OPEN-ISSUE-NOTIFICATION')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => !!result));

        confirm$.pipe(
            switchMapTo(this.issueService.openIssue(this.issueData.id))
        ).subscribe(
            () => {
                this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.OPEN-ISSUE-NOTIFICATION1') + ' ' + '"' +'('  + this.issueData.id + ')' + ' ' + this.issueData.subject + '"', AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.refreshList.emit(null);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.alertService.showAlert(this.translate.instant('ISSUE-VIEW-MENU.ISSUE-CANNOT-OPEN') + ' ' + '"' +'('  + this.issueData.id + ')' + ' ' + this.issueData.subject + '"', AlertType.ERROR, AlertDuration.MEDIUM);
                console.error(httpErrorResponse.error.errorMessages);
            }
        );
    }

    convertIssueToPaid() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '400px',
                data: {
                    header:  this.translate.instant('ISSUE-VIEW-MENU.ISSUE-CONVERT'),
                    input: {
                        type: 'checkbox',
                        label: this.translate.instant('ISSUE-VIEW-MENU.WITHDRAW'),
                        value: true
                    }
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => typeof result === 'boolean'));

        confirm$
            .pipe(
                switchMap(decrementIssue => {
                    const body = decrementIssue ? {user_subscription_id: 0, decrement_issue_counter: 1} : {user_subscription_id: 0};

                    return this.issueService.updateIssue(this.issueData.id, body);
                })
            )
            .subscribe(
                () => this.messagingService.showConfirmation(this.translate.instant('ISSUE-VIEW-MENU.CONVERTED') + ' '+ '"' + "(" + this.issueData.id + ")" + this.translate.instant('ISSUE-VIEW-MENU.PRICING') + '"') ,
                () => this.messagingService.showError(this.translate.instant('ISSUE-VIEW-MENU.FAILED-CONVERT'))
            );
    }
}
