import { LucideAngularModule, icons } from 'lucide-angular';
import {Component, ElementRef, EventEmitter, Inject, Input, OnChanges, OnDestroy, OnInit, Output, Sanitizer, SimpleChanges, ViewChild} from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import {CommentService} from '../../services/comment.service';
import {Subscription} from 'rxjs';
import * as _ from 'underscore';
import {UserInterface} from '../../common/interfaces/user.interface';
import {CommentInterface} from '../../common/interfaces/comment.interface';
import {activateLinks} from '../../common/utils/activate-links';
import {DomSanitizer} from '@angular/platform-browser';
import {IssueCommentDialogComponent} from '../issue-comment-dialog/issue-comment-dialog.component';
import {first} from 'rxjs/operators';
import {AuthService} from '../../services/auth/auth.service';
import {ButtonVariant} from '../../common/enums/button-variant.enum';
import { Ng<PERSON><PERSON>, NgClass, NgIf } from '@angular/common';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { FormsModule } from '@angular/forms';
import { ButtonComponent } from '../../elements/button/button.component';
import { DateRespondoFormatPipe } from '../../shared/pipes/date-respondo-format.pipe';
import { LinkifyPipe } from '../../shared/pipes/linkify.pipe';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-issue-comment-sidebar',
    templateUrl: './issue-comment-sidebar.component.html',
    styleUrls: ['./issue-comment-sidebar.component.scss'],
    standalone: true,
    imports: [NgFor, NgClass, DefaultClassDirective, NgIf, FormsModule, ButtonComponent, DateRespondoFormatPipe, LinkifyPipe, TranslatePipe, LucideAngularModule]
})
export class IssueCommentSidebarComponent implements OnInit, OnChanges, OnDestroy {
    @ViewChild('inputAddCommentToGroup', {static: false}) InputAddCommentToGroup: ElementRef;

    @Input()
    isOpen: boolean = false;

    @Input()
    isOpenFromDetails: boolean = false;

    @Input()
    issueId: number;

    @Input()
    issueCommentGroups;

    @Input()
    issueComments;

    @Output()
    addNewComment = new EventEmitter<string>();

    @Output()
    isOpenChange = new EventEmitter<boolean>();

    subscriptions = new Subscription();

    public replyText: string = '';
    public replyToCommentId: string = null;
    public activeReplyIndex: number = -1;
    public selectedGroupId: string = null;

    protected readonly ButtonVariant = ButtonVariant;

    private commentIndexMap: Map<string, number> = new Map<string, number>();
    private groupedComments: Map<string, CommentInterface[]> = new Map<string, CommentInterface[]>();
    private uniqueGroupIds: string[] = [];

    constructor(private commentService: CommentService, private sanitizer: DomSanitizer, private authService: AuthService) {
    }

    ngOnInit() {
        _.chain(this.issueCommentGroups).get('data').forEach((comment: { User: UserInterface, Comment: CommentInterface }) => {
            if (_.chain(comment.Comment).get('body').isString().value()) {
                comment.Comment.body = <string>this.sanitizer.bypassSecurityTrustHtml(activateLinks(comment.Comment.body));
            }
        }).value();

        this.subscriptions.add(
            this.commentService.selectGroup.subscribe(groupId => {
                if (groupId) {
                    this.selectedGroupId = groupId.toString();
                }
            })
        );
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes.issueComments && !changes.issueComments.firstChange) {
            this.handleCommentsGroups();
        }
    }

    private handleCommentsGroups(): void {
        this.commentIndexMap.clear();
        this.groupedComments.clear();

        for (const i in this.issueComments) {
            if (this.issueComments.hasOwnProperty(i)) {
                const comment = this.issueComments[i];
                // tslint:disable-next-line:radix
                this.commentIndexMap.set(comment.Comment.id.toString(), parseInt(i));
            }
        }

        for (const i in this.issueComments) {
            if (this.issueComments.hasOwnProperty(i)) {
                const comment = this.issueComments[i];
                const groupId = comment.Comment.group_id.toString();

                if (!this.groupedComments.has(groupId)) {
                    this.groupedComments.set(groupId, []);
                }

                this.groupedComments.get(groupId).push(comment);
            }
        }

        const groupEntries = Array.from(this.groupedComments.entries());
        for (const i in groupEntries) {
            if (groupEntries.hasOwnProperty(i)) {
                const [groupId, comments] = groupEntries[i];
                comments.sort((a, b) => {
                    return new Date(a.created).getTime() - new Date(b.created).getTime();
                });
                this.groupedComments.set(groupId, comments);
            }
        }

        this.uniqueGroupIds = Array.from(this.groupedComments.keys());
    }

    getUniqueGroupIds(): string[] {
        return this.uniqueGroupIds;
    }

    getCommentsForGroup(groupId: string): any[] {
        return this.groupedComments.get(groupId) || [];
    }

    getCommentIndex(commentId: string): number {
        return this.commentIndexMap.has(commentId.toString()) ?
            this.commentIndexMap.get(commentId.toString()) : -1;
    }

    toggleReplyInput($event: Event, index: number, commentId: string): void {
        $event.stopPropagation();

        this.activeReplyIndex = this.activeReplyIndex === index ? -1 : index;
        this.replyText = '';
        this.replyToCommentId = this.activeReplyIndex === -1 ? null : commentId;
    }

    selectCommentGroup(groupId: string): void {
        this.selectedGroupId = groupId;

        this.commentService.selectGroupComments(groupId);
    }

    close() {
        this.isOpen = !this.isOpen;
        this.isOpenChange.emit(this.isOpen);
    }

    cancelReply($event: Event): void {
        $event.stopPropagation();
        this.activeReplyIndex = -1;
        this.replyText = '';
        this.replyToCommentId = null;
    }

    submitReply($event: Event, groupId: string): void {
        $event.stopPropagation();

        if (this.replyText.trim()) {
            const replyBody = this.replyText;

            if (this.replyToCommentId) {
                this.commentService.addComment(
                    +groupId,
                    +this.authService.getUserId(),
                    replyBody
                )
                    .pipe(first())
                    .subscribe({
                        next: () => {
                            this.refreshComments();
                        }
                    });
            }

            this.activeReplyIndex = -1;
            this.replyText = '';
            this.replyToCommentId = null;
        }
    }

    private refreshComments(): void {
       this.commentService.refreshComments();
    }

    ngOnDestroy(): void {
        this.subscriptions?.unsubscribe();
    }
}
