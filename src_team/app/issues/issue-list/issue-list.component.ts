import {ChangeDetectorRef, Component, forwardRef, Input, OnDestroy, OnInit, ViewChild} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SanitizeColumnIdPipe } from '../../shared/pipes/sanitize-column-id.pipe';
import { MatAccordion } from '@angular/material/expansion';
import { PageEvent } from '@angular/material/paginator';
import { Observable, of, Subscription, timer } from 'rxjs';
import { first, map, switchMap, take, tap } from 'rxjs/operators';
import { SidebarsContainerService } from '../../services/sidebars-container.service';
import { IssueService } from '../../services/issue.service';
import { AuthService } from '../../services/auth/auth.service';
import { UserInputStorageService } from '../../services/user-input-storage.service';
import { SocketIssuesService } from '../../services/sockets/socket-issues.service';
import { Socket<PERSON>hange } from '../../common/classes/socket-change';
import { IssueInterface } from '../../common/interfaces/issue.interface';
import { LoadingStatus } from '../../common/enums/loading-status.enum';
import { IsMobileService } from '../../services/is-mobile.service';
import { IssueColumnName } from '../../common/types/issue-columns-name';
import { ApplicationStateService } from '../../services/application-state.service';
import { IssueInitiatorService } from '../../services/issue-initiator.service';
import { ToolbarFilter, IssueListToolbarComponent } from '../issue-list-toolbar/issue-list-toolbar.component';
import { OrdersService } from '../../services/orders.service';
import { IssueActionAllowedService } from '../../services/issue-action-allowed.service';
import { environment } from '../../../environments/environment';
import {
    AllCommunityModule,
    CellClickedEvent,
    ColDef,
    GridOptions,
    GridReadyEvent,
    iconSetMaterial,
    ModuleRegistry,
    Theme,
    themeQuartz
} from 'ag-grid-community';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import { DurationPipe } from '../../shared/pipes/duration.pipe';
import { KeyValue } from '../../common/interfaces/key-value.interface';
import { MatDrawer } from '@angular/material/sidenav';
import { getIssueColumnDefs } from '../issue-columns.config';
import { IssueActions } from '../../interfaces/issue-actions';
import { ButtonVariant } from '../../common/enums/button-variant.enum';
import { DetailEvent } from '../../interfaces/details-event';
import { ApplicationSettingsService } from '../../services/application-settings.service';
import { Store } from '@ngrx/store';
import { selectCurrentRoute } from '../../store/router/router.selectors';
import { UserInterface } from '../../common/interfaces/user.interface';
import * as UserSelectors from '../../store/user/user.selectors';
import { IssueCategoriesService } from '../../services/issue-categories.service';
import { TagService } from '../../services/tag.service';
import { BASE_DEFAULT_COL_DEF } from '../../shared/table/base-default-col-def';
import { BASE_GRID_OPTIONS } from '../../shared/table/base-grid-options';
import { IssueShowColumns } from 'src_team/app/interfaces/issue-columns';
import { IssueColumn } from 'src_team/app/common/enums/issue-column.enum';
import {DatePipe, NgClass, NgSwitch, NgSwitchCase, NgTemplateOutlet} from '@angular/common';
import { IssueCountersService } from '../../services/issue-counters.service';
import { ClientViewService } from '../../services/client-view.service';
import { DefaultLayoutDirective, DefaultFlexDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { DialogDetailsComponent } from '../../shared/dialog-details/dialog-details.component';
import { PaginatorComponent } from '../../elements/paginator/paginator.component';
import { DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { IssueListUsersToolbarComponent } from '../issue-list-users-toolbar/issue-list-users-toolbar.component';
import {AgGridAngular, AgGridModule } from 'ag-grid-angular';
ModuleRegistry.registerModules([AllCommunityModule]);
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import {TABLE_THEME} from '../../shared/table/theme';

@Component({
    selector: 'app-issue-list',
    templateUrl: './issue-list.component.html',
    styleUrls: ['./issue-list.component.scss'],
    providers: [DurationPipe],
    imports: [DefaultLayoutDirective, NgSwitch, NgSwitchCase, NgTemplateOutlet, DefaultFlexDirective, forwardRef(() => DialogDetailsComponent), PaginatorComponent, DefaultShowHideDirective, IssueListToolbarComponent, IssueListUsersToolbarComponent, AgGridModule, DefaultLayoutAlignDirective, MatProgressSpinner, TranslatePipe]
})

export class IssueListComponent implements OnInit, OnDestroy {
    @Input()
    modal = false;

    @Input()
    ownerId;

    @Input()
    clientId;

    @Input()
    hideActions = false;

    @Input()
    userIssuesMode = false;

    @Input()
    visibleFilters = ['group', 'tag', 'priority'];

    @Input()
    showStatusLabels = false;

    @Input()
    disableOpenUser = false;

    @Input()
    status = 'open,new,unaccepted,closed';

    /** Kolekcja kolumn, które mają się wyświetlić w tabeli. */
    @Input() showColumns: IssueColumn[];

    @ViewChild('issueListAccordion') issueListAccordion: MatAccordion;

    page = 1;

    initialPage: number;

    limit = 10;

    sortType = 'asc';

    sortField = 'waiting_time';

    listName: string;

    loadingStatus: LoadingStatus = LoadingStatus.loaded;

    userId: number;

    totalIssue: number;

    issuesList: IssueInterface[] = [];

    mobileSubscription: Subscription;

    selectedIssueIndex = -1;

    socketIssues$: Observable<SocketChange>;

    socketSubscription: Subscription;

    shownColumns: IssueColumnName[];

    filters = '';

    isValuateIssuePaymentPermission = false;

    internalMode = false;

    groupFilterId: number;

    columnDefs: ColDef[];

    defaultColDef: ColDef = {
        ...BASE_DEFAULT_COL_DEF
    };

    sideBar: {
        toolPanels: [
            {
                id: 'filters',
                labelDefault: 'Filters',
                labelKey: 'filters',
                iconKey: 'filter',
                toolPanel: 'agFiltersToolPanel',
            }
        ]
    };

    private priorityIcon = encodeURIComponent(`
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64' width='1em' height='1em'>
      <g>
        <path d="M36.989 42.439H27.01L23 2h18z" fill="#b22222"></path>
        <ellipse cx="31.999" cy="54.354" rx="7.663" ry="7.646" fill="#b22222"></ellipse>
      </g>
    </svg>`);

    private readonly animationTime = 300;

    private readonly triggerEventCount = 5;

    protected readonly event = event;

    public readonly ButtonVariant = ButtonVariant;

    @ViewChild('drawer') drawer: MatDrawer;
    isDetailsOpen = false;
    detailsData;
    issueData;
    detailsModel: string;
    currentUser$: Observable<UserInterface>;

    private gridApi;
    private subscriptions: Subscription = new Subscription();
    private isGridReady = false;

    constructor(
        private issueService: IssueService,
        private sidebarsContainer: SidebarsContainerService,
        private route: ActivatedRoute,
        private authService: AuthService,
        private userInputStorageService: UserInputStorageService,
        private applicationSettingsService: ApplicationSettingsService,
        private socketIssuesService: SocketIssuesService,
        public isMobileService: IsMobileService,
        private applicationStateService: ApplicationStateService,
        private issueInitiatorService: IssueInitiatorService,
        private router: Router,
        private cd: ChangeDetectorRef,
        private ordersService: OrdersService,
        private issueActionAllowedService: IssueActionAllowedService,
        private translate: TranslateService,
        private durationPipe: DurationPipe,
        private datePipe: DatePipe,
        private store: Store,
        private issueCategoriesService: IssueCategoriesService,
        private tagService: TagService,
        private issueCountersService: IssueCountersService,
        private clientViewService: ClientViewService,
        private sanitizeColumnIdPipe: SanitizeColumnIdPipe
    ) {
        this.sortField = 'waiting_time';
        this.sortType = 'asc';

        this.currentUser$ = this.store.select(UserSelectors.selectCurrentUser);

        this.subscriptions.add(
            this.clientViewService.clientEdit$
                .subscribe((result) => {
                    if (result.isOpen) {
                        const delayTime = 300;

                        this.isDetailsOpen = false,
                            timer(delayTime).subscribe(() => {
                                this.detailsData = result.data;
                                this.detailsModel = 'client-add';
                                this.isDetailsOpen = true;
                            });
                    }
                })
        );
    }

    gridOptions: GridOptions = {
        ...BASE_GRID_OPTIONS,
        onSortChanged: this.saveState.bind(this),
        getRowId: (params) => params.data.id.toString(),
        onCellKeyDown: (params: any) => {
            const event = params.event;

            if (event.key === 'ArrowRight' || event.key === 'ArrowLeft') {
                const selectedNodes = params.api.getSelectedNodes();

                if (selectedNodes.length === 0) {
                    const firstRow = params.api.getDisplayedRowAtIndex(0);
                    if (firstRow) {
                        params.api.selectNode(firstRow);
                        params.api.ensureNodeVisible(firstRow);
                        this.openDetails({ data: firstRow.data, node: firstRow });
                    }
                    return true;
                }

                const currentIndex = selectedNodes[0].rowIndex;
                const nextIndex = event.key === 'ArrowRight' ? currentIndex + 1 : currentIndex - 1;

                if (nextIndex >= 0 && nextIndex < params.api.getDisplayedRowCount()) {
                    selectedNodes.forEach(node => node.setSelected(false));

                    const nextRow = params.api.getDisplayedRowAtIndex(nextIndex);
                    params.api.selectNode(nextRow);
                    params.api.ensureNodeVisible(nextRow);
                    this.openDetails({ data: nextRow.data, node: nextRow });
                }
                return true;
            }

            return false;
        },
        onRowClicked: (event) => {
            const selectedNode = event.node;

            const isSelected = selectedNode.isSelected();

            if (isSelected) {
                return;
            }

            const selectedNodes = event.api.getSelectedNodes();
            selectedNodes.forEach(node => node.setSelected(false));

            selectedNode.setSelected(true);
            this.openDetails({ data: selectedNode.data, node: selectedNode });
        }
    };

    ngOnInit() {
        this.userId = +this.authService.getUserId();
        this.getPaginationLimit();
        this.loadFilterSettings(this.listName);
        this.setupUserSidebar();
        this.userIssuesMode ? this.setupUserIssuesMode() : this.setupStandardMode();
        this.isValuateIssuePaymentPermission = this.issueActionAllowedService.hasPermission('valuateIssuePayment');
        this.internalMode = environment.internalMode;

        const routeData = this.route.snapshot.data as IssueActions;
        const allowedActions = this.getAllowedActions(routeData);

        this.initShowColumns();

        let showCategoryColumn = false;

        this.columnDefs = getIssueColumnDefs(
            this.translate,
            this.durationPipe,
            this.datePipe,
            this.openDetails.bind(this),
            this.onEmployeeClicked.bind(this),
            this.onClientClicked.bind(this),
            this.issueDetails.bind(this),
            allowedActions,
            this.showColumns,
            this.issueCategoriesService
        );

        const moreOptionsColIndex = this.columnDefs.findIndex(col => col.cellRenderer === 'moreOptionsRenderer');

        if (moreOptionsColIndex !== -1) {
            this.columnDefs[moreOptionsColIndex].cellRendererParams = {
                ...this.columnDefs[moreOptionsColIndex].cellRendererParams,
                refreshList: () => {
                    this.fetchIssues();
                }
            };
        }

        this.subscriptions
            .add(
                this.socketIssuesService.issuesObservable.pipe(
                    tap(() => {
                        this.fetchIssues();
                    }),
                    switchMap(() => this.issueCountersService.updateAllCounters())
                ).subscribe())

        this.subscriptions.add(
            this.currentUser$.subscribe(user => {
                if (user) {
                    showCategoryColumn = +user.customer_id === environment.internalCustomerId;

                    this.columnDefs = getIssueColumnDefs(
                        this.translate,
                        this.durationPipe,
                        this.datePipe,
                        this.openDetails.bind(this),
                        this.onEmployeeClicked.bind(this),
                        this.onClientClicked.bind(this),
                        this.issueDetails.bind(this),
                        allowedActions,
                        this.showColumns,
                        this.issueCategoriesService
                    );

                    const subjectColIndex = this.columnDefs.findIndex(col => col.field === 'subject');
                    if (subjectColIndex !== -1) {
                        this.columnDefs[subjectColIndex].cellStyle = (params) => {
                            if (+params.data.priority) {
                                return {
                                    'background-image': `url("data:image/svg+xml;utf8,${this.priorityIcon}")`,
                                    'background-repeat': 'no-repeat',
                                    'background-position': '0 center',
                                    'background-size': '1em 1em'
                                };
                            }
                            return {
                                'padding-left': '1.2em'
                            };
                        };
                    }

                    if (this.gridApi) {
                        this.gridApi.setColumnDefs?.(this.columnDefs) || this.gridApi.setGridOption?.('columnDefs', this.columnDefs);
                    }

                    this.cd.detectChanges();
                }
            })
        )

        this.subscriptions.add(
            this.tagService.tagsChanged.subscribe(change => {
                this.handleTagsChanged(change);
            })
        )

        this.subscriptions.add(
            this.issueService.changed$.subscribe({
                next: () => {
                    this.fetchIssues();
                }
            })
        );

    }

    ngOnDestroy() {
        if (!this.userIssuesMode) {
            this.applicationStateService.setValue('issuesListLastVisited', this.listName);
            this.applicationStateService.setValue('issuesListLastPage', this.page);
        }

        if (this.mobileSubscription) {
            this.mobileSubscription?.unsubscribe();
        }

        if (this.socketSubscription) {
            this.socketSubscription?.unsubscribe();
        }

        this.subscriptions?.unsubscribe();
    }

    /**
     * Zainicjalizuj kolekcję kolumn do wyświetlenia w tabeli.
     *
     * Jeżeli input "showColumns" komponentu jest niezdefiniowany,
     * dane dotyczące wyświetlania kolumn pobierane są z routingu.
     */
    private initShowColumns(): void {
        this.showColumns = this.showColumns ?? (this.route.snapshot.data as IssueShowColumns).showColumns;
    }

    getRowStyle = () => {
        return {
            'cursor': 'pointer',
            'backgroundColor': '#FBFBFB'
        };
    };

    onClientClicked(clientData) {
        const delayTime = 300;

        +this.detailsData?.data?.id !== +clientData?.data?.id
            ? (
                this.isDetailsOpen = false,
                    timer(delayTime).subscribe(() => {
                        this.detailsData = clientData.data;
                        this.detailsModel = 'clients';
                        this.isDetailsOpen = true;
                    })
            )
            : (this.isDetailsOpen = !this.isDetailsOpen);
    }

    onEmployeeClicked(issue: CellClickedEvent) {
        const delayTime = 300;

        +this.detailsData?.id !== +issue?.data.owner_id
            ? (
                this.isDetailsOpen = false,
                    timer(delayTime).subscribe(() => {
                        this.detailsData = issue.data as IssueInterface;
                        this.detailsModel = 'users';
                        this.isDetailsOpen = true;
                    })
            )
            : (this.isDetailsOpen = !this.isDetailsOpen);
    }

    public applySort(sort) {
        if (this.isGridReady) {
            try {
                // Get column state using the optimal method for AG Grid v33
                const colState = sort.api.getColumnState();

                // Filter to only include columns that have sort information
                const sortState = colState
                    .filter(s => s.sort != null)
                    .map(s => ({ colId: s.colId, sort: s.sort, sortIndex: s.sortIndex }));

                if (sortState && sortState.length > 0) {
                    this.sortField = this.sanitizeColumnIdPipe.transform(sortState[0].colId);
                    this.sortType = sortState[0].sort;

                    // Special handling for waiting_time column
                    if (this.sortField === 'waiting_time') {
                        // Make sure we're using the correct field name for the backend
                        this.sortField = 'waiting_time';
                    }

                    /**
                     * Usunięto zapis filtrów w zadaniu RES-4511.
                     * applySort() jest wywoływany przy inicjalizację agGrid (podpięty jest pod zdarzenie sortChanged).
                     * Zapis powoduje wyczyszczenie filtrów w local storage (błędnie jest ustawiana wartość).
                     * Przez to licznik błędnie działa ponieważ nie ma zdefiniowanych filtrów w local storage.
                     */
                    // this.saveFilterSettings(this.listName);

                    if (!this.filters) {
                        if (this.listName === 'sent' && this.ownerId === undefined) {
                            this.ownerId = this.userId;
                        } else {
                            this.filters = '';
                        }
                    }

                    this.filters = this.filters
                        .replace(/order=[^&]+(&|$)/, '')
                        .concat(`order=${this.sortField}|${this.sortType}&`)
                        .replace(/&&/g, '&');
                }
            } catch (error) {
                console.warn('Error applying sort:', error);
            }
        }

        this.fetchIssues();
        this.cd.detectChanges();
    }

    saveFilterSettings(listName: string) {
        if (!listName) {
            return;
        }

        const filtersSettings = Object.keys(this.filters).filter(item => this.filters[item].savable).map((item: string) => ({key: item, value: this.filters[item].value})),
            settings = {
                filters: filtersSettings,
                groupFilterId: this.groupFilterId,
                sortField: this.sortField,
                sortType: this.sortType
            };

        this.userInputStorageService.setValue('filters_' + listName, JSON.stringify(settings));
    }

    private loadFilterSettings(listName: string) {
        if (!listName) {
            return;
        }

        const settings = JSON.parse(this.userInputStorageService.getValue('filters_' + listName));

        if (!settings) {
            return;
        }

        if (settings.filters) {
            settings.filters.forEach((item: KeyValue) => {
                if (this.filters[item.key]) {
                    this.filters[item.key].value = item.value;
                }
            });
        }

        const otherSettings = Object.keys(settings).filter((key: string) => key !== 'filters');

        if (otherSettings) {
            otherSettings.forEach((key: string) => {
                this[key] = settings[key];
            });
        }
    }

    private getPaginationLimit() {
        this.limit = +this.userInputStorageService.getValue('issueList_paginationLimit') || 10;
    }

    private setPaginationLimit(limit = 10) {
        this.limit = limit;
        this.userInputStorageService.setValue('issueList_paginationLimit', limit);
    }

    private setupUserSidebar() {
        if (this.modal) {
            return;
        }

        const userSidebar = this.sidebarsContainer.sidebar('users'),
            userSidebarExpand = +this.userInputStorageService.getValue('issueList_userSidebarExpand');

        userSidebar.drawerPush(true);
    }

    private setListSettings(listName: string) {
        switch (listName) {
            case 'unassigned':
                this.ownerId = 0;
                this.visibleFilters = ['priority', 'tag'];
                this.shownColumns = ['clientName', 'waitingTime'];
                this.sortField = 'waiting_time';
                this.sortType = 'desc';

                break;
            case 'my':
                this.ownerId = this.userId;
                this.visibleFilters = ['priority', 'tag'];
                this.shownColumns = ['clientName', 'waitingTime', 'statusLabels'];
                this.sortField = 'waiting_time';
                this.sortType = 'desc';

                break;
            case 'delegated':
                this.visibleFilters = ['group', 'priority', 'tag', 'users'];
                this.shownColumns = ['clientName', 'owner', 'waitingTime', 'statusLabels'];
                this.sortField = 'waiting_time';
                this.sortType = 'desc';

                break;
            case 'archives':
                this.visibleFilters = ['group', 'priority', 'tag', 'users', 'activity'];
                this.shownColumns = ['owner', 'answerTime'];
                this.sortField = 'modified';
                this.sortType = 'desc';

                break;
            case 'sent':
                this.visibleFilters = ['group', 'priority', 'tag', 'users'];
                this.sortField = 'modified';
                this.sortType = 'desc';

                break;
            case 'unaccepted':
                this.visibleFilters = ['group', 'priority', 'tag', 'users'];
                this.shownColumns = ['clientName', 'owner', 'waitingTime', 'statusLabels'];
                this.sortField = 'waiting_time';
                this.sortType = 'desc';

                break;
            default:
                this.visibleFilters = ['group', 'priority', 'tag', 'users'];
                this.shownColumns = ['owner', 'waitingTime'];
                this.sortField = 'waiting_time';
                this.sortType = 'desc';
        }
    }

    private setSocketObservable(listType: string) {
        switch (listType) {
            case 'unassigned' :
                this.socketIssues$ = this.socketIssuesService.unassignedIssuesObservable;

                break;
            case 'unaccepted' :
                this.socketIssues$ = this.socketIssuesService.unacceptedIssuesObservable;

                break;
            case 'my' :
                this.socketIssues$ = this.socketIssuesService.myIssuesObservable;

                break;
            case 'delegated' :
                this.socketIssues$ = this.socketIssuesService.delegatedIssuesObservable;

                break;
            case 'sent' :
                this.socketIssues$ = this.socketIssuesService.sentIssuesObservable;

                break;
            case 'archives' :
                this.socketIssues$ = this.socketIssuesService.closedIssuesObservable;

                break;
            default:
                this.socketIssues$ = null;
        }

        if (this.socketSubscription) {
            this.socketSubscription?.unsubscribe();
        }

        if (this.socketIssues$) {
            this.socketSubscription = this.socketIssues$.subscribe(() => {
                this.fetchIssues();
            });
        }
    }

    private get filtersString() {
        const fields = 'Issue_id,Issue_issue_internal_id,Issue_subject,Issue_owner_id,Issue_status,Issue_priority,Issue_tags,Issue_answer_time,Issue_client_info,Issue_modified,Issue_corrected,'
            + 'Issue_issue_initiator_id,Issue_order_id,Issue_offer_send,Issue_archived,Issue_category_id,Issue_sentences_counter,ai_suggest,poll_id';
        const statusFilter = this.status ? 'Issue_status=' + this.status + '&' : '';
        const issueInitiatorFilter: string = this.clientId ? `Issue_issue_initiator_id=${this.clientId}&` : '';

        this.loadFilterSettings(this.listName);

        return 'fields=' + fields + '&' + issueInitiatorFilter + statusFilter + this.filters + '&page=' + this.page + '&limit=' + this.limit;
    }

    fetchIssues() {
        const fetchIssues$ = this.issueService.getIssuesWithIssueSentence(this.filtersString)
            .pipe(
                tap(result => this.totalIssue = result.total),
                map(result => result.issues),
                switchMap(issues => {
                    const orderIds = issues.filter(issue => issue.order_id).map(issue => +issue.order_id);

                    if (!orderIds.length) {
                        return of(issues);
                    }

                    return this.ordersService.getOrdersByIds(orderIds, this.limit).pipe(
                        map(orders => {
                            issues.forEach(issue => {
                                if (issue.order_id) {
                                    const {transaction_status, modified} = orders.find(order => +order.id === +issue.order_id);
                                    issue.payment_status = transaction_status;
                                    issue.payment_date = modified;
                                }
                            });
                            return issues;
                        })
                    );
                }),
                switchMap(issues => {
                    const initiators = new Set<number>();
                    issues.forEach(item => {
                        if (item.issue_initiator_id) {
                            initiators.add(+item.issue_initiator_id);
                        }
                    });
                    return this.issueInitiatorService.getIssueInitiators(Array.from(initiators)).pipe(
                        map(initiatorsList => ({issues, initiatorsList}))
                    );
                }),
                tap(({issues, initiatorsList}) => {
                    for (const issue of issues) {
                        const initiator = initiatorsList.find(item => +item.id === +issue.issue_initiator_id);
                        issue.first_name = initiator ? initiator.first_name : '';
                        issue.last_name = initiator ? initiator.last_name : '';
                        issue.clientId = initiator ? initiator.id : '';
                        issue.client = initiator;
                    }

                    this.issuesList = [...issues];

                    this.cd.detectChanges();
                })
            );

        fetchIssues$.subscribe(
            () => true,
            () => this.loadingStatus = LoadingStatus.error,
            () => this.loadingStatus = this.issuesList.length ? LoadingStatus.loaded : LoadingStatus.nodata
        );
    }

    private setupUserIssuesMode() {
        this.shownColumns = ['waitingTime', 'statusLabels'];
    }

    private setupStandardMode() {
        this.status = this.route.snapshot.data.status;
        this.listName = this.route.snapshot.data.name;
        this.setListSettings(this.listName);
        this.setSocketObservable(this.listName);

        if (this.applicationStateService.getValue('issuesListLastVisited') === this.listName) {
            this.initialPage = this.applicationStateService.getValue('issuesListLastPage');
        }
    }

    private showNoData() {
        this.issuesList = [];
        this.totalIssue = 0;
        this.loadingStatus = LoadingStatus.nodata;
        this.cd.detectChanges();
    }

    private handleTagsChanged(change: { objectName: string, objectId: number, tags: string }) {
        this.fetchIssues();
    }

    onPageEvent(event: PageEvent) {
        this.page = ++event.pageIndex;
        this.setPaginationLimit(event.pageSize);
        this.fetchIssues();
    }

    updateSelectedIssueIndex(event) {
        switch (event.key) {
            case 'ArrowUp':
                if (this.selectedIssueIndex > -1) {
                    this.selectedIssueIndex--;
                }

                break;
            case 'ArrowDown':
                if (this.selectedIssueIndex < this.issuesList.length) {
                    this.selectedIssueIndex++;
                }

                break;
        }
    }

    onFiltersChanged(filters: ToolbarFilter) {
        switch (filters.type) {
            case 'QUERY':
                if (this.initialPage) {
                    this.page = this.initialPage;
                    this.initialPage = null;
                } else {
                    this.page = 1;
                }

                this.filters = filters.data;

                if (filters.showLoader) {
                    this.loadingStatus = LoadingStatus.loading;
                }

                this.fetchIssues();

                break;
            case '1_ISSUE':
                this.issueService.getIssueByInternalId(+filters.data).subscribe(
                    response => response ? this.router.navigateByUrl('/issue/' + response.Issue.id + '?list=' + this.listName) : this.showNoData(),
                    () => this.loadingStatus = LoadingStatus.error
                );

                break;
            case 'EMPTY':
                this.showNoData();

                break;
        }
    }

    get isMyList() {
        return this.listName === 'my';
    }

    async openDetails(event: DetailEvent) {
        if (event.middleClick) {
            this.openIssueInNewTab(event.data);
            return;
        }

        const isSameId = this.detailsData?.id === event.data?.id;
        const isClientDetailsOpen = this.isDetailsOpen && this.detailsModel === 'clients';
        const isIssueDetailsOpen = this.isDetailsOpen && this.detailsModel === 'issue';

        this.isDetailsOpen = false;

        await timer(this.animationTime).pipe(first()).toPromise();

        if (isClientDetailsOpen || !isIssueDetailsOpen || !isSameId) {
            this.detailsData = event.data;
            this.detailsModel = 'issue';

            await timer(this.animationTime).pipe(first()).toPromise();

            this.isDetailsOpen = true;
            this.cd.detectChanges();
            await this.emitEvent();
        }
    }

    private async emitEvent() {
        for (let clickCount = 0; clickCount < this.triggerEventCount; clickCount++) {
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });

            document.body.dispatchEvent(event);

            await timer(this.animationTime).pipe(first()).toPromise();
        }
    }

    private openIssueInNewTab(issueData: any) {
        this.store.select(selectCurrentRoute)
            .pipe(take(1))
            .subscribe(route => {
                const targetPath = route?.url?.length ? route.url[route.url.length > 1 ? 1 : 0].path : '';
                const urlTree = this.router.createUrlTree(['/issue', issueData.id], {
                    queryParams: { type: this.listName, targetPath }
                });

                window.open(this.router.serializeUrl(urlTree), '_blank');
            });
    }

    issueDetails(event) {
        this.issueData = event.data;
    }

    /**
     * Handler zdarzenia gridReady, wywoływany gdy siatka (grid) zostanie w pełni zainicjalizowana.
     * @param params - Obiekt parametrów zdarzenia, zawierający:
     *  - api: Główne API gridu, umożliwiające dostęp do metod operujących na gridzie.
     */
    onGridReady(params: GridReadyEvent) {
        this.gridApi = params.api;

        this.restoreState();

        try {
            const columnApi = this.gridApi.getColumnApi?.() || this.gridApi;

            const columnState = columnApi.getColumnState?.() || [];
            const hasSortedColumn = columnState.some(col => col.sort);

            if (!hasSortedColumn) {
                const sortModel = [{
                    colId: 'waiting_time',
                    sort: 'desc'
                }];

                if (typeof columnApi.applyColumnState === 'function') {
                    columnApi.applyColumnState({
                        state: sortModel,
                        defaultState: { sort: null }
                    });
                } else {
                    console.log('Could not set default sort on waiting_time column');
                }
            }
        } catch (error) {
            console.warn('Error setting default sort:', error);
        }

        this.isGridReady = true;
    }

    /**
     * Zapisuje bieżący stan sortowania gridu.
     *
     * Metoda pobiera aktualny model sortowania z API ag-Grid,
     * a następnie zapisuje go w formacie JSON przy użyciu serwisu ApplicationSettingsService pod kluczem 'agGridState'.
     * Dodatkowo sanityzuje identyfikatory kolumn, usuwając ewentualne przyrostki numeryczne (np. "_1", "_2").
     */
    saveState() {
        if (!this.gridApi || !this.listName) {
            return;
        }

        try {
            // Get column state using the optimal method for AG Grid v33
            const colState = this.gridApi.getColumnState();

            // Filter to only include columns that have sort information and extract only needed properties
            const sortState = colState
                .filter(s => s.sort != null)
                .map(s => ({ colId: s.colId, sort: s.sort, sortIndex: s.sortIndex }));

            const gridState = { sortState };
            this.applicationSettingsService.setValue('agGridState_' + this.listName, JSON.stringify(gridState));
        } catch (error) {
            console.warn('Error saving sort state:', error);
        }
    }

    /**
     * Przywraca stan sortowania gridu z pamięci.
     *     * Metoda odczytuje zapisany stan gridu (sortowanie) z serwisu UserInputStorageService,
     * korzystając z klucza 'agGridState'. Jeśli zapisany stan istnieje, metoda ustawia model sortowania
     * w ag-Grid.
     */
    restoreState() {
        if (!this.gridApi || !this.listName) {
            return;
        }

        const storageKey = 'agGridState_' + this.listName;
        const agGridState = this.userInputStorageService.getValue(storageKey);

        if (agGridState && this.gridApi) {
            try {
                const { sortState } = JSON.parse(agGridState);
                if (sortState && sortState.length > 0) {
                    this.gridApi.applyColumnState({
                        state: sortState,
                        defaultState: { sort: null }
                    });
                }
            } catch (error) {
                console.warn('Failed to restore grid state:', error);
            }
        }
    }

    /**
     * Przetwarza dane akcji (IssueActions) i generuje akcje w tabeli,
     * ustawiając flagi dla dostępnych akcji:
     *
     * @param actions - Obiekt IssueActions (opcjonalne flagi: take, close, delegated).
     * @returns Obiekt allowedActions z flagami: take, delegate, open, close.
     */
    private getAllowedActions(actions: IssueActions): { priority: boolean; take: boolean; delegate: boolean; close: boolean; open: boolean } {
        return {
            priority: true,
            take: !!actions.take,
            delegate: !!actions.delegated,
            open: !!actions.open,
            close: !!(actions.take || actions.close || actions.delegated)
        };
    }
}
