<div class="issue-list-component" fxLayout="column" [class.user-mode]="userIssuesMode">
    <ng-container [ngSwitch]="userIssuesMode">
        <ng-container *ngSwitchCase="true" [ngTemplateOutlet]="usersModeToolbar"></ng-container>
        <ng-container *ngSwitchCase="false" [ngTemplateOutlet]="standardModeToolbar"></ng-container>
    </ng-container>

    <div class="issues-list" [fxFlex]="!hideActions" (keyup)="updateSelectedIssueIndex($event)">
        <ng-container [ngSwitch]="loadingStatus">
            <ng-container *ngSwitchCase="'loaded'" [ngTemplateOutlet]="issuesListTemplate"></ng-container>
            <ng-container *ngSwitchCase="'loading'" [ngTemplateOutlet]="loadingTemplate"></ng-container>
            <ng-container *ngSwitchCase="'error'" [ngTemplateOutlet]="errorTemplate"></ng-container>
            <ng-container *ngSwitchCase="'nodata'" [ngTemplateOutlet]="noDataTemplate"></ng-container>
        </ng-container>
    </div>

    <app-dialog-details
        [isOpen]="isDetailsOpen"
        [data]="detailsData"
        [model]="detailsModel"
        (isOpenChange)="isDetailsOpen = $event">
    </app-dialog-details>
</div>

<ng-template #standardModeToolbar>
    <div class="toolbar">
        <app-5ways-paginator
            [pageSize]="limit"
            [pageIndex]="page-1"
            [length]="totalIssue || 0"
            (page)="onPageEvent($event)"
            fxHide.md fxHide.xs fxHide.sm>
        </app-5ways-paginator>
        <app-issue-list-toolbar
            [initialOwnerId]="ownerId"
            [listName]="listName"
            [visibleFilters]="visibleFilters"
            [initialSortField]=sortField
            [initialSortType]=sortType
            (filtersChanged)=onFiltersChanged($event)
        ></app-issue-list-toolbar>
    </div>
</ng-template>

<ng-template #usersModeToolbar>
    <div class="toolbar">
        <app-5ways-paginator
            [pageSize]="limit"
            [pageIndex]="page-1"
            [length]="totalIssue || 0"
            (page)="onPageEvent($event)"
            fxHide.md fxHide.xs fxHide.sm>
        </app-5ways-paginator>
        <app-issue-list-users-toolbar
            [ownerId]="ownerId"
            [initialSortField]=sortField
            [initialSortType]=sortType
            (filtersChanged)=onFiltersChanged($event)>
        </app-issue-list-users-toolbar>
    </div>
</ng-template>

<ng-template #issuesListTemplate>
    <div class="table-wrapper">
        <ag-grid-angular
            [domLayout]="'autoHeight'"
            (gridReady)="onGridReady($event)"
            [rowData]="issuesList"
            [columnDefs]="columnDefs"
            [defaultColDef]="defaultColDef"
            [getRowStyle]="getRowStyle"
            (filterChanged)="onFiltersChanged($event)"
            (sortChanged)="applySort($event)"
            [suppressMenuHide]="false"
            [gridOptions]="gridOptions"
            [sideBar]="sideBar"
            [enableBrowserTooltips]="true">
        </ag-grid-angular>
    </div>
</ng-template>

<ng-template #noDataTemplate>
    <div class="panel-info" fxLayoutAlign="center center">
        {{'ISSUE-LIST.CLEAR-LIST' | translate}}
    </div>
</ng-template>

<ng-template #loadingTemplate>
    <div fxLayoutAlign="center center" class="full-height">
        <mat-spinner></mat-spinner>
    </div>
</ng-template>

<ng-template #errorTemplate>
    <div class="panel-info" fxLayoutAlign="center center">
        {{'ISSUE-LIST.LIST-ERROR' | translate}}
    </div>
</ng-template>
