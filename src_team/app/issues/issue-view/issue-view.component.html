<div class="issue-view">

    <app-dialog-details
            [isOpen]="isDetailsOpen"
            [data]="detailsData"
            [model]="detailsModel"
            (isOpenChange)="isDetailsOpen = $event">
    </app-dialog-details>

    <div class="header">
        <div class="accordion-item">
            <button class="accordion-header" [ngClass]="{ 'open': isAccordionOpen('main') }" (click)="toggleAccordion('main')">
                <div class="accordion-header__left">
                    <ngx-avatars matTooltip="{{initiatorData?.first_name}}" [size]="28" [name]="userName" [bgColor]="defaultAvatarBgColor"></ngx-avatars>
                    <span>{{userName}}</span>
                </div>

                <div class="accordion-header__right">
                    <span>{{issueData.modified | dateRespondoFormat}}</span>
                    <i-lucide name='chevron-down' class="icon"></i-lucide>
                </div>
            </button>
            <div class="accordion-content" [ngClass]="{ 'open': isAccordionOpen('main') }">
                <div class="accordion-content__left">
                    <div class="detail-accordion__content__left" appLucideIcons>
                    <ul class="mt-0 mb-0">
                        <li *ngIf="initiatorData?.first_name" class="client-info-key">
                            {{'ISSUE-VIEW-SIDEBAR.CLIENT' | translate}} &nbsp;
                            <span (click)="openClientDetails()" class="client-info-data cursor-pointer">
                                {{initiatorData.first_name}}
                            </span>
                            <span (click)="openClientDetails(ClientModalTabs.CLIENT_NOTES)"><i-lucide name='notebook-pen' [size]="24" class="notification-icon cursor-pointer"></i-lucide></span>
                        </li>
                        <li *ngIf="initiatorData?.email" class="client-info-key">
                            {{'ISSUE-VIEW-SIDEBAR.EMAIL' | translate}} &nbsp;
                            <span class="client-info-data">
                                {{initiatorData.email}}
                            </span>
                        </li>
                        <li *ngIf="initiatorData?.street" class="client-info-key">
                            {{'ISSUE-VIEW-SIDEBAR.STREET' | translate}} &nbsp;
                            <span class="client-info-data">
                                {{initiatorData.street}}
                            </span>
                        </li>
                        <li *ngIf="initiatorData?.web" class="client-info-key">
                            {{'ISSUE-VIEW-SIDEBAR.WEB' | translate}} &nbsp;
                            <span class="client-info-data">
                                {{initiatorData.web}}
                            </span>
                        </li>
                        <li *ngIf="initiatorData?.city" class="client-info-key">
                            {{'ISSUE-VIEW-SIDEBAR.CITY' | translate}} &nbsp;
                            <span class="client-info-data">
                                {{initiatorData.city}}
                            </span>
                        </li>
                        <li *ngIf="initiatorData?.phone" class="client-info-key">
                            {{'ISSUE-VIEW-SIDEBAR.PHONE' | translate}} &nbsp;
                            <span class="client-info-data">
                                {{initiatorData.phone}}
                            </span>
                        </li>
                        <li *ngIf="initiatorData?.nip" class="client-info-key">
                            {{'ISSUE-VIEW-SIDEBAR.NIP' | translate}} &nbsp;
                            <span class="client-info-data">
                                {{initiatorData.nip}}
                            </span>
                        </li>
                        <li *ngIf="initiatorData?.zip" class="client-info-key">
                            {{'ISSUE-VIEW-SIDEBAR.ZIP' | translate}} &nbsp;
                            <span class="client-info-data">
                                {{initiatorData.zip}}
                            </span>
                        </li>
                        <li *ngIf="initiatorData?.created" class="client-info-key">
                            {{'ISSUE-VIEW-SIDEBAR.CREATED' | translate}} &nbsp;
                            <span class="client-info-data">
                                {{initiatorData.created}}
                            </span>
                        </li>

                        <ng-container *ngIf="basicClientData">
                            <li *ngFor="let info of basicClientData.value" class="client-info-key">
                                {{ info.key }}: <span class="client-info-data">{{ info.value }}</span>
                            </li>
                        </ng-container>

                        <ng-container *ngIf="environment.internalMode">
                            <li class="client-info-key">{{'ISSUE-VIEW-SIDEBAR.ORIGIN' | translate}} &nbsp;<span class="client-info-data">{{ getOriginText() }}</span></li>
                        </ng-container>

                        <app-client-subscriptions
                                [clientId]="+issueData?.user_client_id">
                        </app-client-subscriptions>
                    </ul>
                    </div>
                </div>

                <div class="accordion-content__right">
                    <div class="accordion-content__right__info mb-6">
                        <div class="accordion-content__left__item" *ngIf="issueData?.status">
                            <div class="accordion-content__left__item__title">{{'ISSUE-VIEW-SIDEBAR.STATUS' | translate}}</div>
                            <div class="accordion-content__left__item__description">{{translateStatus(issueData.status)}}&nbsp; <small *ngIf="+issueData?.archived">{{'ISSUE-VIEW-SIDEBAR.ARCHIVE' | translate}}</small></div>
                        </div>
                        <ng-container *ngIf="+issueData?.priority">
                            <div class="accordion-content__left__item">
                                <div class="accordion-content__left__item__key warning-red">
                                    {{'ISSUE-VIEW-SIDEBAR.PRIORITY' | translate}}
                                </div>
                            </div>
                        </ng-container>

                        <ng-container *ngIf="!+issueData?.user_subscription_id && +issueData.offer_send">
                            <div class="accordion-content__left__item">
                                <ng-container *ngIf="environment.simplifyPricing; else extendedPricing">
                                <span *ngIf="+issueData.offer_send" class="accordion-content__left__item__key warning-red">
                                    {{'ISSUE-VIEW-SIDEBAR.ISSUE-VALUED' | translate}}
                                </span>
                                </ng-container>
                                <ng-template #extendedPricing>
                                    <span
                                        *ngIf="+issueData.offer_send"
                                        class="accordion-content__left__item__key warning-red"
                                        [ngClass]="{'cursor-pointer': issueActionAllowedService.hasPermission('valuateIssuePayment')}"
                                        (click)="environment.simplifyPricing ? null : showValuation()">
                                        {{'ISSUE-VIEW-SIDEBAR.ISSUE-VALUED' | translate}}
                                    </span>
                                </ng-template>
                            </div>
                        </ng-container>

                        <!-- Status płatności:-->
                        <ng-container *ngIf="+issueData.offer_send">
                            <div class="accordion-content__left__item">
                                <div class="accordion-content__left__item__description">
                                    <ng-container [ngSwitch]="issueData.payment_status">
                                        <span class="warn-color" *ngSwitchCase="'success'">{{ 'ISSUE-VIEW-SIDEBAR.PAYMENT-STATUS-SUCCESS' | translate }}</span>
                                        <span class="warn-color" *ngSwitchCase="'pending'">{{ 'ISSUE-VIEW-SIDEBAR.PAYMENT-STATUS-PENDING' | translate }}</span>
                                        <span class="warn-color" *ngSwitchCase="'failure'">{{ 'ISSUE-VIEW-SIDEBAR.PAYMENT-STATUS-FAILURE' | translate }}</span>
                                        <span class="warn-color" *ngSwitchCase="'cancel'">{{ 'ISSUE-VIEW-SIDEBAR.PAYMENT-STATUS-CANCEL' | translate }}</span>
                                        <span class="warn-color" *ngSwitchDefault>&nbsp;{{ 'ISSUE-VIEW-SIDEBAR.PAYMENT-STATUS-NOT-PAID' | translate }}</span>
                                    </ng-container>
                                    <span *ngIf="issueData.payment_date" matTooltip="{{issueData.payment_date | date : 'dd-MM-yyyy HH:mm'}}">({{issueData.payment_date | date : 'dd-MM-yy'}})</span>
                                </div>
                            </div>
                        </ng-container>

                        <div class="accordion-content__left__item" *ngIf="issueData.complaint_id">
                            <div class="accordion-content__left__item__title">{{'ISSUE-VIEW-SIDEBAR.COMPLAINTS-STATUS' | translate}}</div><div class="accordion-content__left__item__description">{{issueComplaintService.statusNames[complaintStatus]}}</div>
                        </div>

                        <ng-container *ngIf="issueData?.poll_id">
                            <div class="accordion-content__left__item">
                                <div class="accordion-content__left__item__key warning-red">
                                    {{'ISSUE-VIEW-SIDEBAR.OFFER' | translate}}
                                </div>
                            </div>
                        </ng-container>

                        <ng-container *ngIf="categories && categories.length > 0">
                            <div class="accordion-content__left__item">
                                <div class="accordion-content__left__item__title">{{'ISSUE-VIEW-SIDEBAR.CATEGORY' | translate}}</div>
                                <div class="accordion-content__left__item__description">
                                    {{ issueCategory$ | async }}

                                    <ng-container *ngIf="'changeIssueCategory' | checkPermissionName">
                                        <mat-icon
                                                matTooltip="{{'ISSUE-VIEW-SIDEBAR.CHANGE-CATEGORY' | translate}}"
                                                color="primary"
                                                [matMenuTriggerFor]="menuCategories"
                                                (menuOpened)="openedMenuCategories()">
                                            <i-lucide name="pen" class="icon cursor-pointer"></i-lucide>
                                        </mat-icon>
                                    </ng-container>

                                    <mat-menu #menuCategories="matMenu" xPosition="before">
                                        <ng-template matMenuContent>
                                            <div>
                                                <ng-container *ngFor="let category of categories | orderBy: 'sequence'">
                                                    <button mat-menu-item (click)="setCategory(category.id)">
                                                        <span>{{category.name}}</span>
                                                    </button>
                                                </ng-container>
                                                <button mat-menu-item (click)="addNewCategory()" *ngIf="'categoriesManagement' | checkPermissionName">
                                                    <mat-icon>add</mat-icon>
                                                    <span>{{'CATEGORIES.CATEGORY-LIST-ADD' | translate}}</span>
                                                </button>
                                            </div>
                                        </ng-template>
                                    </mat-menu>
                                </div>
                            </div>
                        </ng-container>

                        <div class="accordion-content__left__item">
                            <div class="accordion-content__left__item__title">{{'ISSUE-VIEW-SIDEBAR.NUMBER-ISSUES' | translate}}</div>
                            <div class="accordion-content__left__item__description">
                            <span (click)="openClientDetails(ClientModalTabs.ISSUES)">
                                {{clientIssuesCount}}
                            </span>
                            </div>
                        </div>

                        <ng-container *ngIf="!decodedClientInfoSegmented">
                            <div class="accordion-content__left__item" *ngFor="let info of decodedClientInfo">
                                <div class="accordion-content__left__item__title">{{info.key}}:</div>
                                <div class="accordion-content__left__item__description">
                                    {{info.value}}
                                </div>
                            </div>
                        </ng-container>

                        <ng-container *ngIf="issuePaymentOffer && environment.simplifyPricing">
                            <div class="accordion-content__left__item">
                                <div class="accordion-content__left__item__title">{{'SHARED.ISSUE-VALUATION'|translate}}</div>
                                <div class="accordion-content__left__item__description">
                                    <div>
                                        <span class="client-info-key">{{ 'SHARED.NAME'| translate}}</span>&nbsp;<span class="client-info-data">{{issuePaymentOffer.name}}</span><br>
                                        <span class="client-info-key">{{ 'SHARED.AMOUNT' | translate}}</span>&nbsp;<span class="client-info-data">{{issuePaymentOffer.amount}} PLN</span><br>
                                        <span class="client-info-key">{{ 'SHARED.WORK-TIME' | translate}}</span>&nbsp;<span class="client-info-data">{{workTime}} {{workTimeDescription}}</span>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                    <ng-container *ngIf="!decodedClientInfoSegmented">
                        <div *ngFor="let info of decodedClientInfo">
                            <ul>
                                <li class="client-info-key">{{info.key}}:&nbsp;&nbsp;<span class="client-info-data">{{info.value}}</span></li>
                            </ul>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="decodedClientInfoSegmented">
                        <div *ngFor="let segment of getSortedClientInfo(); let i = index">
                            <div class="detail-accordion__item">
                                <button class="detail-accordion__header" [ngClass]="{ 'open': isAccordionOpen(segment.key) }" (click)="toggleAccordion(segment.key)">
                                    <div class="detail-accordion__header__left">
                                        <i-lucide class="notification-icon" [name]="detailsIcons[i]"></i-lucide>
                                        {{segment.key}}
                                    </div>

                                    <div class="detail-accordion__header__right">
                                        <i-lucide name='chevron-down' class="icon"></i-lucide>
                                    </div>
                                </button>
                                <div class="detail-accordion__content" [ngClass]="{ 'open': isAccordionOpen(segment.key) }">
                                    <div class="detail-accordion__content__left">
                                        <ul>
                                            <li *ngFor="let info of segment.value" class="client-info-key">
                                                {{info.key}}: &nbsp;<span class="client-info-data">{{info.value}}</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
    </div>
    <div class="body issue-mainblock-wrapper" [class.blocked]=" this.issueLockedByAnotherUser?.id && +issueLockedByAnotherUser?.id !== +this.userId" (keydown.tab)="onMainblockTab($event)">
        <ng-container *ngIf="this.issueLockedByAnotherUser?.id && +issueLockedByAnotherUser?.id !== +this.userId">
            <div class="block-text">
                {{ 'ISSUE-VIEW.ISSUE-EDIT-USER' | translate }}  {{ this.issueLockedByAnotherUser.name }}
                <div class="take-button">
                    <app-5ways-button
                        [variant]="ButtonVariant.GHOST"
                        (click)="editIssue($event)">
                        {{'ISSUE-VIEW.EDIT-ISSUE' | translate}}
                    </app-5ways-button>
                </div>
            </div>
        </ng-container>
        <div class="body__header">
            <span class="body__header__id" ngxClipboard [cbContent]="issueData.issue_internal_id + ''" matTooltip="{{'ISSUE-VIEW.ISSUE-ID' | translate}}">
                {{issueData?.issue_internal_id}}
            </span>
            <div class="vertical-separator"></div>
            <span *ngIf="hasComments()" (click)="toggleCommentSidebar($event)" matTooltipPosition="above" matTooltip="{{'ISSUE-VIEW.HAS-COMMENTS' | translate}}">
                <i-lucide size="16" name='message-circle' class="comment-icon"></i-lucide>
            </span>
            <span class="body__header__title" [matTooltip]="issueData?.subject?.length > 50 ? issueData?.subject : ''" (click)="copyIssueLink($event)">
                {{issueData?.subject}}
            </span>
        </div>

        <div class="separator" *ngIf="messageCounter >= MINIMUM_MESSAGES_FOR_SEPARATOR">
            <div class="separator-container">
                <div class="separator-container-dimension">
                    <span (click)="toggleIssues()" class="separator-circle" [ngClass]="{'expanded': showAllIssues}">
                        <span *ngIf="!showAllIssues">{{messageCounter}}</span>
                    </span>
                </div>
            </div>
        </div>


        <div class="body__content" #messagesContainer [ngClass]="{'body__content__height': isEditorOpen, 'body__content__accordion': isAccordionOpen('main')}">
            <ng-container *ngIf="lastIssueSentence && !showAllIssues">
                <div *ngFor="let sentenceItem of lastPairIssueSentence" class="sentence-item">
                    <div class="sentence-header" *ngIf="sentenceItem.IssueSentence.speaker !== 'log'">
                        <div class="sentence-header__left">
                            <app-user-avatar-display
                                    [size]="28"
                                    [userId]="+sentenceItem.User.id"
                                    *ngIf="sentenceItem.User.id; else initiatorAvatar"
                                    (click)="openUser(sentenceItem.User.id)"
                                    class="cursor-pointer">
                            </app-user-avatar-display>

                            <ng-template #initiatorAvatar>
                                <ngx-avatars
                                        [size]="28"
                                        [name]="userName"
                                        [bgColor]="defaultAvatarBgColor"
                                        (click)="openClientDetails()"
                                        class="cursor-pointer">
                                </ngx-avatars>
                            </ng-template>

                            <div *ngIf="sentenceItem.User.id; else isInitiator">
                    <span class="user-description cursor-pointer" (click)="openUser(sentenceItem.User.id)">
                        {{sentenceItem.User?.firstname}} {{sentenceItem.User?.lastname}}
                    </span>
                            </div>

                            <ng-template #isInitiator>
                    <span (click)="openClientDetails()" class="cursor-pointer">
                        {{initiatorData?.first_name}} {{initiatorData.last_name}}
                    </span>
                            </ng-template>
                        </div>
                        <div class="sentence-header__right">
                            <span>{{sentenceItem.IssueSentence.modified | dateRespondoFormat}}</span>
                        </div>
                    </div>
                    <div [innerHTML]="sentenceItem?.IssueSentence?.body | safeHtml" class="issue-view-body"></div>
                    <div class="mb-10 sentence-container-attachments" *ngIf="sentenceItem?.IssueSentence?.speaker !== 'log'">
                        <app-issue-attachments
                                [commonFileObject]="sentenceItem?.CommonFileObject"
                                [issueSentence]="sentenceItem?.IssueSentence"
                        ></app-issue-attachments>
                        <div *ngIf="+issueData.unread && lastSentOwnerSentenceId === +sentenceItem.IssueSentence?.id" class="undo-send">
                            <button
                                    mat-button
                                    color="primary"
                                    (click)="undoSendSentence()"
                                    matTooltip="{{'ISSUE-VIEW-LIST.REPLY' | translate}}"
                                    appIssueActionButton="undoSendSentence"
                                    [issue]="issueData"
                                    [hideWhenNotAllowed]="true">
                                <mat-icon class="undo-send-btn">undo</mat-icon>
                            </button>
                        </div>
                    </div>
                    <div class="horizontal-separator"></div>
                </div>
            </ng-container>

            <div [ngClass]="{
                   'fade-in': showAllIssues && hasInteracted,
                   'fade-out': !showAllIssues && hasInteracted,
                   'hidden': !hasInteracted
                 }">
                <div *ngFor="let issue of allIssueSentences">
                    <div class="sentence-header" *ngIf="issue.IssueSentence.speaker !== 'log'">
                        <div class="sentence-header__left">
                            <app-user-avatar-display
                                size="28"
                                [userId]="+issue.User.id"
                                *ngIf="issue.User.id; else initiatorAvatar"
                                (click)="openUser(issue.User.id)"
                                class="cursor-pointer">
                            </app-user-avatar-display>

                            <ng-template #initiatorAvatar>
                                <ngx-avatars
                                    [size]="28"
                                    [name]="userName"
                                    [bgColor]="defaultAvatarBgColor"
                                    (click)="openClientDetails()"
                                    class="cursor-pointer">
                                </ngx-avatars>
                            </ng-template>

                            <div *ngIf="issue.User.id; else isInitiator">
                                <span class="user-description cursor-pointer" (click)="openUser(issue.User.id)">
                                    {{issue.User?.firstname}} {{issue.User?.lastname}}
                                </span>
                            </div>

                            <ng-template #isInitiator>
                                <span (click)="openClientDetails()" class="cursor-pointer">
                                    {{initiatorData?.first_name}} {{initiatorData.last_name}}
                                </span>
                            </ng-template>
                        </div>
                        <div class="sentence-header__right">

                            <span>{{issue.IssueSentence.modified | dateRespondoFormat}}</span>
                        </div>
                    </div>
                    <div class="issue-view-body" [innerHTML]="issue.IssueSentence?.body | safeHtml"></div>
                    <div class="sentence-container-attachments">
                        <app-issue-attachments [commonFileObject]="issue?.CommonFileObject"
                                               [issueSentence]="issue?.IssueSentence">
                        </app-issue-attachments>
                        <div *ngIf="+issueData?.unread && lastSentOwnerSentenceId === +issue.IssueSentence?.id" class="undo-send">
                            <button
                                    mat-button
                                    color="primary"
                                    (click)="undoSendSentence()"
                                    matTooltip="{{'ISSUE-VIEW-LIST.REPLY' | translate}}"
                                    appIssueActionButton="undoSendSentence"
                                    [issue]="issueData"
                                    [hideWhenNotAllowed]="true">
                                <mat-icon class="undo-send-btn">undo</mat-icon>
                            </button>
                        </div>
                    </div>

                    <div class="horizontal-separator"></div>
                </div>
            </div>
        </div>
        <div class="body__editor" [ngClass]="{'body__editor__height': isEditorOpen, 'body__editor__accordion': isEditorOpen && isAccordionOpen('main')}">
            <app-issue-editor
                    *ngIf="isEditorOpen"
                    class="editor"
                    [disabled]="!editingAllowed || editorBlocked"
                    [issueData]="issueData"
                    [lastSentence]="lastSentenceBody"
                    [isSimplifiedView]="true"
                    (issueLocked)="onIssueLocked($event)"
                    (isCommentSidebarOpen)="openCommentSidebar()"
                    [isEditorOpen]="isEditorOpen"
                    (isEditorOpenChange)="isEditorOpen = $event"
            ></app-issue-editor>
            <div *ngIf="!isEditorOpen" class="body__editor__toolbar">
                <div class="body__editor__toolbar__left">
                    <app-5ways-button iconRight="pencil-line" variant="ghost" (click)="openEditor()">
                        {{'ISSUE-VIEW-SIDEBAR.ANSWER' | translate}}
                    </app-5ways-button>
                </div>

                <div class="body__editor__toolbar__right">
                    <app-5ways-button [iconSize]="24" iconRight="maximize" matTooltip="{{'ICONS.FULLSCREEN' | translate}}" variant="ghost" (middleclick)="openFull(true)" (click)="openFull(false)">
                    </app-5ways-button>
                </div>
            </div>
        </div>
    </div>
    <div class="footer">
        <div class="footer__left">
            <div class="footer--buttons footer__left">
                <ng-container *ngIf="issueData.status !== 'closed'; else isClosed">
                    <app-5ways-button iconLeft="user-round-plus" [variant]="ButtonVariant.SECONDARY" appIssueActionButton="takeIssue" [issue]="issueData" [hideWhenNotAllowed]="true" (click)="clickTake()">
                        {{'ISSUE-VIEW-MENU.TAKE-ISSUE' | translate}}
                    </app-5ways-button>
                    <app-5ways-button *ngIf="'delegateIssue' | checkPermissionName" iconLeft="circle-user" [variant]="ButtonVariant.SECONDARY" (click)="clickDelegate(issueData.id)" appIssueActionButton="delegateIssue" [issue]="issueData" [hideWhenNotAllowed]="true">
                        {{ 'ISSUE-LIST-POSITION.DELEGATE' | translate }}
                    </app-5ways-button>
                    <app-5ways-button *ngIf="'closeIssue' | checkPermissionName" iconLeft="circle-check" [variant]="ButtonVariant.SECONDARY" appIssueActionButton="closeIssue" [issue]="issueData" [hideWhenNotAllowed]="false" (click)="clickClose()">
                        {{'ISSUE-VIEW-MENU.CLOSE-ISSUE' | translate}}
                    </app-5ways-button>
                </ng-container>
                <ng-template #isClosed>
                    <app-5ways-button iconLeft="circle-user" [variant]="ButtonVariant.SECONDARY" (click)="clickDelegate(issueData.id)" appIssueActionButton="delegateIssue" [issue]="issueData" [hideWhenNotAllowed]="true">
                        {{ 'ISSUE-LIST-POSITION.DELEGATE' | translate }}
                    </app-5ways-button>
                    <app-5ways-button iconLeft="lock-keyhole-open" [variant]="ButtonVariant.SECONDARY" appIssueActionButton="openIssue" [issue]="issueData" [hideWhenNotAllowed]="true" (click)="clickOpen()">
                        {{'ISSUE-LIST-POSITION.OPEN' | translate}}
                    </app-5ways-button>
                </ng-template>
            </div>
            <div class="tags">
                <app-tags place="issue" [objectName]="tagObjectName" [tagsIds]="issueData.tags" [objectId]="issueData.id" [editMode]="tagsEditMode"></app-tags>
            </div>
        </div>
        <div class="footer__right">
            <ng-container *ngIf="isEditorOpen">
                <app-5ways-button
                    matTooltip="{{'ICONS.FULLSCREEN' | translate}}"
                    iconRight="maximize"
                    [iconSize]="24"
                    [variant]="ButtonVariant.GHOST"
                    (middleclick)="openFull(true)"
                    (click)="openFull(false)">
                </app-5ways-button>
            </ng-container>
            <app-more-options [menuItems]="moreOptionsMenu"></app-more-options>
        </div>
    </div>
    <app-issue-comment-sidebar [(isOpen)]="isCommentsSidebarOpen" [issueId]="issueData.id" [issueCommentGroups]="issueCommentGroups" [issueComments]="issueComments" [isOpenFromDetails]="false"></app-issue-comment-sidebar>
</div>
