<ng-container *ngIf="showButton">
    <ng-container *ngIf="!(isMobileService.isMobileView | async); else mobile">
        <button
            *ngIf="!sending"
            [disabled]="_disabled"
            (click)="onClick($event)"
            mat-stroked-button
            class="button-rounded primary my-custom-button "
            color="primary"
        >
            <ng-container [ngSwitch]="actionType">
                <ng-container *ngSwitchCase="'sendToAccept'">
                    <i-lucide class="icon" name="check" size="18"></i-lucide>
                    {{'ISSUE-ACTION-BUTTON.SEND-ACCEPT' | translate}}
                </ng-container>

                <ng-container *ngSwitchCase="'sendIssue'">
                    <i-lucide class="icon" name="send" size="18"></i-lucide>
                    {{'ISSUE-ACTION-BUTTON.SEND' | translate}}
                </ng-container>

                <ng-container *ngSwitchCase="'undoSendToAccept'">
                    <i-lucide class="icon" name="undo" size="18"></i-lucide>
                    {{'ISSUE-ACTION-BUTTON.REVERSE-ACCEPT' | translate}}
                </ng-container>
            </ng-container>
        </button>
    </ng-container>

    <ng-template #mobile>
        <button
            *ngIf="!sending"
            [disabled]="_disabled"
            mat-mini-fab
            [color]="colorMobile"
            (click)="onClick($event)"
            class="mat-elevation-z0"
        >
            <ng-container [ngSwitch]="actionType">
                <i-lucide class="icon" *ngSwitchCase="'sendToAccept'" name="check" size="18"></i-lucide>
                <i-lucide class="icon" *ngSwitchCase="'sendIssue'" name="send" size="18"></i-lucide>
                <i-lucide class="icon" *ngSwitchCase="'undoSendToAccept'" name="undo" size="18"></i-lucide>
            </ng-container>
        </button>
    </ng-template>

    <button mat-stroked-button color="primary" type="button"  class="button-rounded primary" *ngIf="sending">
        <mat-spinner diameter="20" color="primary" [ngStyle]="{display: 'inline-block', 'margin-right': '4px'}"></mat-spinner>
        {{'ISSUE-ACTION-BUTTON.WAIT' | translate}}
    </button>
</ng-container>
