import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {UserInputStorageService} from '../../services/user-input-storage.service';
import {ToolbarFilter} from '../issue-list-toolbar/issue-list-toolbar.component';
import {SanitizeColumnIdPipe} from '../../shared/pipes/sanitize-column-id.pipe';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective, DefaultLayoutGapDirective } from 'ngx-flexible-layout/flex';
import { SearchInputComponent } from '../../elements/search-input/search-input.component';
import { FormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';
import { IssueSortListComponent } from '../issue-sort-list/issue-sort-list.component';
import { ListSortTypeComponent } from '../../shared/list-sort-type/list-sort-type.component';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-issue-list-users-toolbar',
    templateUrl: './issue-list-users-toolbar.component.html',
    styleUrls: ['./issue-list-users-toolbar.component.scss'],
    imports: [DefaultLayoutDirective, DefaultLayoutAlignDirective, DefaultLayoutGapDirective, SearchInputComponent, FormsModule, NgIf, IssueSortListComponent, ListSortTypeComponent, TranslatePipe]
})
export class IssueListUsersToolbarComponent implements OnInit {
    @Input() ownerId: number;

    @Input() set initialSortField(value: string) {
        this.sortField = value;
    }

    @Input() set initialSortType(value: string) {
        this.sortType = value;
    }

    @Output() filtersChanged: EventEmitter<ToolbarFilter> = new EventEmitter();

    sortField = 'waiting_time';
    sortType = 'asc';
    searchTerm = '';
    showFilters = false;

    constructor(
        private userInputStorageService: UserInputStorageService,
        private sanitizeColumnIdPipe: SanitizeColumnIdPipe
    ) {
    }

    private saveFilterSettings(listName: string) {
        if (!listName) {
            return;
        }

        const settings = {
            sortField: this.sortField,
            sortType: this.sortType
        };

        this.userInputStorageService.setValue('filters_' + listName, JSON.stringify(settings));
    }

    private loadFilterSettings(listName: string) {
        if (!listName) {
            return;
        }

        const settings = JSON.parse(this.userInputStorageService.getValue('filters_' + listName));

        if (!settings) {
            return;
        }

        const otherSettings = Object.keys(settings).filter((key: string) => key !== 'filters');

        if (otherSettings) {
            otherSettings.forEach((key: string) => {
                this[key] = settings[key];
            });
        }
    }

    private getFiltersString() {
        let query = this.ownerId ? `Issue_owner_id=${this.ownerId}` : '';

        if (this.searchTerm) {
            query += '&search=' + this.searchTerm + '&search_in=Issue_issue_internal_id,Issue_subject,IssueSentence_body&search_type=AND';
        }

        if (this.sortField) {
            const sanitizedSortField = this.sanitizeColumnIdPipe.transform(this.sortField);
            query += '&order=' + sanitizedSortField;

            if (this.sortType) {
                query += '|' + this.sortType;
            }
        }

        return query;
    }

    applyFilters() {
        this.saveFilterSettings('user_issues');
        this.filtersChanged.emit({
            type: 'QUERY',
            data: this.getFiltersString(),
            showLoader: false
        });
    }

    ngOnInit() {
        this.loadFilterSettings('user_issues');
        this.applyFilters();
    }

    onSortFieldChange(value) {
        this.sortField = this.sanitizeColumnIdPipe.transform(value);
        this.applyFilters();
    }

    onSortTypeChange(value) {
        this.sortType = value;
        this.applyFilters();
    }

    clearSearch() {
        this.searchTerm = '';
        this.applyFilters();
    }
}
