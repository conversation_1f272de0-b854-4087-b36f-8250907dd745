import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {of} from 'rxjs';
import {map, take, tap} from 'rxjs/operators';
import {KeyValue} from '../../common/interfaces/key-value.interface';
import {IssueCountersService} from '../../services/issue-counters.service';
import {UserInputStorageService} from '../../services/user-input-storage.service';
import {IsMobileService} from '../../services/is-mobile.service';
import {UserStoreService} from '../../services/store/user-store.service';
import dayjs from 'dayjs';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {FilterOption} from '../../common/interfaces/filter-option.interface';
import {ButtonVariant} from '../../common/enums/button-variant.enum';
import {SanitizeColumnIdPipe} from '../../shared/pipes/sanitize-column-id.pipe';
import { DefaultClassDirective, DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { MatButton } from '@angular/material/button';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { MatBadge } from '@angular/material/badge';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective, DefaultLayoutGapDirective } from 'ngx-flexible-layout/flex';
import { SearchInputComponent } from '../../elements/search-input/search-input.component';
import { FormsModule } from '@angular/forms';
import { UserGroupFilterComponent } from '../../shared/user-group-filter/user-group-filter.component';
import { TagFilterComponent } from '../../shared/tag-filter/tag-filter.component';
import { DropdownFilterComponent } from '../../shared/dropdown-filter/dropdown-filter.component';
import { IssueActivityFilterComponent } from '../../shared/issue-activity-filter/issue-activity-filter.components';
import { UsersFilterComponent } from '../../shared/users-filter/users-filter.component';
import { ButtonComponent } from '../../elements/button/button.component';
import { CheckPermissionNamePipe } from '../../shared/pipes/check-permission-name.pipe';

export interface ToolbarFilter {
    type: string;
    data: string;
    showLoader: boolean;
}

@Component({
    selector: 'app-issue-list-toolbar',
    templateUrl: './issue-list-toolbar.component.html',
    styleUrls: ['./issue-list-toolbar.component.scss'],
    imports: [DefaultClassDirective, MatButton, DefaultShowHideDirective, NgIf, MatIcon, MatBadge, DefaultLayoutDirective, DefaultLayoutAlignDirective, SearchInputComponent, FormsModule, NgTemplateOutlet, DefaultLayoutGapDirective, UserGroupFilterComponent, TagFilterComponent, DropdownFilterComponent, IssueActivityFilterComponent, UsersFilterComponent, ButtonComponent, CheckPermissionNamePipe, TranslatePipe]
})
export class IssueListToolbarComponent implements OnInit {
    @Input() listName: string;
    @Input() visibleFilters = [];

    @Input() set initialOwnerId(value: number) {
        this.ownerId = +value;
    }

    @Input() set initialSortField(value: string) {
        this.sortField = value;
    }

    @Input() set initialSortType(value: string) {
        this.sortType = value;
    }

    @Output() filtersChanged: EventEmitter<ToolbarFilter> = new EventEmitter();

    @ViewChild('searchInput') searchInput: ElementRef;

    filtersCounter = 0;
    filtersVisibility: any = {};
    groupFilterId: number;
    ownerId: number;
    showListSettings = false;
    sortField = 'waiting_time';
    sortType = 'asc';
    priorities: FilterOption[] = [
        {value: 1, viewValue: this.translate.instant('SHARED.FILTER-OPTION-WITH-PRIORITY')},
        {value: 0, viewValue: this.translate.instant('SHARED.FILTER-OPTION-WITHOUT-PRIORITY')}
    ];

    filters = {
        tags: {
            value: [],
            savable: true,
            getFilterString: () => this.filters.tags.value.length ? 'Issue_tags=' + this.filters.tags.value.map(item => '(' + item + ')').join(' ') : ''
        },
        priority: {
            value: -1,
            savable: true,
            getFilterString: () => (Number.isFinite(this.filters.priority.value) && this.filters.priority.value >= 0)
                ? 'Issue_priority=' + this.filters.priority.value
                : ''
        },
        groupOwners: {
            value: [],
            savable: true,
            getFilterString: () => this.filters.groupOwners.value.length ? 'Issue_owner_id=' + this.filters.groupOwners.value.join(',') : ''
        },
        owner: {
            value: -1,
            savable: true,
            getFilterString: () => (Number.isFinite(this.filters.owner.value) && this.filters.owner.value > 0) ? 'Issue_owner_id=' + this.filters.owner.value : ''
        },
        search: {
            value: '',
            savable: false,
            getFilterString: () => this.filters.search.value ? 'search=' + this.filters.search.value + '&search_in=Issue_id,Issue_subject,IssueSentence_body&search_type=AND' : ''
        },
        lastActivity: {
            value: null,
            savable: true,
            getFilterString: () => this.getPeriodTime(this.filters.lastActivity.value)
        }
    };

    readonly ButtonVariant = ButtonVariant;

    constructor(
        private issueCountersService: IssueCountersService,
        private userInputStorageService: UserInputStorageService,
        private userStoreService: UserStoreService,
        public isMobileService: IsMobileService,
        public translate: TranslateService,
        private sanitizeColumnIdPipe: SanitizeColumnIdPipe
    ) {
    }

    private getPeriodTime(numberOfMonth: number) {
        const dateStart = new Date();

        dateStart.setMonth(dateStart.getMonth() - numberOfMonth);

        return numberOfMonth > 0 ? 'Issue_answer_time=gte_' + dayjs(dateStart).format('YYYY-MM-DD') + ' 00:00:00' : '';
    }

    private getOwnerFilterString() {
        if (this.ownerId >= 0) {
            return 'Issue_owner_id=' + this.ownerId;
        }

        if (this.listName === 'delegated' && (this.filters.groupOwners.value.length === 0 && this.filters.owner.value === -1)) {
            return 'Issue_owner_id=gt_0';
        }

        if (this.ownerId <= 0) {
            return '';
        }

        if (this.filters.groupOwners.value.length || this.filters.owner.value) {
            return '';
        }

        return '';
    }

    private setFiltersVisibility() {
        this.visibleFilters.forEach(item => {
            this.filtersVisibility[item] = true;
        });
    }

    private getFilters(filters: any) {
        const filterKeys = Object.keys(filters);

        if (!filterKeys.length) {
            return '';
        }

        return filterKeys.map(key => filters[key].getFilterString())
            .filter(element => element !== '')
            .join('&');
    }

    private getOrder() {
        if (!this.sortField) {
            return '';
        }

        const sanitizedSortField = this.sanitizeColumnIdPipe.transform(this.sortField);
        const query = 'order=' + sanitizedSortField;

        return this.sortType ? query + '|' + this.sortType : query;
    }

    private getFiltersString(filters: any) {
        const queryArr = [];

        queryArr.push(this.getFilters(filters));
        queryArr.push(this.getOwnerFilterString());
        queryArr.push(this.getOrder());

        return queryArr.filter(item => !!item).join('&');
    }

    private updateIssueCounters() {
        if (this.listName) {
            const currentSettings = JSON.parse(this.userInputStorageService.getValue('filters_' + this.listName)) || {};
            currentSettings.groupFilterId = this.groupFilterId;

            this.userInputStorageService.setValue('filters_' + this.listName, JSON.stringify(currentSettings));
        }

        this.issueCountersService.refreshListCounter.next({
            listName: this.listName,
            userGroupId: this.groupFilterId
        });
    }

    private saveFilterSettings(listName: string) {
        if (!listName) {
            return;
        }

        const filtersSettings = Object.keys(this.filters).filter(item => this.filters[item].savable).map((item: string) => ({key: item, value: this.filters[item].value})),
            settings = {
                filters: filtersSettings,
                groupFilterId: this.groupFilterId,
                sortField: this.sortField,
                sortType: this.sortType
            };

        this.userInputStorageService.setValue('filters_' + listName, JSON.stringify(settings));
    }

    private loadFilterSettings(listName: string) {
        if (!listName) {
            return;
        }

        const settings = JSON.parse(this.userInputStorageService.getValue('filters_' + listName));

        if (!settings) {
            return;
        }

        if (settings.filters) {
            settings.filters.forEach((item: KeyValue) => {
                if (this.filters[item.key]) {
                    this.filters[item.key].value = item.value;
                }
            });
        }

        const otherSettings = Object.keys(settings).filter((key: string) => key !== 'filters');

        if (otherSettings) {
            otherSettings.forEach((key: string) => {
                this[key] = settings[key];
            });
        }
    }

    private emitDataAndSetCounter(type: string, data: string) {
        this.filtersChanged.emit({
            type,
            data,
            showLoader: !!this.filters.search.value
        });
    }

    private applyFilters(saveFilters = true) {
        if (saveFilters) {
            this.saveFilterSettings(this.listName);
        }

        if (this.filters.owner.value > 0 && this.filters.groupOwners.value.length) {
            if (this.filters.groupOwners.value.includes(+this.filters.owner.value)) {
                const filters = {...this.filters};

                filters.groupOwners.value = [];
                this.emitDataAndSetCounter('QUERY', this.getFiltersString(filters));
            } else {
                this.emitDataAndSetCounter('EMPTY', '');
            }

        } else {
            this.emitDataAndSetCounter('QUERY', this.getFiltersString(this.filters));
        }
    }

    private setUsersAndApplyFilters() {
        const users$ = this.groupFilterId
            ? this.userStoreService.getUsersForGroup(this.groupFilterId)
                .pipe(
                    take(1),
                    map((users: any[]) => users.map(user => +user.User.id)),
                    tap(users => this.filters.groupOwners.value = users.length ? users : [-1])
                )
            : of(null).pipe(tap(() => this.filters.groupOwners.value = []));

        users$.subscribe(() => {
            this.applyFilters();
            this.updateIssueCounters();
        });
    }

    ngOnInit() {
        this.setFiltersVisibility();
        this.loadFilterSettings(this.listName);
        this.setUsersAndApplyFilters();
    }

    search() {
        if (!this.filters.search.value.trim()) {
            this.clearSearch();
        } else {
            isNaN(+this.filters.search.value)
                ? this.applyFilters()
                : this.emitDataAndSetCounter('1_ISSUE', this.filters.search.value);
        }
    }

    clearSearch() {
        this.filters.search.value = '';
        this.applyFilters();
    }

    onGroupChange(value) {
        this.groupFilterId = value;
        this.setUsersAndApplyFilters();
    }

    onPriorityChange(value) {
        this.filters.priority.value = value;
        this.applyFilters();
    }

    onUsersChange(value) {
        this.filters.owner.value = value ?? -1;
        this.applyFilters();
    }

    onTagChange(value: any[]) {
        this.filters.tags.value = value;
        this.applyFilters();
    }

    onLastActivityChange(value) {
        this.filters.lastActivity.value = value;
        this.applyFilters();
    }

    removeFilters() {
        Promise.resolve()
            .then(() => {
                this.userInputStorageService.setValue('filters_' + this.listName, {});

                return { success: true, message: 'Filters cleared successfully' };
            })
            .then((result) => {
                if (result.success) {
                    this.filters.tags.value = [];
                    this.filters.priority.value = -1;
                    this.filters.owner.value = -1;
                    this.filters.lastActivity.value = -1;
                    this.filters.groupOwners.value = [];
                    this.groupFilterId = null;

                    this.applyFilters();
                    this.updateIssueCounters();

                    return { success: true, message: 'Filters reset and UI updated' };
                }
            })
            .catch(error => {
                console.error('Error clearing filters:', error);
            });
    }

}
