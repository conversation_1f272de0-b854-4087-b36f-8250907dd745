@use "../../../variables" as *;

.list-toolbar-settings {
    flex-direction: row;
    display: flex;
    width: 100%;
    background-color: var(--background-highlight-color);
    justify-content: space-between;
    max-height: 600px;
    overflow: hidden;

    @media (max-width: 1279px) {
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}

.search-button, .clear-filters-button {
    border: none;
    margin-top: -4px;

    @media (min-width: 1280px) {
        padding: 13px 7px;
    }

    @media (min-width: 1360px) {
        padding: 13px 15px;
    }
}

.options.mobile button {
    font-size: 12px;
}

.options {
    flex-flow: column wrap;
}

.search-input {
    width: 300px;
}

.toolbar-icon {
    color: $dark-blue;
    cursor: pointer;
    font-size: 25px !important;
    margin-top: -25px;
}

.show-settings {
    padding-top: 5px;
    width: 100%;
    z-index: 1;

    .filters-count {
        margin-left: 15px;
    }
}

.user-mode-filters {
    margin-top: 20px;
}

.list-toolbar {
    z-index: 1;
}

.toolbar-icon {
    cursor: pointer;
    font-size: 16px;
}

.toggle-view .list-toolbar-settings {
    padding: 10px;

    &.collapsed {
        height: 0;
        padding: 0 10px;
    }

    .sort {
        @media (min-width: 1280px) {
            padding-top: 20px;
        }
    }
}

.sort {
    &-list {
        @media (min-width: 1280px) {
            margin-right: 2px;
        }

        @media (min-width: 1360px) {
            margin-right: 15px;
        }
    }
}

.delete-icon {
    width: 16px;
}
