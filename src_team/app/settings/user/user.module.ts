import {LOCALE_ID, NgModule} from '@angular/core';
import {SharedModule} from '../../shared/shared.module';
import {TranslateModule} from '@ngx-translate/core';
import localePl from '@angular/common/locales/pl';
import {DatePipe, registerLocaleData} from '@angular/common';
import {PdfMakeWrapper} from 'pdfmake-wrapper';
import pdfFonts from 'pdfmake/build/vfs_fonts';

registerLocaleData(localePl);

PdfMakeWrapper.setFonts(pdfFonts);
@NgModule({
    imports: [
        SharedModule,
        TranslateModule
    ],
    declarations: [
    ],
    exports: [],
    providers: [
        DatePipe,
        {
            provide: LOCALE_ID, useValue: 'pl'
        }
    ]
})
export class UserModule {
}
