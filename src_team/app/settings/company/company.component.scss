@use '../../../variables' as *;

.company-settings {
  padding: 16px;
  overflow: auto;
  height: 100%;

  &__container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 50px;
  }

  &__panel {
    border-radius: $container-border-radius;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    &-title {
      color: $fiveways-gray;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 8px;
    }

    &-subtitle {
      color: $client-label-color;
      font-size: 14px;
      font-weight: 400;
      margin-bottom: 24px;
    }
  }

  &__form {
    display: flex;
    flex-direction: column;
    gap: 16px;

    &-row {
      display: flex;
      gap: 16px;
      width: 100%;

      @media (max-width: 992px) {
        flex-direction: column;
      }
    }

    &-field {
      width: 100%;
    }
  }

  &__toggle {
    margin-bottom: 16px;
    color: $fiveways-gray;

    &-label {
      font-size: 14px;
    }
  }

  &__form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }

  &__tax-rate {
    width: 100%;
    max-width: 300px;
  }
}
