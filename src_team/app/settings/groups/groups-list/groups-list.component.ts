import {Component, OnInit} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {GroupAddComponent} from '../group-add/group-add.component';
import {GroupService} from '../../../services/group.service';
import {shareReplay} from 'rxjs/operators';
import { DefaultLayoutDirective } from 'ngx-flexible-layout/flex';
import { Mat<PERSON>ardActions, MatCard, MatCardTitle, MatCardSubtitle } from '@angular/material/card';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatNavList, MatListItem } from '@angular/material/list';
import { NgFor, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-groups-list',
    templateUrl: './groups-list.component.html',
    styleUrls: ['./groups-list.component.scss'],
    imports: [DefaultLayoutDirective, MatCardActions, MatButton, MatIcon, MatCard, MatCardTitle, MatCardSubtitle, MatNavList, NgFor, MatListItem, AsyncPipe, TranslatePipe]
})
export class GroupsListComponent implements OnInit {
  groupsList$;

  dialogSettings = {
    width: '290px',
    panelClass: 'full-width-dialog'
  };

  constructor(private groupService: GroupService, public dialog: MatDialog) {
  }

  ngOnInit(): void {
    this.groupsList$ = this.groupService.getGroupListObs().pipe(shareReplay(1));
  }

  editGroup(data) {
    const dataC = {data: JSON.parse(JSON.stringify(data))};

    this.dialog.open(GroupAddComponent, {...this.dialogSettings, ...dataC});
  }

  addNewGroup() {
    this.dialog.open(GroupAddComponent, this.dialogSettings);
  }

}
