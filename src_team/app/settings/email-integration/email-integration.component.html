<div class="mail-settings">
    <mat-card class="panel w-98 ml-15 mail-integration">
        <mat-card-title>
            {{ 'MAIL-INTEGRATION.TITLE' | translate }}
            <mat-card-subtitle class="mb-10 mt-10">
                <span>{{ 'MAIL-INTEGRATION.SUBTITLE' | translate }}</span>
            </mat-card-subtitle>
        </mat-card-title>

        <form [formGroup]="mailIntegration">
            <ng-container [ngSwitch]="getEmailIntegrationStep">
                <ng-container *ngSwitchCase="EmailIntegrationStep.SELECT_INTEGRATION_METHOD">
                    <div class="integration-methods">
                        <button class="mail-settings-btn" type="button" (click)="setEmailIntegrationStep({ step: EmailIntegrationStep.GMAIL })">
                            <mat-icon svgIcon="google"></mat-icon>
                            <span class="ml-2">{{ 'MAIL-INTEGRATION.GMAIL' | translate }}</span>
                        </button>
                        <button class="mail-settings-btn" type="button" (click)="setEmailIntegrationStep({ step: EmailIntegrationStep.OUTLOOK })">
                            <mat-icon svgIcon="outlook"></mat-icon>
                            <span class="ml-2">{{ 'MAIL-INTEGRATION.OUTLOOK' | translate }}</span>
                        </button>
                        <button class="mail-settings-btn" type="button" (click)="setEmailIntegrationStep({ step: EmailIntegrationStep.AUTO_DETECT_PROVIDER })">
                            <app-5ways-icon name="refresh-cw" stroke="black"></app-5ways-icon>
                            <span class="ml-2">{{ 'MAIL-INTEGRATION.AUTO-DETECT' | translate }}</span>
                        </button>
                        <button class="mail-settings-btn" type="button" (click)="setEmailIntegrationStep({ step: EmailIntegrationStep.MANUL_CONFIGURATION })">
                            <app-5ways-icon name="settings" stroke="black"></app-5ways-icon>
                            <span class="ml-2">{{ 'MAIL-INTEGRATION.MANUAL-CONFIG' | translate }}</span>
                        </button>
                    </div>
                </ng-container>
                <ng-container *ngSwitchCase="EmailIntegrationStep.AUTO_DETECT_PROVIDER">
                    <div class="d-flex col mb-16 mt-5">
                        <mat-form-field appearance="outline" class="w-30 mr-20">
                            <mat-label>{{ 'MAIL-INTEGRATION.EMAIL.LABEL' | translate }}</mat-label>
                            <input
                                matInput
                                formControlName="mail"
                                [required]="true"
                                (paste)="trimEmailInput($event)"
                                (keydown.enter)="$event.preventDefault(); isProviderDetected && !mailIntegration.get('mail').invalid && setEmailIntegrationStep({ step: EmailIntegrationStep.AUTO_DETECT_FINAL })">
                        </mat-form-field>
                        <span *ngIf="devMode">Wykryty dostawca poczty: {{ selectedProvider }}</span>
                    </div>
                    <div class="mail-settings__navigation w-30">
                        <app-5ways-button [variant]="ButtonVariant.GHOST" (click)="setEmailIntegrationStep({ step: EmailIntegrationStep.SELECT_INTEGRATION_METHOD })">
                            <app-5ways-icon name="arrow-left" stroke="black"></app-5ways-icon>
                            {{ 'MAIL-INTEGRATION.BUTTONS.BACK' | translate }}
                        </app-5ways-button>
                        <app-5ways-button
                            [variant]="ButtonVariant.GHOST"
                            (click)="setEmailIntegrationStep({ step: EmailIntegrationStep.AUTO_DETECT_FINAL })"
                            [disabled]="!isProviderDetected || mailIntegration.get('mail').invalid"
                            matTooltip="{{'MAIL-INTEGRATION.TOOLTIP.ENTER-EMAIL-ADDRESS' | translate}}"
                            [matTooltipDisabled]="isProviderDetected">
                            {{ 'MAIL-INTEGRATION.BUTTONS.NEXT' | translate }}
                            <app-5ways-icon name="arrow-right" stroke="black"></app-5ways-icon>
                        </app-5ways-button>
                    </div>
                </ng-container>
                <ng-container *ngSwitchCase="EmailIntegrationStep.AUTO_DETECT_FINAL">
                    <app-5ways-button [variant]="ButtonVariant.GHOST" (click)="setEmailIntegrationStep({ step: EmailIntegrationStep.AUTO_DETECT_PROVIDER })">
                        <app-5ways-icon name="arrow-left" stroke="black"></app-5ways-icon>
                        {{ 'MAIL-INTEGRATION.BUTTONS.BACK' | translate }}
                    </app-5ways-button>
                    <app-smtp-imap-integration [smtpMailServerConfig]="mailServerSmtpConfig" [imapMailServerConfig]="mailServerImapConfig" [showServerConfigs]="false" (changeIntegrationStep)="setEmailIntegrationStep($event)"></app-smtp-imap-integration>
                </ng-container>
                <ng-container *ngSwitchCase="EmailIntegrationStep.MANUL_CONFIGURATION">
                    <app-5ways-button [variant]="ButtonVariant.GHOST" (click)="setEmailIntegrationStep({ step: EmailIntegrationStep.SELECT_INTEGRATION_METHOD })">
                        <app-5ways-icon name="arrow-left" stroke="black"></app-5ways-icon>
                        {{ 'MAIL-INTEGRATION.BUTTONS.BACK' | translate }}
                    </app-5ways-button>
                    <app-smtp-imap-integration (changeIntegrationStep)="setEmailIntegrationStep($event)"></app-smtp-imap-integration>
                </ng-container>
                <ng-container *ngSwitchCase="EmailIntegrationStep.SELECT_MAILBOXES">
                    <app-smtp-imap-integration [smtpMailServerConfig]="mailServerSmtpConfig" [imapMailServerConfig]="mailServerImapConfig" (changeIntegrationStep)="setEmailIntegrationStep($event)"></app-smtp-imap-integration>
                </ng-container>
                <ng-container *ngSwitchCase="EmailIntegrationStep.GMAIL">
                    <app-gmail (changeIntegrationStep)="setEmailIntegrationStep($event)" [mailServerConfiguration]="mailServerConfig"></app-gmail>
                </ng-container>
                <ng-container *ngSwitchCase="EmailIntegrationStep.OUTLOOK">
                    <app-outlook (changeIntegrationStep)="setEmailIntegrationStep($event)"></app-outlook>
                </ng-container>
            </ng-container>
        </form>
    </mat-card>

    <app-footer *ngIf="isIntegrated"></app-footer>
</div>
