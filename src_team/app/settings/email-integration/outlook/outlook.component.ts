import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, inject, OnDestroy, OnInit, Output} from '@angular/core';
import {FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {But<PERSON>Variant} from '../../../common/enums/button-variant.enum';
import {ButtonComponent} from '../../../elements/button/button.component';
import {DownloadAttachments, EmailIntegrationStep, ServerProtocol} from '../../../common/enums/email-integration.enum';
import {EmailIntegrationService} from '../../../services/email-integration.service';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {EmailService} from '../../../services/email.service';
import {EMPTY, of, Subscription} from 'rxjs';
import {IntegrationChangeStepEvent, MailboxOption, MailMailbox, MAILOAuth2Data, MailServer} from '../../../common/interfaces/email-integration.interface';
import {formatDateToYMD, getMinDate} from '../helpers/dateTimeHelper';
import {DataHelperService} from '../../../services/data/data-helper.service';
import {catchError, debounceTime, distinctUntilChanged, map, switchMap} from 'rxjs/operators';
import {FormBuilderValidatorService} from '../../../services/validators/form-builder-validator.service';
import {ConfirmDialogComponent} from '../../../shared/confirm-dialog/confirm-dialog.component';
import {MatDialog} from '@angular/material/dialog';
import {Store} from '@ngrx/store';
import {setMailboxes, setMailIntegrationActive} from '../../../store/mailbox/mailbox.actions';
import {setMailServerState} from '../../../store/mail-server/mail-server.actions';
import {NgClass, NgFor, NgIf} from '@angular/common';
import {MatError, MatFormField, MatLabel, MatSelect, MatSuffix} from '@angular/material/select';
import {MatOption} from '@angular/material/autocomplete';
import {MatInput} from '@angular/material/input';
import {MatDatepicker, MatDatepickerInput, MatDatepickerToggle} from '@angular/material/datepicker';
import {MatChip, MatChipListbox} from '@angular/material/chips';
import {DefaultClassDirective} from 'ngx-flexible-layout/extended';
import {AlertService} from '../../../services/alert.service';
import {AlertType} from '../../../common/enums/alert-type.enum';
import {IconComponent} from '../../../elements/icon/icon.component';

@Component({
    selector: 'app-outlook',
    templateUrl: './outlook.component.html',
    styleUrls: ['./outlook.component.scss'],
    imports: [
        NgIf,
        FormsModule,
        ReactiveFormsModule,
        MatFormField,
        MatLabel,
        MatSelect,
        MatOption,
        MatError,
        MatInput,
        MatDatepickerInput,
        MatDatepickerToggle,
        MatSuffix,
        MatDatepicker,
        MatChipListbox,
        NgFor,
        MatChip,
        NgClass,
        DefaultClassDirective,
        TranslatePipe,
        ButtonComponent,
        IconComponent
    ]
})
export class OutlookComponent implements OnInit, OnDestroy, AfterViewChecked {
    protected readonly ButtonVariant = ButtonVariant;

    @Output() changeIntegrationStep: EventEmitter<IntegrationChangeStepEvent> = new EventEmitter<IntegrationChangeStepEvent>();

    oAuthRedirectUrl = '';
    DownloadAttachments = DownloadAttachments;
    userIntegrationData: FormGroup;
    dataId: number | null; // id wpisu w mail_server
    isRefreshTokenValid: boolean;
    integrationDataSub$: Subscription;
    today: Date = new Date();
    minDate: Date = getMinDate();
    mailboxOptions: MailboxOption[] = [];
    valueChangesSubscription: Subscription;
    isInitialized: boolean = false;
    formChanged: boolean = false;
    isIntegrationChecked: boolean = false;
    checkOrSaveIntegartionButtonText = '';
    authUrlSubscription: Subscription;
    updateSubscription: Subscription;
    postSubscription: Subscription;
    deleteDialogSubscription: Subscription;
    deleteIntegrationSubscription: Subscription;

    private alertService: AlertService = inject(AlertService);

    constructor(
        private readonly emailIntegrationService: EmailIntegrationService,
        private readonly translateService: TranslateService,
        private readonly emailService: EmailService,
        private readonly fb: FormBuilder,
        private readonly dataHelper: DataHelperService,
        private readonly fbValidator: FormBuilderValidatorService,
        private readonly dialog: MatDialog,
        private readonly translate: TranslateService,
        private readonly store: Store
    ) {
        this.checkOrSaveIntegartionButtonText = this.translate.instant('MAIL-INTEGRATION.BUTTONS.CHECK-INTEGRATION');
    }

    ngOnInit() {
        this.pageInitialization();
    }

    ngAfterViewChecked() {
        if (!this.isInitialized) {
            this.isInitialized = true;
        }
    }

    ngOnDestroy() {
        this.valueChangesSubscription?.unsubscribe();
        this.integrationDataSub$?.unsubscribe();
        this.authUrlSubscription?.unsubscribe();
        this.updateSubscription?.unsubscribe();
        this.postSubscription?.unsubscribe();
        this.deleteDialogSubscription?.unsubscribe();
        this.deleteIntegrationSubscription?.unsubscribe();
    }

    pageInitialization() {
        this.initOutlookForm();
        this.fetchIntegrationData();
        this.valueChangesSubscription = this.userIntegrationData.valueChanges
            .pipe(
                debounceTime(300),
                distinctUntilChanged()
            )
            .subscribe(() => {
                if (this.isInitialized) {
                    this.formChanged = true;
                }
            });
    }

    get emailValue() {
        return this.userIntegrationData.get('email')?.value;
    }

    initOutlookForm() {
        this.userIntegrationData = this.fb.group({
            email: new FormControl('', [Validators.email, Validators.required]),
            downloadAttachments: new FormControl(DownloadAttachments.ON, [Validators.required, Validators.pattern('^[0-1]$')]),
            fetchAfterDate: new FormControl(this.today, Validators.required),
            mailboxNames: [[''], [Validators.required, this.fbValidator.nonEmptyStringArray]]
        });
    }

    fetchIntegrationData() {
        this.integrationDataSub$ = this.emailIntegrationService.getIntegrationSettingsData(ServerProtocol.OUTLOOK).pipe(
            switchMap(result => {
                const mailServerData = result[0];
                const userId = mailServerData?.user_id;
                this.dataId = +mailServerData?.id;
                this.store.dispatch(setMailIntegrationActive({ isActive: !!this.dataId }));
                this.isRefreshTokenValid = Boolean(mailServerData?.is_refresh_token_valid);
                if (this.dataId && this.isRefreshTokenValid) {
                    this.patchFormValues(mailServerData);
                } else {
                    this.loadOutlookAuthUrl();
                }

                return this.emailIntegrationService.getMailboxData(userId).pipe(
                    map((mailboxes: MailMailbox[]) => {
                        if (mailboxes && mailboxes.length > 0) {
                            if (mailboxes[0] && mailboxes[0].hasOwnProperty('MailMailbox')) {
                                return mailboxes[0]['MailMailbox'];
                            }
                            return mailboxes[0];
                        }
                        return null;
                    }),

                    catchError(error => {
                        return of(null);
                    })
                );
            }),
            catchError(error => {
                console.error('Błąd w getIntegrationSettingsData', error);
                return EMPTY;
            })
        ).subscribe((mailboxData: MailMailbox | null) => {
            if (mailboxData) {
                this.isIntegrationChecked = true;
                this.checkOrSaveIntegartionButtonText = this.translate.instant('MAIL-INTEGRATION.BUTTONS.SAVE');
                this.handleMailboxData(mailboxData);
            }
        });
    }

    loadOutlookAuthUrl() {
        if (!this.dataId) {
            this.authUrlSubscription = this.emailService.getOutlookAuthUrl().subscribe((redirectUrl) => {
                this.oAuthRedirectUrl = redirectUrl;
                this.onOAuthConsent();
            });
        }
    }

    patchFormValues(data: MailServer) {
        const fetchAfterDate = data.fetch_after_date ? new Date(data.fetch_after_date) : null;

        const parsedMailboxNames = data.mailbox_names ? this.dataHelper.safeParse(data.mailbox_names) : [];

        this.userIntegrationData.patchValue({
            email: data.user_email,
            downloadAttachments: +data.download_attachments,
            fetchAfterDate: fetchAfterDate,
            mailboxNames: parsedMailboxNames
        });

        if (this.dataId) {
            this.store.dispatch(setMailboxes({ mailboxes: parsedMailboxNames }));
        }

        this.mailboxOptions = [];

        for (const mailboxName of parsedMailboxNames) {
            this.mailboxOptions.push({
                value: mailboxName,
                label: mailboxName,
                active: true,
                error: false
            });
        }
    }

    handleMailboxData(mailboxData: MailMailbox) {
        const dynamicMailboxNames = mailboxData.dynamic_mailbox_names || [];
        const parsedMailboxNames = this.userIntegrationData.value.mailboxNames || [];

        const allMailboxNames = Array.from(new Set([...parsedMailboxNames, ...dynamicMailboxNames]));

        this.mailboxOptions = allMailboxNames.map(mailboxName => {
            const isDynamic = dynamicMailboxNames.includes(mailboxName);
            const isParsed = parsedMailboxNames.includes(mailboxName);

            return {
                value: mailboxName,
                label: mailboxName,
                active: isDynamic && isParsed,
                error: isParsed && !isDynamic
            };
        });
    }

    onSubmit() {
        if (this.userIntegrationData.valid) {
            let fetchAfterDateValue = this.userIntegrationData.get('fetchAfterDate').value;
            if (fetchAfterDateValue) {
                fetchAfterDateValue = new Date(fetchAfterDateValue);
            }
            const mailboxNames = this.userIntegrationData.get('mailboxNames').value;

            const formData: MAILOAuth2Data = {
                download_attachments: this.userIntegrationData.get('downloadAttachments').value,
                fetch_after_date: formatDateToYMD(fetchAfterDateValue),
                mailbox_names: JSON.stringify(mailboxNames)
            };

            if (this.dataId) {
                this.updateSubscription = this.emailIntegrationService.updateIntegrationData(this.dataId, formData).subscribe(() => {
                    this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.UPDATED'), AlertType.SUCCESS);
                    this.userIntegrationData.markAsPristine();
                    this.store.dispatch(setMailboxes({ mailboxes: mailboxNames }));
                }, error => {
                    console.error(error);
                    this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.UPDATE-ERROR'), AlertType.ERROR);
                });
            } else {
                this.postSubscription = this.emailIntegrationService.postIntegrationData(formData).subscribe(response => {
                    this.fetchIntegrationData();
                    this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.INTEGRATION-SUCCESS'), AlertType.SUCCESS);
                    this.userIntegrationData.markAsPristine();
                }, error => {
                    console.error(error);
                    this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.INTEGRATION-ERROR'), AlertType.ERROR);
                });
            }
        } else {
            this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.FORM-INVALID'), AlertType.ERROR);
        }
    }

    onOAuthConsent() {
        if (this.oAuthRedirectUrl) {
            window.location.href = this.oAuthRedirectUrl;
        }
    }

    onDelete() {
        this.deleteDialogSubscription = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '400px',
                data: {
                    header: this.translate.instant('MAIL-INTEGRATION.TITLE'),
                    description: this.translate.instant('MAIL-INTEGRATION.MODAL-CHOOSE-DISCONNECT.DESCRIPTION'),
                },
                panelClass: 'full-width-dialog',
                disableClose: true
            }
        ).afterClosed().subscribe(isConfirmed => {
            if (isConfirmed) {
                if (this.dataId) {
                    this.deleteIntegrationSubscription = this.emailIntegrationService.deleteIntegrationData(this.dataId).subscribe(() => {
                        this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.OUTLOOK-DISABLED'), AlertType.SUCCESS);

                        this.dataId = null;
                        this.isIntegrationChecked = false;
                        this.checkOrSaveIntegartionButtonText = this.translate.instant('MAIL-INTEGRATION.BUTTONS.CHECK-INTEGRATION');
                        this.mailboxOptions = [];
                        this.formChanged = false;

                        this.userIntegrationData.reset({
                            email: '',
                            downloadAttachments: DownloadAttachments.ON,
                            fetchAfterDate: this.today,
                            mailboxNames: ['']
                        });

                        this.store.dispatch(setMailServerState({
                            id: null,
                            is_mail_server_token_related: false,
                            is_refresh_token_active: null,
                            server_protocol_name: null
                        }));

                        Object.keys(this.userIntegrationData.controls).forEach(key => {
                            const control = this.userIntegrationData.get(key);
                            control?.clearValidators();
                            control?.setErrors(null);
                            control?.markAsUntouched();
                            control?.markAsPristine();
                            control?.updateValueAndValidity();
                        });

                        this.userIntegrationData.markAsPristine();
                        this.userIntegrationData.markAsUntouched();

                        this.store.dispatch(setMailIntegrationActive({ isActive: !!this.dataId }));
                        this.changeIntegrationStep.emit({
                            step: EmailIntegrationStep.SELECT_INTEGRATION_METHOD
                        });
                    }, error => {
                        console.error(this.translateService.instant('MAIL-INTEGRATION.OUTLOOK-ERROR', error));
                        this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.OUTLOOK-DISABLE-ERROR'), AlertType.ERROR);
                    });
                }
            }
        });
    }

    toggleMailbox(value: string) {
        const updatedMailboxNames = this.mailboxOptions?.map(option =>
            option.value === value ? { ...option, active: !option.active } : option,
        );

        const mailboxNames = updatedMailboxNames.filter(option => option.active).map(option => option.value);

        this.mailboxOptions = [...updatedMailboxNames];

        this.userIntegrationData.get('mailboxNames').setValue(mailboxNames);

        this.userIntegrationData.markAsDirty();
    }

    removeMailbox(value: string) {
        const currentMailboxNames = this.userIntegrationData.get('mailboxNames').value as string[];
        const updatedMailboxNames = currentMailboxNames.filter(item => item !== value);
        this.userIntegrationData.get('mailboxNames').setValue(updatedMailboxNames);

        this.mailboxOptions = this.mailboxOptions?.filter(option => option.value !== value);
    }

}
