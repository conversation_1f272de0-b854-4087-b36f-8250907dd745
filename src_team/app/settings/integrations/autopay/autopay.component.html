<mat-card class="autopay-card mat-elevation-z0">
    <h1 mat-card-title>
        <img class="logo" src="/assets/images/autopay-logo.svg" alt="autopay">
    </h1>

    <div class="form-wrapper" fxLayout="column" fxLayoutGap="10px" ngClass.xs="is-mobile">
        <form [formGroup]="form">
            <div *ngIf="!isLoggedAutopay">
                <div>
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>{{'INTEGRATIONS-SETTINGS.AUTOPAY-SERVICE-ID' | translate}}</mat-label>
                        <input
                            matInput
                            required
                            formControlName="serviceId"
                            name="subject"
                        >
                        <mat-error *ngIf="form.invalid && (formCtrlServiceId.touched || formCtrlServiceId.dirty)">
                            <mat-error *ngIf="formCtrlServiceId.errors?.required">{{'INTEGRATIONS-SETTINGS.FIELD-REQUIRED' | translate}}</mat-error>
                            <mat-error *ngIf="formCtrlServiceId?.errors?.invalidServiceId  && (formCtrlServiceId?.touched || formCtrlServiceId?.dirty)">{{'INTEGRATIONS-SETTINGS.INCORRECT-SERVICE-ID' | translate}}</mat-error>
                        </mat-error>
                    </mat-form-field>
                </div>
                <div>
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>{{'INTEGRATIONS-SETTINGS.AUTOPAY-HASH-KEY' | translate}}</mat-label>
                        <input
                            matInput
                            required
                            formControlName="hashKey"
                            name="subject"
                        >
                        <mat-error *ngIf="form.invalid && (formCtrlHashKey.touched || formCtrlHashKey.dirty)">
                            <mat-error *ngIf="formCtrlHashKey.errors?.required">{{'INTEGRATIONS-SETTINGS.FIELD-REQUIRED' | translate}}</mat-error>
                            <mat-error *ngIf="formCtrlHashKey?.errors?.invalidHashKey">{{'INTEGRATIONS-SETTINGS.INCORRECT-HASH-KEY' | translate}}</mat-error>
                        </mat-error>
                    </mat-form-field>
                </div>
                <div>
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>{{'INTEGRATIONS-SETTINGS.AUTOPAY-BANK-ACCOUNT-NUMBER' | translate}}</mat-label>
                        <input
                            matInput
                            required
                            formControlName="bankAccountNumber"
                            name="subject"
                        >
                        <mat-error *ngIf="form.invalid && (formCtrlBankAccountNumber.touched || formCtrlBankAccountNumber.dirty)">
                            <mat-error *ngIf="formCtrlBankAccountNumber?.errors?.required">{{'INTEGRATIONS-SETTINGS.FIELD-REQUIRED' | translate}}</mat-error>
                            <mat-error *ngIf="formCtrlBankAccountNumber?.errors?.invalidIban">
                                {{'INTEGRATIONS-SETTINGS.INCORRECT-IBAN' | translate}}
                            </mat-error>
                        </mat-error>
                    </mat-form-field>
                </div>

                <div>
                    <a mat-button [disabled]="!form.valid" (click)="submit()">{{'INTEGRATIONS-SETTINGS.AUTOPAY-LOGIN-BUTTON' | translate}}</a>
                </div>
            </div>

            <div *ngIf="isLoggedAutopay">
                <a mat-button (click)="logout()">{{'INTEGRATIONS-SETTINGS.AUTOPAY-LOGOUT-BUTTON' | translate}}</a>
            </div>
        </form>
    </div>
</mat-card>
