import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { first, map, switchMap } from 'rxjs/operators';
import {WidgetInterface} from '../../../common/interfaces/widget.interface';
import { EnvironmentService } from '../../../services/environment.service';
import {ApplicationSettingsService} from '../../../services/application-settings.service';
import {PollActivationResponse} from '../../../common/interfaces/poll.interface';

@Injectable({
    providedIn: 'root',
})


export class WidgetApiService {
    private _environment: any;

    constructor(
        private _httpClient: HttpClient,
        private _environmentService: EnvironmentService,
        private _applicationSettingsService: ApplicationSettingsService
    ) {
        this._environment = this._environmentService.selectedEnvironment;
    }

    public createNewWidget(widgetUrl: string, type: string, settings?: WidgetInterface): Observable<any> {
        this._applicationSettingsService.getConfig()
            .pipe(
                first()
            )
            .subscribe(data => {
                let widgetType: string;

                switch (type) {
                    case 'form':
                        widgetType = 'caseManagement';
                        break;
                    case 'chat':
                        widgetType = 'chat';
                        break;
                    case 'poll':
                        widgetType = 'poll';
                        break;
                }

                this._applicationSettingsService.sidebarSetValue(widgetType, 'visible', true, data['results'][0]['Settings'].id);
            });

        let widgetEndpoint: string;
        let widgetObjectKey: string;
        switch (type) {
            case 'form':
            case 'chat':
                widgetEndpoint = 'chat_widget';
                widgetObjectKey = 'ChatWidget';
                break;
            case 'poll':
                widgetEndpoint = 'widget_poll';
                widgetObjectKey = 'WidgetPoll';
                break;
        }
        return this._httpClient
            .post(this._environment.apiUrl + widgetEndpoint, {
                [widgetObjectKey]: {
                    url: widgetUrl,
                    type: type,
                    settings: settings ? JSON.stringify(settings) : ''
                },
            })
            .pipe(first());
    }

    public getWidgetSettings(uid: string): Observable<any> {
        return this._httpClient
            .get(this._environment.apiUrl + `chat_widget&external_hash=${uid}&limit=1`)
            .pipe(
                first(),
                map((response: {results, total: number}) =>
                    response?.results.map((item) => item.ChatWidget)
                )
            );
    }

    public updateWidgetSettings(
        id: number,
        data: WidgetInterface
    ): Observable<any> {
        const dataToSend = JSON.stringify(data);

        return this._httpClient.put(
            this._environment.apiUrl + `chat_widget/${id}`,
            {
                ChatWidget: {
                    url: data.name,
                    settings: dataToSend,
                },
            }
        );
    }

    public getWidgets(conditions = null, limit = 1000): Observable<any> {
        return this._httpClient
            .get(this._environment.apiUrl + `chat_widget?${conditions}&limit=${limit}`)
            .pipe(
                first(),
                map((inputValues: { results: any }) => inputValues?.results)
            );
    }

    public getChatsLists(): Observable<any> {
        return this._httpClient
            .get(this._environment.apiUrl + 'chat_widget&type=chat&limit=1000')
            .pipe(
                first(),
                map((inputValues: { results: any }) => inputValues?.results)
            );
    }

    public getFormsLists(): Observable<any> {
        return this._httpClient
            .get(this._environment.apiUrl + 'chat_widget&type=form&limit=1000')
            .pipe(
                first(),
                map((inputValues: { results: any }) => inputValues?.results)
            );
    }

    public getPollsLists(): Observable<any> {
        return this._httpClient
            .get(this._environment.apiUrl + 'widget_poll&type=poll&limit=1000')
            .pipe(
                first(),
                map((inputValues: { results: any }) => inputValues?.results)
            );
    }

    public deleteWidget(id: number): Observable<any> {
        return this._httpClient
            .delete(this._environment.apiUrl + `chat_widget/${id}`)
            .pipe(
                first(),
                switchMap((res) => this.getWidgets('type=chat,form'))
            );
    }

    public togglePollActivation(id: number, value: string): Observable<PollActivationResponse> {
        const poll = {
            ChatWidget: {
                is_active: value
            }
        };

        return this._httpClient.put<PollActivationResponse>(this._environment.apiUrl + `widget_poll/${id}`, JSON.stringify(poll));
    }
}
