import {WidgetInterface} from '../../common/interfaces/widget.interface';
import {chain} from '../../helpers/chain.helper';
import {TranslateService} from '@ngx-translate/core';

export class PredefinedWidgetStylesClass {

    constructor(private _translate: TranslateService) {
    }

    private initStyles: WidgetInterface = {
        name: '',
        uid: '',
        theme: this._translate.instant('STYLES.LIGHT'),
        id: 1,
        header: {
            background: '#F6F8FA',
            formLabel: this._translate.instant('FORM-WIDGET.FORM'),
            chatLabel: this._translate.instant('WIDGET.CHAT-TITLE'),
            iconColor: '#0360AA',
            labelColor: '#000000',
            labelSize: 17,
            formSubtitle: this._translate.instant('WIDGET.SUBTITLE'),
            formLabelColor: '#000000',
            formLabelFontSize: 22,
            formSubtitleColor: '#000000',
            formSubtitleFontSize: 18,
            textAlign: 'center'
        },
        content: {
            textareaBorderColor: '#015faa',
            placeholderText: this._translate.instant('FORM-WIDGET.TEXTAREA-PLACEHOLDER'),
            backgroundColor: '#ffffff',
            supportBackground: '#ffffff',
            clientBackground: '#E9F0F5',
            contentBackground: '#F6F8FA',
            fontSize: 14,
            supportColor: '#000000',
            clientColor: '#000000',
            termsAndConditionsLink: '',
            privacyStatementLink: '',
            inputsTextColor: '#000000',
            inputsBorderColor: '#015faa',
            formLinksAndCloseIconColor: '#015faa',
            placeholderTextColor: '#C1C1C1',
            formBackground: '#ffffff',
            customUserTerms: []
        },
        actions: {
            defaultButtonBackground: 'linear-gradient(to right, #309a00, #3cc300)',
            buttonBackground: '#005FAA',
            fontSizeButton: 16,
            attachmentIconColor: '#C1C1C1',
            actionsBackground: '#b9b9b9',
            textAreaBackground: '#F6F8FA',
            buttonLabelColor: '#ffffff',
            showAttachmentIcon: true,
            showPhoneInput: true,
            showLastnameInput: true,
            sendBtnLabelColor: '#ffffff'
        },
        button: {
            color: '#ffffff',
            background: '#005FAA',
            positionOnPage: {
                right: 2,
                bottom: 2,
            },
            radius: 20,
            size: 60,
            fontSize: 30,
            iconSvg: 'chat_bubble',
            formIconSvg: 'description'
        },
        onInit: {
            requiredSetFirstName: true,
            requiredRegister: false,
            logo: '',
            text: '',
            buttonBackground: '#309a00',
            buttonColorText: '#ffffff',
            buttonFontSize: 17,
            fontText: 17,
            textColor: '#000000',
            autoOpenChat: false,
            autoOpenChatDelay: 0,
            showTooltipMessage: false,
            tooltipMessageDelay: 500,
            tooltipMessageText: '',
            tooltipBackground: '#005FAA',
            tooltipTextColor: '#ffffff',
            inputPlaceholder: '',
            buttonLabel: '',
            footerBackground: '#E2E8ED'
        },
        settings: undefined
    };

    private readonly lightTheme: WidgetInterface = {
        name: 'Light theme',
        theme: this._translate.instant('STYLES.LIGHT'),
        header: {
            background: '#F6F8FA',
            formLabel: this._translate.instant('FORM-WIDGET.FORM'),
            chatLabel: this._translate.instant('WIDGET.CHAT-TITLE'),
            iconColor: '#0360AA',
            labelColor: '#000000',
            labelSize: 17,
            formSubtitle: this._translate.instant('WIDGET.SUBTITLE'),
            formLabelColor: '#000000',
            formLabelFontSize: 22,
            formSubtitleColor: '#000000',
            formSubtitleFontSize: 18,
            textAlign: 'center'
        },
        content: {
            textareaBorderColor: '#015faa',
            placeholderText: this._translate.instant('FORM-WIDGET.TEXTAREA-PLACEHOLDER'),
            backgroundColor: '#ffffff',
            supportBackground: '#ffffff',
            clientBackground: '#E9F0F5',
            contentBackground: '#F6F8FA',
            fontSize: 14,
            supportColor: '#000000',
            clientColor: '#000000',
            termsAndConditionsLink: '',
            privacyStatementLink: '',
            inputsTextColor: '#000000',
            inputsBorderColor: '#015faa',
            formLinksAndCloseIconColor: '#015faa',
            placeholderTextColor: '#C1C1C1',
            formBackground: '#ffffff',
            customUserTerms: []
        },
        actions: {
            defaultButtonBackground: 'linear-gradient(to right, #309a00, #3cc300)',
            buttonBackground: '#005FAA',
            fontSizeButton: 16,
            attachmentIconColor: '#C1C1C1',
            actionsBackground: '#b9b9b9',
            textAreaBackground: '#F6F8FA',
            buttonLabelColor: '#ffffff',
            showAttachmentIcon: true,
            showPhoneInput: true,
            showLastnameInput: true,
            sendBtnLabelColor: '#ffffff'
        },
        button: {
            color: '#ffffff',
            background: '#005FAA',
            positionOnPage: {
                right: 2,
                bottom: 2,
            },
            radius: 20,
            size: 60,
            fontSize: 30,
            iconSvg: 'chat_bubble',
            formIconSvg: 'description'
        },
        onInit: {
            requiredSetFirstName: true,
            requiredRegister: false,
            logo: '',
            text: '',
            buttonBackground: '#309a00',
            buttonColorText: '#ffffff',
            buttonFontSize: 17,
            fontText: 17,
            textColor: '#000000',
            autoOpenChat: false,
            autoOpenChatDelay: 0,
            showTooltipMessage: false,
            tooltipMessageDelay: 500,
            tooltipMessageText: '',
            tooltipBackground: '#005FAA',
            tooltipTextColor: '#ffffff',
            inputPlaceholder: '',
            buttonLabel: '',
            footerBackground: '#E2E8ED'
        },
        settings: undefined
    };

    private readonly darkTheme: WidgetInterface = {
        name: 'Dark theme',
        theme: this._translate.instant('STYLES.DARK'),
        header: {
            background: '#4A4A4A',
            formLabel: this._translate.instant('FORM-WIDGET.FORM'),
            chatLabel: this._translate.instant('WIDGET.CHAT-TITLE'),
            iconColor: '#B0B7BF',
            labelColor: '#B0B7BF',
            labelSize: 17,
            formSubtitle: this._translate.instant('WIDGET.SUBTITLE'),
            formLabelColor: '#9b9b9b',
            formLabelFontSize: 22,
            formSubtitleColor: '#9b9b9b',
            formSubtitleFontSize: 18,
            textAlign: 'center'
        },
        content: {
            textareaBorderColor: '#4A4A4A',
            placeholderText: this._translate.instant('FORM-WIDGET.TEXTAREA-PLACEHOLDER'),
            backgroundColor: '#424141',
            supportBackground: '#424141',
            clientBackground: '#5A5A5A',
            contentBackground: '#424141',
            fontSize: 14,
            supportColor: '#FFFFFF',
            clientColor: '#FFFFFF',
            termsAndConditionsLink: '',
            privacyStatementLink: '',
            inputsTextColor: '#949494',
            inputsBorderColor: '#424141',
            formLinksAndCloseIconColor: '#d3d3d3',
            placeholderTextColor: '#C1C1C1',
            formBackground: '#4A4A4A',
            customUserTerms: []
        },
        actions: {
            defaultButtonBackground: 'linear-gradient(to right, #309a00, #3cc300)',
            buttonBackground: '#B0B7BF',
            fontSizeButton: 16,
            attachmentIconColor: '#C1C1C1',
            actionsBackground: '#151819',
            textAreaBackground: '#4A4A4A',
            buttonLabelColor: '#ffffff',
            showAttachmentIcon: true,
            showPhoneInput: true,
            showLastnameInput: true,
            sendBtnLabelColor: '#ffffff'
        },
        button: {
            color: '#ffffff',
            background: '#2B2B2B',
            positionOnPage: {
                right: 2,
                bottom: 2,
            },
            radius: 20,
            size: 60,
            fontSize: 30,
            iconSvg: 'chat_bubble',
            formIconSvg: 'description'
        },
        onInit: {
            requiredSetFirstName: true,
            requiredRegister: false,
            logo: '',
            text: '',
            buttonBackground: '#309a00',
            buttonColorText: '#ffffff',
            buttonFontSize: 17,
            fontText: 17,
            textColor: '#ffffff',
            autoOpenChat: false,
            autoOpenChatDelay: 0,
            showTooltipMessage: false,
            tooltipMessageDelay: 500,
            tooltipMessageText: '',
            tooltipBackground: '#151819',
            tooltipTextColor: '#ffffff',
            inputPlaceholder: '',
            buttonLabel: '',
            footerBackground: '#2B2B2B'
        },
        settings: undefined
    };

    private readonly contrastTheme: WidgetInterface = {
        name: 'Contrast theme',
        theme: this._translate.instant('STYLES.CONTRAST'),
        header: {
            background: '#FFFFFF',
            formLabel: this._translate.instant('FORM-WIDGET.FORM'),
            chatLabel: this._translate.instant('WIDGET.CHAT-TITLE'),
            iconColor: '#0360AA',
            labelColor: '#000000',
            labelSize: 17,
            formSubtitle: this._translate.instant('WIDGET.SUBTITLE'),
            formLabelColor: '#000000',
            formLabelFontSize: 22,
            formSubtitleColor: '#000000',
            formSubtitleFontSize: 18,
            textAlign: 'center'
        },
        content: {
            textareaBorderColor: '#000000',
            placeholderText: this._translate.instant('FORM-WIDGET.TEXTAREA-PLACEHOLDER'),
            backgroundColor: '#ffffff',
            supportBackground: '#ffffff',
            clientBackground: '#ffffff',
            contentBackground: '#ffffff',
            fontSize: 14,
            supportColor: '#000000',
            clientColor: '#000000',
            termsAndConditionsLink: '',
            privacyStatementLink: '',
            inputsTextColor: '#000000',
            inputsBorderColor: '#000000',
            formLinksAndCloseIconColor: '#015faa',
            placeholderTextColor: '#000000',
            formBackground: '#ffffff',
            customUserTerms: []
        },
        actions: {
            defaultButtonBackground: 'linear-gradient(to right, #309a00, #3cc300)',
            buttonBackground: '#005FAA',
            fontSizeButton: 16,
            attachmentIconColor: '#C1C1C1',
            actionsBackground: '#b9b9b9',
            textAreaBackground: '#ffffff',
            buttonLabelColor: '#ffffff',
            showAttachmentIcon: true,
            showPhoneInput: true,
            showLastnameInput: true,
            sendBtnLabelColor: '#ffffff'
        },
        button: {
            color: '#ffffff',
            background: '#151819',
            positionOnPage: {
                right: 2,
                bottom: 2,
            },
            radius: 20,
            size: 60,
            fontSize: 30,
            iconSvg: 'chat_bubble',
            formIconSvg: 'description'
        },
        onInit: {
            requiredSetFirstName: true,
            requiredRegister: false,
            logo: '',
            text: '',
            buttonBackground: '#309a00',
            buttonColorText: '#ffffff',
            buttonFontSize: 17,
            fontText: 17,
            textColor: '#000000',
            autoOpenChat: false,
            autoOpenChatDelay: 0,
            showTooltipMessage: false,
            tooltipMessageDelay: 500,
            tooltipMessageText: '',
            tooltipBackground: '#000000',
            tooltipTextColor: '#ffffff',
            inputPlaceholder: '',
            buttonLabel: '',
            footerBackground: '#E0E0E0'
        },
        settings: undefined
    };

    public getStyles(): WidgetInterface[] {
        return [chain([]).cloneDeep(this.darkTheme),
            chain([]).cloneDeep(this.lightTheme),
            chain([]).cloneDeep(this.contrastTheme)];
    }

    public getInitStyle(): WidgetInterface {
        return chain([]).cloneDeep(this.initStyles);
    }

}
