<div class="user-add__header">
    <h1 class="user-add__title" mat-dialog-title>{{ userId ? ('USER-ADD.EDIT-DIALOG' | translate) : ('USER-ADD.ADD-DIALOG' | translate) }}</h1>
    <div *ngIf="isAdmin && userId" (click)="onReportOpen()" class="user-add__icon">
        <mat-icon matTooltip="Raport RODO" matTooltipPosition="after" class="mat-icon cursor-pointer user-add__rodo">folder_open</mat-icon>
    </div>
</div>

<form #userForm="ngForm" (ngSubmit)="submit(userForm)">
    <div mat-dialog-content fxLayout="column" #userDialogContent>
        <mat-form-field appearance="outline" [ngClass.xs]="{'server-error': emailCtrl.errors?.serverError}">
            <mat-label>{{'USER-ADD.USER-EMAIL' | translate }}</mat-label>
            <input #emailCtrl="ngModel"
                   matInput
                   [(ngModel)]="user.email"
                   (ngModelChange)="trimInput(emailCtrl)"
                   name="email"
                   type="email"
                   [disabled]="!!userId"
                   matTooltip="{{!!userId ? ('USER-ADD.PERMISSION-DENIED' | translate) : null}}"
                   required
                   customEmailValidator>
            <mat-error *ngIf="emailCtrl.invalid && (emailCtrl.touched || emailCtrl.dirty)">
                <div *ngIf="emailCtrl.errors.required || emailCtrl.errors.whitespace">{{ 'USER-ADD.FIELD-REQUIRED' | translate }}</div>
                <div *ngIf="emailCtrl.errors.email && !emailCtrl.errors.required">{{ 'USER-ADD.INCORRECT-EMAIL' | translate }}</div>
                <div *ngIf="emailCtrl.errors.serverError">{{emailCtrl.errors.serverError}}</div>
            </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" [ngClass.xs]="{'server-error': firstNameCtrl.errors?.serverError}">
            <mat-label>{{'USER-ADD.USER-FIRSTNAME' | translate }}</mat-label>
            <input #firstNameCtrl="ngModel"
                   matInput
                   [(ngModel)]="user.firstname"
                   (ngModelChange)="trimInput(firstNameCtrl)"
                   name="firstname"
                   [maxLength]="64"
                   required>
            <mat-hint align="end">{{user.firstname.length}}/64</mat-hint>
            <mat-error *ngIf="firstNameCtrl.invalid && (firstNameCtrl.touched || firstNameCtrl.dirty)">
                <div *ngIf="firstNameCtrl.errors.required">{{ 'USER-ADD.FIELD-REQUIRED' | translate }}</div>
                <div *ngIf="firstNameCtrl.errors.serverError">{{firstNameCtrl.errors.serverError}}</div>
            </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" [ngClass.xs]="{'server-error': lastNameCtrl.errors?.serverError}">
            <mat-label>{{'USER-ADD.USER-LASTNAME' | translate }}</mat-label>
            <input #lastNameCtrl="ngModel"
                   matInput
                   [(ngModel)]="user.lastname"
                   (ngModelChange)="trimInput(lastNameCtrl)"
                   name="lastname"
                   [maxLength]="64"
                   required>
            <mat-hint align="end">{{user.lastname.length}}/64</mat-hint>
            <mat-error *ngIf="lastNameCtrl.invalid && (lastNameCtrl.touched || lastNameCtrl.dirty)">
                <div *ngIf="lastNameCtrl.errors.required">{{ 'USER-ADD.FIELD-REQUIRED' | translate }}</div>
                <div *ngIf="lastNameCtrl.errors.serverError">{{lastNameCtrl.errors.serverError}}</div>
            </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" [ngClass.xs]="{'server-error': phoneCtrl.errors?.serverError}">
            <mat-label>{{'USER-ADD.USER-PHONE' | translate }}</mat-label>
            <input #phoneCtrl="ngModel" matInput [(ngModel)]="user.phone" (ngModelChange)="trimInput(phoneCtrl)" name="phone">
            <mat-error *ngIf="phoneCtrl.invalid && phoneCtrl.errors?.serverError">{{phoneCtrl.errors.serverError}}</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" [ngClass.xs]="{'server-error': descriptionCtrl.errors?.serverError}">
            <mat-label>{{'USER-ADD.USER-DESCRIPTION' | translate }}</mat-label>
            <input #descriptionCtrl="ngModel" matInput [(ngModel)]="user.description" (ngModelChange)="trimInput(descriptionCtrl)" name="description">
            <mat-error *ngIf="descriptionCtrl.invalid && descriptionCtrl.errors?.serverError">{{descriptionCtrl.errors.serverError}}</mat-error>
        </mat-form-field>

        <mat-form-field [ngClass.xs]="{'server-error': roleCtrl.errors?.serverError}">
            <mat-label>{{ 'USER-ADD.ROLE' | translate }}</mat-label>
            <mat-select
                #roleCtrl="ngModel"
                [(ngModel)]="user.user_role_id"
                name="user_role_id"
                matTooltip="{{!permissions.role ? ('USER-ADD.PERMISSION-DENIED' | translate) : null}}"
                [disabled]="!permissions.role"
                (selectionChange)="onRoleChange($event.value)"
                required>
                <mat-option [value]="null"></mat-option>
                <mat-option *ngFor="let role of roles" [value]="role.id">
                    {{ role.name }}
                </mat-option>
            </mat-select>
            <mat-error *ngIf="roleCtrl.invalid && (roleCtrl.touched || roleCtrl.dirty)">
                <div *ngIf="roleCtrl.errors.required">{{ 'USER-ADD.FIELD-REQUIRED' | translate }}</div>
            </mat-error>
        </mat-form-field>

        <mat-form-field [ngClass.xs]="{'server-error': mentorCtrl.errors?.serverError}">
            <mat-label>{{ 'USER-ADD.REPRESENTATIVE' | translate }}</mat-label>
            <mat-select
                #mentorCtrl="ngModel"
                [(ngModel)]="user.mentor_id"
                name="mentor_id"
                placeholder="{{'USER-ADD.USER-REPRESENTATIVE' | translate}}"
                matTooltip="{{!permissions.mentor ? ('USER-ADD.PERMISSION-DENIED' | translate) : null}}"
                [disabled]="!permissions.mentor"
                (selectionChange)="onMentorChange()">
                <mat-option></mat-option>
                <mat-option *ngFor="let mentor of mentors" [value]="mentor.id">
                    {{mentor.lastname}} {{mentor.firstname}}
                </mat-option>
            </mat-select>
            <mat-error *ngIf="mentorCtrl.invalid && mentorCtrl.errors?.serverError">{{mentorCtrl.errors.serverError}}</mat-error>
        </mat-form-field>

        <mat-form-field [ngClass.xs]="{'server-error': groupsCtrl.errors?.serverError}">
            <mat-label>{{ 'USER-ADD.GROUP' | translate }}</mat-label>
            <mat-select #groupsCtrl="ngModel"
                        [(ngModel)]="groupIds"
                        [disabled]="!groups"
                        name="groupIds"
                        placeholder="{{'USER-ADD.USER-GROUP' | translate}}"
                        multiple>
                <mat-option *ngFor="let group of groups" [value]="group.id">
                    {{group.name}}
                </mat-option>
            </mat-select>
            <mat-error *ngIf="groupsCtrl.invalid && groupsCtrl.errors?.serverError">{{groupsCtrl.errors.serverError}}</mat-error>
        </mat-form-field>

        <mat-slide-toggle
            [class.invisible]="!userId"
            #userBannedCtrl="ngModel"
            color="accent"
            class="permission-slider"
            [checked]="!!user.banned"
            name="banned"
            ngModel
            matTooltip="{{!permissions.ban ? ('USER-ADD.PERMISSION-DENIED' | translate) : null}}"
            [disabled]="!permissions.ban"
            (change)="onPermissionSliderChange($event)">
            {{'USER-ADD.USER-BLOCKED' | translate }}
        </mat-slide-toggle>
        <mat-error *ngIf="userBannedCtrl.invalid && userBannedCtrl.errors?.serverError">{{userBannedCtrl.errors.serverError}}</mat-error>
    </div>

    <div mat-dialog-actions fxLayout="row" fxLayoutAlign="space-between" >
        <button type="button" mat-button (click)="onNoClick()" class="action-button">{{'SHARED.CANCEL' | translate }}</button>
        <button mat-button color="primary" [disabled]="userForm.invalid" class="action-button">{{'SHARED.SAVE' | translate }}</button>
    </div>
</form>
