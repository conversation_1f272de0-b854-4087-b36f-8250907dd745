<div class="template-list" fxLayout="column">
    <mat-toolbar>
        <button mat-icon-button matTooltip=" {{'TEMPLATE-LIST.LIST-GROUP' | translate}}" class="previous" routerLink="/settings/templates">
            <mat-icon>arrow_back</mat-icon>
        </button>

        <button mat-stroked-button class="button-rounded primary green" color="primary" (click)="clickAddNewTemplate()">
            <mat-icon>add</mat-icon>
            {{'TEMPLATE-LIST.ADD-TEMPLATE' | translate}}
        </button>
    </mat-toolbar>

    <div class="template-list">
        <mat-card class="panel">
            <mat-card-title>
                {{'TEMPLATE-LIST.GROUP' | translate}} <span class="group-name" (click)="clickEditTemplateGroup()">{{templateGroupData?.name}}</span>
                <mat-card-subtitle>{{'TEMPLATE-LIST.SELECT-TEMPLATE' | translate}}
                    <span fxHide.lt-lg>{{'TEMPLATE-LIST.DRAG' | translate}}</span>
                </mat-card-subtitle>
            </mat-card-title>

            <app-draggable-items-list
                [itemsLoaded]="itemsSubscription && itemsSubscription.closed"
                [items]="templatesData"
                displayedFieldName="name"
                (itemDropped)="onItemDropped($event)"
                (itemClicked)="onItemClicked($event)"
            >
            </app-draggable-items-list>
        </mat-card>
    </div>
</div>
