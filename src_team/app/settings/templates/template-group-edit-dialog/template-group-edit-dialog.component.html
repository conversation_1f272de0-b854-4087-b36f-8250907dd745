<h1 mat-dialog-title>{{'TEMPLATE-GROUP-EDIT-DIALOG.EDITING' | translate}}</h1>

<form [formGroup]="editTemplateGroupForm" (ngSubmit)="onSubmit()">
    <div mat-dialog-content fxLayout="column">
        <div fxLayout="row" fxLayout.xs="column" fxLayoutGap.gt-xs="20px">
            <mat-form-field appearance="outline" focused="true" fxFlex="calc(50% - 20px)">
                <mat-label>{{'TEMPLATE-GROUP-EDIT-DIALOG.TEMPLATE-GROUP-NAME' | translate}}</mat-label>
                <input matInput formControlName="name" required>
                <mat-error
                    *ngIf="editTemplateGroupForm.get('name').invalid && (editTemplateGroupForm.get('name').dirty || editTemplateGroupForm.get('name').touched)">
                    <div *ngIf="editTemplateGroupForm.get('name').errors.required">
                        {{'TEMPLATE-GROUP-EDIT-DIALOG.FIELD' | translate}}<strong>{{'TEMPLATE-GROUP-EDIT-DIALOG.REQUIRED' | translate}}</strong>
                    </div>
                </mat-error>
            </mat-form-field>
        </div>
    </div>

    <div mat-dialog-actions  fxLayout="row" fxLayout.xs="column" fxLayoutAlign="space-between">
        <button type="button" mat-button color="warn" (click)="onClickDelete()">{{'ISSUE-SUBJECT-ADD.DELETE' | translate}}</button>
        <div fxLayout.xs="column">
            <button type="button" mat-button matDialogClose>{{'ISSUE-SUBJECT-ADD.CANCEL' | translate}}</button>
            <button type="submit" mat-button color="primary" [ngStyle.xs]="{'margin-left': 0}" [disabled]="!editTemplateGroupForm.valid">{{'ISSUE-SUBJECT-ADD.SAVE' | translate}}</button>
        </div>
    </div>
</form>
