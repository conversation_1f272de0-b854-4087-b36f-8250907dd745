import {Component, EventEmitter, inject, Inject, Output} from '@angular/core';
import {FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialogActions, MatDialogClose, MatDialogContent, MatDialogRef, MatDialogTitle} from '@angular/material/dialog';
import {TemplateGroupService} from '../../../services/template-group.service';
import {TdDialogService} from '@covalent/core/dialogs';
import {Router} from '@angular/router';
import {RespondoValidators} from '../../../shared/validators/RespondoValidators';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {CdkScrollable} from '@angular/cdk/scrolling';
import {DefaultFlexDirective, DefaultLayoutAlignDirective, DefaultLayoutDirective, DefaultLayoutGapDirective} from 'ngx-flexible-layout/flex';
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orm<PERSON><PERSON>, <PERSON><PERSON>abel} from '@angular/material/select';
import {MatInput} from '@angular/material/input';
import {NgIf} from '@angular/common';
import {MatButton} from '@angular/material/button';
import {DefaultStyleDirective} from 'ngx-flexible-layout/extended';
import {AlertService} from '../../../services/alert.service';
import {AlertType} from '../../../common/enums/alert-type.enum';
import {AlertDuration} from '../../../common/enums/alert-duration.enum';

@Component({
    selector: 'app-template-group-edit-dialog',
    templateUrl: './template-group-edit-dialog.component.html',
    styleUrls: ['./template-group-edit-dialog.component.scss'],
    imports: [MatDialogTitle, FormsModule, ReactiveFormsModule, CdkScrollable, MatDialogContent, DefaultLayoutDirective, DefaultLayoutGapDirective, MatFormField, DefaultFlexDirective, MatLabel, MatInput, NgIf, MatError, MatDialogActions, DefaultLayoutAlignDirective, MatButton, MatDialogClose, DefaultStyleDirective, TranslatePipe]
})
export class TemplateGroupEditDialogComponent {

    @Output()
    refreshTemplateGroupData: EventEmitter<any> = new EventEmitter();

    editTemplateGroupForm: UntypedFormGroup;

    private alertService: AlertService = inject(AlertService);

    constructor(
        private fb: UntypedFormBuilder,
        @Inject(MAT_DIALOG_DATA) public data,
        private templateGroupService: TemplateGroupService,
        public dialogRef: MatDialogRef<TemplateGroupEditDialogComponent>,
        private dialogService: TdDialogService,
        private router: Router,
        public translate: TranslateService
    ) {
        this.editTemplateGroupForm = this.fb.group({
            name: [this.data.name, RespondoValidators.minChars(3)]
        });
    }

    onSubmit(): void {
        this.templateGroupService.updateTemplateGroup(this.data.id, this.editTemplateGroupForm.value).subscribe(() => {
            this.refreshTemplateGroupData.emit(null);
            this.alertService.showAlert(this.translate.instant('TEMPLATE-GROUP-EDIT-DIALOG.GROUP-SAVED'), AlertType.SUCCESS, AlertDuration.MEDIUM);
            this.dialogRef.close();
        });
    }

    onClickDelete(): void {
        this.dialogService.openConfirm({
            message: this.translate.instant('TEMPLATE-GROUP-EDIT-DIALOG.GROUP-DELETING'),
            disableClose: false,
            title: this.translate.instant('TEMPLATE-GROUP-EDIT-DIALOG.GROUP-DELETING-1'),
            cancelButton: this.translate.instant('TEMPLATE-GROUP-EDIT-DIALOG.NO'),
            acceptButton: this.translate.instant('TEMPLATE-GROUP-EDIT-DIALOG.YES'),
            panelClass: 'confirm-dialog'
        }).afterClosed().subscribe((accept: boolean) => {
            if (!accept) {
                return;
            }

            this.templateGroupService.deleteTemplateGroup(this.data.id).subscribe(res => {
                if (res.status !== 'OK') {
                    return;
                }

                this.alertService.showAlert(this.translate.instant('TEMPLATE-GROUP-EDIT-DIALOG.GROUP-DELETED'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.dialogRef.close();
                this.router.navigate(['settings/templates']);
            });
        });
    }
}
