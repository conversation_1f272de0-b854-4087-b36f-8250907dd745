import { LucideAngularModule } from 'lucide-angular';
import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, inject, Input, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {EmailService} from '../../services/email.service';
import {ConfirmDialogComponent} from '../../shared/confirm-dialog/confirm-dialog.component';
import {debounceTime, filter, map, startWith, switchMap, take} from 'rxjs/operators';
import {HttpErrorResponse} from '@angular/common/http';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {MatDialog} from '@angular/material/dialog';
import {ActivatedRouteSnapshot, Router} from '@angular/router';
import {SimpleMenuItem} from '../../common/interfaces/simple-menu.interface';
import {UserService} from '../../services/user.service';
import {selectCurrentRoute, selectRouteData} from '../../store/router/router.selectors';
import {Store} from '@ngrx/store';
import {DialogService} from '../../services/dialog-details.service';
import {EMPTY, forkJoin, fromEvent, Observable, of, Subject, Subscription} from 'rxjs';
import {MailAttachmentsService} from '../../services/mail-attachments.service';
import {ButtonVariant} from '../../common/enums/button-variant.enum';
import {MatAutocomplete, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatOption} from '@angular/material/autocomplete';
import {COMMA, ENTER} from '@angular/cdk/keycodes';
import {MatChip, MatChipGrid, MatChipInput, MatChipInputEvent, MatChipRemove} from '@angular/material/chips';
import {ClientsService} from '../../services/clients.service';
import {MailsDetailsHelper} from './helpers/mails-details.helper';
import { AsyncPipe, DatePipe, NgClass, NgFor, NgIf} from '@angular/common';
import {DefaultClassDirective} from 'ngx-flexible-layout/extended';
import {AvatarModule} from 'ngx-avatars';
import {MatTooltip} from '@angular/material/tooltip';
import {ClipboardModule} from 'ngx-clipboard';
import {ShadowWrapperComponent} from '../../shared/shadow-wrapper/shadow-wrapper.component';
import {MailsBodyComponent} from '../mails-body/mails-body.component';
import {MailsAttachedFilesListComponent} from '../mails-attached-files-list/mails-attached-files-list.component';
import {DownloadButtonComponent} from '../../shared/download-button/download-button.component';
import {ButtonComponent} from '../../elements/button/button.component';
import {MiddleclickDirective} from '../../directives/middleclick.directive';
import {MailsEditorComponent} from '../mails-editor/mails-editor.component';
import {TagsComponent} from '../../shared/tags/tags.component';
import {MoreOptionsComponent} from '../../shared/more-options/more-options.component';
import {MatFormField, MatLabel} from '@angular/material/select';
import {MatIcon} from '@angular/material/icon';
import {MatInput} from '@angular/material/input';
import {FormsModule} from '@angular/forms';
import {TimestampToDatePipe} from '../../shared/pipes/timestamp-to-date.pipe';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';
import { ObjectNameType } from '../../interfaces/object-name-type';
import { MailThreadComponent } from '../mail-thread/mail-thread.component';

@Component({
    selector: 'app-mails-details',
    templateUrl: './mails-details.component.html',
    styleUrls: ['./mails-details.component.scss'],
    imports: [NgIf, NgClass, DefaultClassDirective, AvatarModule, MatTooltip, ClipboardModule, ShadowWrapperComponent, MailsBodyComponent, MailsAttachedFilesListComponent, DownloadButtonComponent, ButtonComponent, MiddleclickDirective, MailsEditorComponent, TagsComponent, MoreOptionsComponent, MatFormField, MatLabel, NgFor, MatChip, MatIcon, MatChipRemove, MatAutocompleteTrigger, MatChipInput, MatAutocomplete, MatOption, MatInput, FormsModule, AsyncPipe, DatePipe, TranslatePipe, TimestampToDatePipe, LucideAngularModule, MatChipGrid, MailThreadComponent]
})

export class MailsDetailsComponent implements OnInit, AfterViewInit, OnDestroy {

    @Input()
    data;

    @Input()
    entryList: string;

    @Input()
    tagsEditMode = true;

    @ViewChild('body', { static: false }) editorContainer!: ElementRef;
    @ViewChild('sendersInput') sendersInput: ElementRef<HTMLInputElement>;
    @ViewChild(MatAutocompleteTrigger) autocompleteTrigger: MatAutocompleteTrigger;
    @ViewChild('chipList', { read: ElementRef }) chipList: ElementRef;
    @ViewChild(MailThreadComponent) mailThread: MailThreadComponent;

    isEditorOpen: boolean = false;
    isOpenAccordion: boolean = false;
    defaultAvatarBgColor = '#f1c40f';
    forwardEditorVisible: boolean;
    tagObjectName: ObjectNameType = 'mail_sent_mysql_message';
    moreOptionsMenu: SimpleMenuItem[] = [];
    mailData;
    mailReceiver: string;
    mailId: number = null;
    commonFileId: string = null;
    commonFileIdArr: string[];
    commonFileName: string;
    commonFileType: string;
    commonFileAttachments: { commonFileId: string, commonFileName: string, commonFileType: string }[] = [];
    moreMailsDots: string = '';
    draftView: boolean;
    readonly ButtonVariant = ButtonVariant;
    missingAttachmentCountPhrase: string = '';
    isMailReceived: boolean = false;
    separatorKeysCodes: number[] = [ENTER, COMMA];
    hasMailsInputFocus = false;
    allClients: string[] = [];
    filteredClients: Observable<string[]>;
    mailAddressArray: string[] = [];
    visibleSenders: string[] = [];
    maxVisibleSenders = 2;

    private context: string;
    private readonly DEBOUNCE_TIME = 2000;
    private subscriptions: Subscription = new Subscription();
    private topicChangeSubject = new Subject<any>();
    private alertService: AlertService = inject(AlertService);


    constructor(
        private emailService: EmailService,
        private translate: TranslateService,
        private dialog: MatDialog,
        private router: Router,
        private userService: UserService,
        private store: Store,
        private dialogService: DialogService,
        private mailAttachmentService: MailAttachmentsService,
        private clientsService: ClientsService,
        private cdr: ChangeDetectorRef,
        private mailsDetailsHelper: MailsDetailsHelper
    ) {
    }

    ngOnInit(): void {
        this.context = this.data.context;
        this.mailData = this.data = this.data.data;
        this.checkDraftView();
        this.isMailReceived = this.mailData.hasOwnProperty('receiver_mail_address');

        this.moreMailsDots = this.mailData?.mail_address?.split(',')?.filter((item: string) => item.trim() !== '').length > 1 ?
            '...' : '';

        if (+this.mailData.missing_attachments_count) {
            this.missingAttachmentCountPhrase = this.mailsDetailsHelper.getMissingAttachmentCountPhrase(+this.mailData.missing_attachments_count);
        }

        this.subscriptions.add(this.store.select(selectRouteData).subscribe(value => {
            const name = value?.name;

            const tagObjectMap: Record<string, ObjectNameType> = {
                sent: 'mail_sent_mysql_message',
                draft: 'mail_sent_mysql_message',
                important: 'mail_received_mysql_message',
                other: 'mail_received_mysql_message',
            };

            this.tagObjectName = tagObjectMap[name] || (this.isMailReceived ? 'mail_received_mysql_message' : 'mail_sent_mysql_message');
        }));

        this.updateMenuItems();
        this.getMailReceiver(this.mailData.userId);
        this.initializeMailAddressArray();
        this.updateVisibleSenders();

        this.mailId = +this.mailData.id;

        if (this.mailId) {
            this.getCommonFileIdAndAttachmentData(this.mailId);
        }

        if (this.draftView) {
            this.getClientsData();
            this.filteredClients = of([]);
        }

        this.subscriptions.add(this.topicChangeSubject.pipe(
            debounceTime(this.DEBOUNCE_TIME)
        ).subscribe(() => {
            this.onDraftChange();
        }));

        if (this.mailData.is_forward) {
            this.openForwardEditor();
        }
    }

    ngAfterViewInit(): void {
        if (this.draftView && this.sendersInput) {
            this.filteredClients = fromEvent<InputEvent>(this.sendersInput.nativeElement, 'input')
                .pipe(
                    map((event: InputEvent) => (event.target as HTMLInputElement).value),
                    startWith(''),
                    map(value => this.filterClients(value))
                );
        }
    }

    ngOnDestroy() {
        this.subscriptions?.unsubscribe();
    }

    updateMenuItems(): void {
        this.moreOptionsMenu = [
            {
                label: +this.mailData.priority
                    ? this.translate.instant('MAILS-VIEW.DELETE-PRIORITY')
                    : this.translate.instant('MAILS-VIEW.SET-PRIORITY'),
                action: () => this.togglePriority()
            },
            { label: this.translate.instant('MAILS-VIEW.TRASH'), action: () => this.clickRemove() }
        ];
    }

    clickAddPriority() {
        this.subscriptions.add(this.emailService.setPriority(this.data.id).subscribe(
            () => {},
            (httpErrorResponse: HttpErrorResponse) => {
                console.error(httpErrorResponse.error.errorMessages);
            }
        ));

        this.mailData.priority = 1;

        return this.mailData.priority;
    }

    clickDelPriority() {
        this.subscriptions.add(this.emailService.unsetPriority(this.data.id).subscribe(
            () => {},
            (httpErrorResponse: HttpErrorResponse) => {
                console.error(httpErrorResponse.error.errorMessages);
            }
        ));

        this.mailData.priority = 0;

        return this.mailData.priority;
    }

    toggleAccordion() {
        this.isOpenAccordion = !this.isOpenAccordion;
    }

    openEditor() {
        this.isEditorOpen = true;
        this.forwardEditorVisible = false;
    }

    openForwardEditor() {
        this.forwardEditorVisible = true;
        this.isEditorOpen = false;
    }

    clickRemove() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '450px',
                data: {
                    header: this.translate.instant('MAILS-VIEW.REMOVE-CONFIRM')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed();

        confirm$.pipe(
            switchMap((confirm) => {
                if (confirm === true) {
                    return this.context === 'drafts'
                        ? this.emailService.removeDraft(this.data.id)
                        : this.emailService.remove(this.data.id);
                }

                return EMPTY;
            })
        ).subscribe(
            () => {
                this.alertService.showAlert(
                    this.translate.instant('MAILS-VIEW.REMOVE-INFO'),
                    AlertType.SUCCESS,
                    AlertDuration.MEDIUM
                );
                this.dialogService.closeDialog();
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.alertService.showAlert(this.translate.instant('MAILS-VIEW.REMOVE-ERROR'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                console.error(httpErrorResponse.error.errorMessages);
            }
        );
    }

    clickBack() {
        this.router.navigateByUrl('mail/' + (this.entryList ? this.entryList : 'important'));
    }

    openFull(newTab: boolean = false) {
        this.store.select(selectCurrentRoute).pipe(
            take(1),
            filter((r): r is ActivatedRouteSnapshot =>
                r != null && Array.isArray((r as any).url)
            ),
            map(r => r as ActivatedRouteSnapshot)
        ).subscribe(route => {
            const segments = route.url;
            const index = segments.length > 1 ? 1 : segments.length - 1;
            const targetPath = segments[index]?.path ?? '';

            const urlTree = this.router.createUrlTree(
                ['/mail', this.data.id],
                {
                    queryParams: {
                        type: this.data.listName,
                        targetPath,
                        tagObjectName: this.tagObjectName
                    }
                }
            );
            const url = this.router.serializeUrl(urlTree);

            if (newTab) {
                window.open(url, '_blank');
            } else {
                this.router.navigateByUrl(url);
            }
        });
    }

    togglePriority() {
        +this.mailData.priority ? this.clickDelPriority() : this.clickAddPriority();

        this.updateMenuItems();
    }

    isMultipleValue(el: string[]) {
        return el?.length > 1;
    }

    private getMailReceiver(id: number): void {
        this.subscriptions.add(this.userService.getUser(id).subscribe(value => {
            this.mailReceiver = value.email;
        }));
    }

    private checkDraftView() {
        const draftStatus = '4';
        this.draftView = this.mailData?.status === draftStatus;
    }

    private initializeMailAddressArray() {
        if (this.mailData && this.mailData.mail_address) {
            this.mailAddressArray = this.mailData.mail_address.split(',').map(email => email.trim()).filter(email => email);
        } else {
            this.mailAddressArray = [];
        }
    }

    addEmailAddress(event: MatChipInputEvent): void {
        const input = event.input;
        const value = event.value;

        if ((value || '').trim() && this.isValidEmail(value.trim())) {
            this.mailAddressArray.push(value.trim());
            this.updateMailAddress();
            this.onDraftChange();
        }

        if (input) {
            input.value = '';
        }
    }

    handleInputBlur(event: FocusEvent) {
        const input = event.target as HTMLInputElement;
        const value = input.value;

        if ((value || '').trim() && this.isValidEmail(value.trim())) {
            this.mailAddressArray.push(value.trim());
            this.updateMailAddress();
            this.onDraftChange();
        }

        if (input) {
            input.value = '';
        }
    }

    removeEmailAddress(sender: string, event: MouseEvent): void {
        event.preventDefault();
        event.stopPropagation();

        const index = this.mailAddressArray.indexOf(sender);
        if (index >= 0) {
            this.mailAddressArray.splice(index, 1);
            this.updateMailAddress();
            this.onDraftChange();
        }
    }

    private updateMailAddress() {
        this.mailData.mail_address = this.mailAddressArray.join(',');
        this.updateVisibleSenders();
    }

    private updateVisibleSenders() {
        this.visibleSenders = this.mailAddressArray.slice(0, this.maxVisibleSenders);
    }

    private isValidEmail(email: string): boolean {
        const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailPattern.test(email);
    }

    handleMailsInputBlur(event: FocusEvent) {
        const newFocused = event.relatedTarget as HTMLElement;

        if (newFocused && this.chipList && newFocused.closest('mat-chip-list') === this.chipList.nativeElement) {
            return;
        }

        this.hasMailsInputFocus = false;
        this.updateVisibleSenders();
    }

    onDraftChange() {
        if (this.mailData && this.mailData.id) {
            const draftData = {
                MailSentMysqlMessage: {
                    mail_id: this.mailData.id,
                    mail_server_id: this.mailData.mail_server_id,
                    mail_address: this.mailData.mail_address,
                    mail_subject: this.mailData.mail_subject,
                    mail_body: this.mailData.mail_body,
                    status: '4'
                }
            };

            this.subscriptions.add(this.emailService.updateDraftMail(draftData, this.mailData.id).subscribe(
                () => {
                    this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-UPDATED'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                },
                () => {
                    this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-ERROR'), AlertType.ERROR,
                        AlertDuration.MEDIUM);
                }
            ));
        }
    }

    onDraftChanged(event: any) {
        if (event && event.success) {
            this.mailData = event.data;

            if (event.mail_sent_mysql_messages_ids) {
                let ids = event.mail_sent_mysql_messages_ids;
                if (typeof ids === 'string') {
                    try {
                        ids = JSON.parse(ids);
                    } catch (e) {
                        ids = [];
                    }
                }

                ids = [...new Set(ids.map(id => id.toString()))];
                this.mailData.mail_sent_mysql_messages_ids = ids;

                if (this.mailThread) {
                    this.mailThread.refreshMessages();
                }

                this.cdr.detectChanges();
            }
        }
    }

    private getCommonFileIdAndAttachmentData(id: number) {
        this.mailAttachmentService.getReceivedAttachments(id)
            .pipe(
                switchMap(ids => {
                    if (!this.isMultipleValue(ids)) {
                        this.commonFileId = ids[0];
                        return this.processAttachmentData(this.commonFileId)
                            .pipe(
                                map(data => [data])
                            );
                    } else {
                        this.commonFileIdArr = ids;
                        return forkJoin(this.commonFileIdArr.map(fileId => this.processAttachmentData(fileId)));
                    }
                }),
                take(1)
            )
            .subscribe(attachments => {
                this.commonFileAttachments = attachments;

                if (attachments.length > 0) {
                    this.commonFileName = attachments[0]?.commonFileName;
                    this.commonFileId = attachments[0]?.commonFileId;
                    this.commonFileType = attachments[0]?.commonFileType;
                }
                this.cdr.detectChanges();
            });
    }

    private processAttachmentData(fileId: string) {
        return this.mailAttachmentService.getReceivedAttachmentsData(+fileId)
            .pipe(
                map(data => ({
                    commonFileId: fileId,
                    commonFileName: data[0]?.fileName,
                    commonFileType: data[0]?.type
                }))
            );
    }

    private getClientsData() {
        const conditions = '?page=1&limit=9999';
        this.subscriptions.add(this.clientsService.getClients(conditions).subscribe((response) => {
            this.allClients = response.items.map(item => `${item.email}`.trim());

            if (this.draftView) {
                this.filteredClients = of(this.allClients.filter(client =>
                    !this.mailAddressArray.includes(client)));
            }
        }));
    }

    private filterClients(value: string): string[] {
        if (!value) {
            return this.allClients.filter(client => !this.mailAddressArray.includes(client));
        }

        const filterValue = value.toLowerCase();
        return this.allClients.filter(client =>
            client.toLowerCase().includes(filterValue) && !this.mailAddressArray.includes(client));
    }

    onEmailSelected(event: MatAutocompleteSelectedEvent): void {
        const selectedClient = event.option.viewValue;

        if (this.isValidEmail(selectedClient) && this.allClients.includes(selectedClient)) {
            const existingIndex = this.mailAddressArray.indexOf(selectedClient);
            if (existingIndex >= 0) {
                this.mailAddressArray.splice(existingIndex, 1);
            }

            this.mailAddressArray.push(selectedClient);
            this.updateMailAddress();
            this.onDraftChange();
        }

        if (this.sendersInput) {
            this.sendersInput.nativeElement.value = '';
        }
    }

    onTopicChange(): void {
        this.topicChangeSubject.next(null);
    }
}
