import { AfterViewChecked, Component, ElementRef, Input, Renderer2, OnChanges, SimpleChanges } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
    selector: 'app-mails-body',
    templateUrl: './mails-body.component.html',
    standalone: true,
    styleUrls: ['./mails-body.component.scss']
})
export class MailsBodyComponent implements OnChanges, AfterViewChecked {

    constructor(
        private sanitizer: DomSanitizer,
        private el: ElementRef,
        private renderer: Renderer2
    ) {}

    @Input() body: string;

    parsedEmailContent: SafeHtml;
    private initialized = false;

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['body'] && changes['body'].currentValue) {
            const emailContent = this.addMarginsToBlockquotes(this.body || '');
            this.parsedEmailContent = this.sanitizer.bypassSecurityTrustHtml(this.parseEmailString(emailContent)) ?? this.body;
        }
    }

    ngAfterViewChecked(): void {
        if (!this.initialized && this.parsedEmailContent) {
            this.addQuoteButtonListener();
            this.initialized = true;
        }
    }

    parseEmailString(emailStr: string): string {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = emailStr;

        const parentQuote = tempDiv.querySelector('blockquote');

        if (parentQuote) {
            parentQuote.style.display = 'none';
            parentQuote.classList.add('hidden-quote');

            const button = document.createElement('button');
            button.style.cursor = 'pointer';
            button.style.fontSize = '16px';
            button.style.border = '1px solid #ccc';
            button.style.background = '#f0f0f0';
            button.style.padding = '0';
            button.style.marginBottom = '8px';
            button.style.width = '24px';
            button.style.height = '24px';
            button.style.display = 'flex';
            button.style.alignItems = 'center';
            button.style.justifyContent = 'center';
            button.textContent = '...';
            button.classList.add('ellipsis-button');

            parentQuote.parentNode.insertBefore(button, parentQuote);
        }

        return tempDiv.innerHTML;
    }

    addQuoteButtonListener(): void {
        const buttons = this.el.nativeElement.querySelectorAll('.ellipsis-button');
        buttons.forEach(button => {
            this.renderer.listen(button, 'click', () => {
                const blockquote = button.nextElementSibling;
                if (blockquote && blockquote.style.display === 'none') {
                    blockquote.style.display = 'block';
                } else if (blockquote) {
                    blockquote.style.display = 'none';
                }
            });
        });
    }

    private addMarginsToBlockquotes(emailStr: string): string {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = emailStr;

        const blockquotes = tempDiv.querySelectorAll('blockquote');
        blockquotes.forEach((blockquote: HTMLElement) => {
            blockquote.style.marginBlockStart = '2px';
            blockquote.style.marginBlockEnd = '2px';
            blockquote.style.marginInlineStart = '2px';
            blockquote.style.marginInlineEnd = '2px';
        });

        return tempDiv.innerHTML;
    }
}
