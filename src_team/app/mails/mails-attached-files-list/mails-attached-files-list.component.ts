import {Component, Input} from '@angular/core';
import { MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle } from '@angular/material/expansion';
import { NgIf, NgFor } from '@angular/common';
import { DownloadButtonComponent } from '../../shared/download-button/download-button.component';
import { TranslatePipe } from '@ngx-translate/core';

interface CommonFileAttachment {
  commonFileId: string;
  commonFileName: string;
  commonFileType: string;
}
@Component({
    selector: 'app-mails-attached-files-list',
    templateUrl: './mails-attached-files-list.component.html',
    standalone: true,
    imports: [MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle, NgIf, NgFor, DownloadButtonComponent, TranslatePipe]
})

export class MailsAttachedFilesListComponent {

  @Input() commonFileObject: CommonFileAttachment[] = [];

  expanded = false;

  constructor() {
  }

}
