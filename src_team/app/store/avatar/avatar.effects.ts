import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType, ROOT_EFFECTS_INIT } from '@ngrx/effects';
import { catchError, map, mergeMap, switchMap, filter, take } from 'rxjs/operators';
import { from, of } from 'rxjs';
import * as AvatarActions from './avatar.actions';
import { UserAvatarService } from '../../services/user-avatar.service';
import { UserStoreService } from '../../services/store/user-store.service';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable()
export class AvatarEffects {
    loadAvatar$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AvatarActions.loadAvatar),
            mergeMap(action =>
                this.userStoreService.getUserFromStore(action.userId).pipe(
                    filter(user => !!user),
                    take(1),
                    mergeMap(user =>
                        this.userAvatarService.addUserAvatar(user).pipe(
                            map(userWithAvatar => {
                                const firstNamePart = userWithAvatar.User.firstname
                                    ? userWithAvatar.User.firstname.split(' ')[0]
                                    : '';
                                const lastNamePart = userWithAvatar.User.lastname
                                    ? userWithAvatar.User.lastname.split(' ')[0]
                                    : '';
                                return AvatarActions.loadAvatarSuccess({
                                    userId: action.userId,
                                    avatar: userWithAvatar.avatar || '',
                                    name: `${firstNamePart} ${lastNamePart}`
                                });
                            }),
                            catchError(error => {
                                return of(AvatarActions.loadAvatarFailure({ userId: action.userId, error }));
                            })
                        )
                    )
                )
            )
        )
    );

    loadAvatars$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AvatarActions.loadAvatars),
            switchMap(() =>
                this.userStoreService.users$.pipe(
                    switchMap(users => {
                        if (!users || users.length === 0) {
                            return of(AvatarActions.loadAvatarsFailure({
                                error: new HttpErrorResponse({
                                    error: 'No users found',
                                    status: 404,
                                    statusText: 'Not Found'
                                })
                            }));
                        }

                        return from(users).pipe(
                            mergeMap(user =>
                                this.userAvatarService.addUserAvatar(user).pipe(
                                    map(userWithAvatar => {
                                        const firstNamePart = userWithAvatar.User.firstname
                                            ? userWithAvatar.User.firstname.split(' ')[0]
                                            : '';
                                        const lastNamePart = userWithAvatar.User.lastname
                                            ? userWithAvatar.User.lastname.split(' ')[0]
                                            : '';

                                        return AvatarActions.loadAvatarSuccess({
                                            userId: userWithAvatar.User.id,
                                            avatar: userWithAvatar.avatar || '',
                                            name: `${firstNamePart} ${lastNamePart}`
                                        });
                                    }),
                                    catchError(error => {
                                        return of(AvatarActions.loadAvatarFailure({
                                            userId: user.id,
                                            error: error as HttpErrorResponse
                                        }));
                                    })
                                )
                            )
                        );
                    }),
                    catchError(error => of(AvatarActions.loadAvatarsFailure({
                        error: error as HttpErrorResponse
                    })))
                )
            )
        )
    );

    init$ = createEffect(() => {
        return this.actions$.pipe(
            ofType(ROOT_EFFECTS_INIT),
            map(() => AvatarActions.loadAvatars())
        );
    })

    constructor(
        private actions$: Actions,
        private userAvatarService: UserAvatarService,
        private userStoreService: UserStoreService
    ) {}
}
