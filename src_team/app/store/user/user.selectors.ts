import { createFeatureSelector, createSelector } from '@ngrx/store';
import { UserState } from './user.state';
import {UserInterface} from '../../common/interfaces/user.interface';

export const selectUserState = createFeatureSelector<UserState>('user');

export const selectAllUsers = createSelector(
    selectUserState,
    (state: UserState) => state.users
);

export const selectUsersLoading = createSelector(
    selectUserState,
    (state: UserState) => state.loading
);

export const selectUsersError = createSelector(
    selectUserState,
    (state: UserState) => state.error
);

export const selectCurrentUser = createSelector(
    selectUserState,
    (state: UserState) => state.currentUser
);

export const selectUserById = (userId: number) => createSelector(
    selectAllUsers,
    (users: UserInterface[]): UserInterface | undefined => {
        if (!users || users.length === 0) {
            return undefined;
        }

        return users.find(user => +user.id === +userId);
    }
);
