import { Injectable } from '@angular/core';
import {Actions, ofType, ROOT_EFFECTS_INIT, createEffect} from '@ngrx/effects';
import { EmailIntegrationService } from '../../services/email-integration.service';
import * as MailServerAddressesActions from './mail-server-addresses.actions';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { of } from 'rxjs';

@Injectable()
export class MailServerAddressesEffects {
    constructor(
        private actions$: Actions,
        private emailIntegrationService: EmailIntegrationService
    ) {}

    loadMailServerAddresses$ = this.actions$.pipe(
        ofType(MailServerAddressesActions.loadMailServerAddresses),
        mergeMap(() =>
            this.emailIntegrationService.getEmailIntegrationAddresses().pipe(
                map((addresses) =>
                    MailServerAddressesActions.loadMailServerAddressesSuccess({ addresses })
                ),
                catchError((error) =>
                    of(MailServerAddressesActions.loadMailServerAddressesFailure({ error }))
                )
            )
        )
    );

    init$ = createEffect(() => {
        return this.actions$.pipe(
            ofType(ROOT_EFFECTS_INIT),
            map(() => MailServerAddressesActions.loadMailServerAddresses())
        );
    })
}
