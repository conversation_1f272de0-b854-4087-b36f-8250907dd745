import { animate, state, style, transition, trigger } from '@angular/animations';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, forwardRef, HostListener, inject, On<PERSON>estroy, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';

import { Store } from '@ngrx/store';
import dayjs from 'dayjs';
import { CircleUserRound, icons, LucideAngularModule } from 'lucide-angular';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { EMPTY, interval, of, Subject, Subscription } from 'rxjs';
import { catchError, delay, map, shareReplay, switchMap, takeUntil } from 'rxjs/operators';

import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { IconComponent } from 'src_team/app/elements/icon/icon.component';
import { AlertDuration } from '../../common/enums/alert-duration.enum';
import { AlertType } from '../../common/enums/alert-type.enum';
import { CalendarEventInterface } from '../../common/interfaces/duty-event.interface';
import { FilterOption } from '../../common/interfaces/filter-option.interface';
import { UserThreadAssociationInterface } from '../../common/interfaces/user-thread-association.interface';
import { UserInterface } from '../../common/interfaces/user.interface';
import { DB_DATE_FORMAT } from '../../common/utils/date-utils';
import { DropdownComponent } from '../../elements/dropdown/dropdown.component';
import { SearchInputComponent } from '../../elements/search-input/search-input.component';
import { AlertService } from '../../services/alert.service';
import { ApplicationSettingsService } from '../../services/application-settings.service';
import { AuthService } from '../../services/auth/auth.service';
import { DutyScheduleService } from '../../services/duty-schedule.service';
import { GroupService } from '../../services/group.service';
import { IssueService } from '../../services/issue.service';
import { ThreadsInternalService } from '../../services/livechat-wfirma/threads-internal.service';
import { SocketUsersStatusesService } from '../../services/sockets/socket-users-statuses.service';
import { UserStoreService } from '../../services/store/user-store.service';
import { UserAvatarService } from '../../services/user-avatar.service';
import { UserInputStorageService } from '../../services/user-input-storage.service';
import { DialogDetailsComponent } from '../../shared/dialog-details/dialog-details.component';
import { selectAllUsers } from '../../store/user/user.selectors';
import { UserState } from '../../store/user/user.state';
import { UserSidebarAvatarComponent } from '../user-sidebar-avatar/user-sidebar-avatar.component';

@Component({
    selector: 'app-user-sidebar',
    templateUrl: './user-sidebar.component.html',
    styleUrls: ['./user-sidebar.component.scss'],
    animations: [
        trigger('slideInOut', [
            state('closed', style({
                transform: 'translateX(100%)'
            })),
            state('open', style({
                transform: 'translateX(0)'
            })),
            transition('closed => open', animate('300ms ease-in')),
            transition('open => closed', animate('300ms ease-out'))
        ]),
        trigger('buttonSlide', [
            state('closed', style({
                right: '0'
            })),
            state('open', style({
                right: '264px'
            })),
            transition('closed => open', animate('300ms ease-in')),
            transition('open => closed', animate('300ms ease-out'))
        ])
    ],
    imports: [
        forwardRef(() => DialogDetailsComponent),
        SearchInputComponent,
        FormsModule,
        DropdownComponent,
        NgFor,
        UserSidebarAvatarComponent,
        NgClass,
        DefaultClassDirective,
        NgIf,
        TranslatePipe,
        LucideAngularModule,
        IconComponent
    ]
})
export class UserSidebarComponent implements OnInit, OnDestroy {
    @ViewChild('usersPanel') usersPanel: ElementRef;
    @ViewChild('toggleBtn') toggleBtn: ElementRef;

    isPanelOpen = false;
    selectedGroupValue: number;
    groups: FilterOption[];
    groupListSub: Subscription;
    allUsers: UserInterface[];
    users: UserInterface[];
    baseUsers: UserInterface[];
    name: string;
    actualAvatar: string;
    userSub$: Subscription;
    active: boolean;
    userThreadsIds: UserThreadAssociationInterface[];
    filters: FilterOption[];
    usersSubscription: Subscription;
    groupsSubscription: Subscription;
    threadId: string;
    searchValue: string = '';

    usersStatusSubscription = new Subscription();
    usersLoaded$ = new Subject();

    dutyEvents: any[] = [];
    dutySubscription: Subscription;
    dutyRefreshInterval: Subscription;
    isDetailsOpen = false;
    detailsData: any;
    detailsModel = 'users';

    isDelegateMode = false;
    delegateIssueSubscription: Subscription;
    private destroy$ = new Subject<void>();
    justOpenedInDelegateMode = false;

    readonly DUTY_GROUP_ID = -1;

    private alertService: AlertService = inject(AlertService);

    constructor(
        private userStore: Store<UserState>,
        private userStoreService: UserStoreService,
        private groupService: GroupService,
        private userAvatarService: UserAvatarService,
        public dialog: MatDialog,
        private threadsInternalService: ThreadsInternalService,
        private router: Router,
        private authService: AuthService,
        private userInputStorageService: UserInputStorageService,
        private socketUsersStatusesService: SocketUsersStatusesService,
        private applicationSettingsService: ApplicationSettingsService,
        private dutyScheduleService: DutyScheduleService,
        private cdr: ChangeDetectorRef,
        private translate: TranslateService,
        private issueService: IssueService
    ) {
        dayjs.locale('pl');
    }

    ngOnInit() {
        this.getGroups();
        this.getUsers();
        this.getUserAvatar();
        this.checkThreadExists();

        this.setSocketSubscriptions();
        this.setUserRefreshSubscription();
        this.setDelegateSubscription();

        this.socketUsersStatusesService.join();

        this.loadDutyEvents();
    }

    private setDelegateSubscription() {
        this.delegateIssueSubscription = this.issueService.delegateIssueId
            .pipe(takeUntil(this.destroy$))
            .subscribe(issueId => {
                const wasInDelegateMode = this.isDelegateMode;
                this.isDelegateMode = !!issueId;

                if (this.isDelegateMode) {
                    this.isPanelOpen = true;
                    this.justOpenedInDelegateMode = true;
                } else if (wasInDelegateMode) {
                    this.isPanelOpen = false;
                    this.justOpenedInDelegateMode = false;
                }

                this.cdr.detectChanges();
            });
    }

    ngOnDestroy() {
        this.usersSubscription?.unsubscribe();
        this.groupsSubscription?.unsubscribe();
        this.groupListSub?.unsubscribe();
        this.userSub$?.unsubscribe();
        this.usersStatusSubscription?.unsubscribe();
        this.dutySubscription?.unsubscribe();
        this.dutyRefreshInterval?.unsubscribe();
        this.delegateIssueSubscription?.unsubscribe();

        this.destroy$.next();
        this.destroy$.complete();

        this.socketUsersStatusesService.close();
    }

    private loadDutyEvents() {
        this.dutySubscription = this.getDutyEvents$().pipe(
            delay(100)
        ).subscribe(events => {
            this.dutyEvents = events;

            if (this.users) {
                this.users = this.users.map(user => ({
                    ...user,
                    hasDuty: this.isUserOnDuty(+user.id)
                }));
            }

            if (this.baseUsers) {
                this.baseUsers = this.baseUsers.map(user => ({
                    ...user,
                    hasDuty: this.isUserOnDuty(+user.id)
                }));
            }

            if (this.allUsers) {
                this.allUsers = this.allUsers.map(user => ({
                    ...user,
                    hasDuty: this.isUserOnDuty(+user.id)
                }));
            }

            if (this.selectedGroupValue === this.DUTY_GROUP_ID) {
                this.onSelectGroupValue(this.DUTY_GROUP_ID);
            }

            this.cdr.detectChanges();
        });
    }

    private getDutyEvents$() {
        const momentDate = dayjs(),
            dateRange = {
                dateStart: momentDate.startOf('day').format(DB_DATE_FORMAT).toString(),
                dateEnd: momentDate.endOf('day').format(DB_DATE_FORMAT).toString()
            };

        return this.dutyScheduleService.getDutyEvents(dateRange)
            .pipe(
                map((dutyEvents: CalendarEventInterface[]) =>
                    dutyEvents.map(event => ({
                        userIds: event.userIds,
                        start: dayjs(event.start),
                        end: dayjs(event.end)
                    }))
                ),
                catchError(error => of([])),
                shareReplay(1)
            );
    }

    private isUserOnDuty(userId: number) {
        if (!this.dutyEvents || !this.dutyEvents.length) {
            return false;
        }

        const userEvents = this.dutyEvents.filter(event =>
            event.userIds && event.userIds.some(id => +id === +userId)
        );

        if (!userEvents.length) {
            return false;
        }

        const now = Date.now(),
            userCurrentEvents = userEvents.filter(event =>
                event.start.valueOf() <= now && event.end.valueOf() >= now);

        return !!userCurrentEvents.length;
    }

    @HostListener('document:click', ['$event'])
    clickOutside(event: Event) {
        if (!this.isPanelOpen) {
            return;
        }

        if (this.isDelegateMode && this.justOpenedInDelegateMode) {
            return;
        }

        if (this.selectedGroupValue === 0 || this.selectedGroupValue === null) {
            return;
        }

        const panel = this.usersPanel?.nativeElement;
        const toggleButton = this.toggleBtn?.nativeElement;
        const clickedElement = event.target as HTMLElement;

        const isOutsideClick = panel && toggleButton &&
            !panel.contains(clickedElement) &&
            !toggleButton.contains(clickedElement);

        if (isOutsideClick) {
            if (this.isDelegateMode) {
                return;
            }

            this.isPanelOpen = false;
        }
    }

    cancelDelegation(event: MouseEvent) {
        event.preventDefault();
        event.stopPropagation();

        this.isPanelOpen = false;
        this.issueService.endDelegateMode();
        this.resetSidebar();
    }

    getGroupPlaceholder(): string {
        if (this.selectedGroupValue === this.DUTY_GROUP_ID) {
            return this.translate.instant('USER-SIDEBAR.GROUP-DUTY');
        }
        return this.translate.instant('USER-FILTERS.GROUP');
    }

    togglePanel(event: Event) {
        event.stopPropagation();

        if (this.isDelegateMode && this.isPanelOpen) {
            return;
        }

        this.isPanelOpen = !this.isPanelOpen;

        if (this.isPanelOpen) {
            this.loadDutyEvents();
        } else if (this.isDelegateMode) {
            this.issueService.endDelegateMode();
            this.resetSidebar();
        }
    }

    search() {
        if (!this.searchValue.trim()) {
            this.clearSearch();
        } else {
            this.applySearch();
        }
    }

    private applySearch() {
        const searchTerm = this.searchValue.toLowerCase().trim();

        if (!searchTerm) {
            this.users = [...this.baseUsers];
            return;
        }

        this.users = this.baseUsers.filter(user => {
            return user.firstname.toLowerCase().includes(searchTerm) ||
                user.lastname.toLowerCase().includes(searchTerm);
        });
    }

    clearSearch() {
        this.searchValue = '';
        this.applySearch();
    }

    getUsers() {
        this.usersSubscription = this.userStore.select(selectAllUsers)
            .pipe(
                map(users => users
                    .filter(user => +user.banned !== 1)
                    .map(user => {
                        return {
                            ...user,
                            isOnline: false,
                            lastActiveTimePassed: 0,
                            hasDuty: false
                        };
                    })
                    .sort((a, b) => {
                        if (a.isOnline !== b.isOnline) {
                            return a.isOnline ? -1 : 1;
                        }
                        return a.firstname.localeCompare(b.firstname);
                    })
                )
            )
            .subscribe(users => {
                if (users) {
                    this.allUsers = users;

                    if (!this.selectedGroupValue) {
                        this.baseUsers = users;
                        this.users = users;
                    } else if (this.selectedGroupValue) {
                        this.onSelectGroupValue(this.selectedGroupValue);
                    }

                    this.usersLoaded$.next(null);
                    this.socketUsersStatusesService.getCurrentUsersStatuses();

                    if (this.dutyEvents && this.dutyEvents.length) {
                        this.updateUsersWithDutyStatus();
                    }
                }
            });
    }

    private updateUsersWithDutyStatus() {
        if (this.users) {
            this.users = this.users.map(user => ({
                ...user,
                hasDuty: this.isUserOnDuty(+user.id)
            }));
        }

        if (this.baseUsers) {
            this.baseUsers = this.baseUsers.map(user => ({
                ...user,
                hasDuty: this.isUserOnDuty(+user.id)
            }));
        }

        if (this.allUsers) {
            this.allUsers = this.allUsers.map(user => ({
                ...user,
                hasDuty: this.isUserOnDuty(+user.id)
            }));
        }

        this.cdr.detectChanges();
    }

    private setSocketSubscriptions() {
        this.usersStatusSubscription.add(
            this.socketUsersStatusesService.usersStatusObservable.subscribe(user => {
                this.updateUserStatus(user.id.toString(), user.status === 'online');
                this.sortUsersByStatus();
            })
        );

        this.usersStatusSubscription.add(
            this.usersLoaded$.subscribe(() => {
                this.socketUsersStatusesService.getCurrentUsersStatuses();
            })
        );
    }

    private setUserRefreshSubscription() {
        const refreshInterval = this.applicationSettingsService.getValue('USERS_REFRESH_INTERVAL') || 60000;

        this.usersStatusSubscription.add(
            interval(refreshInterval).subscribe(() => {
                this.sortUsersByStatus();
            })
        );
    }

    private resetSidebar() {
        this.searchValue = '';

        const currentGroupValue = this.selectedGroupValue;

        if (currentGroupValue) {
            this.onSelectGroupValue(currentGroupValue);
        } else {
            this.baseUsers = this.allUsers;
            this.users = this.allUsers;
        }
    }

    private updateUserStatus(userId: string, isOnline: boolean) {
        const userInAll = this.allUsers.find(item => item.id.toString() === userId);
        if (userInAll && userInAll.isOnline !== isOnline) {
            userInAll.lastActiveTimePassed = Date.now();
            userInAll.isOnline = isOnline;
        }

        const userInBase = this.baseUsers.find(item => item.id.toString() === userId);
        if (userInBase && userInBase.isOnline !== isOnline) {
            userInBase.lastActiveTimePassed = Date.now();
            userInBase.isOnline = isOnline;
        }

        const userInCurrent = this.users.find(item => item.id.toString() === userId);
        if (userInCurrent && userInCurrent.isOnline !== isOnline) {
            userInCurrent.lastActiveTimePassed = Date.now();
            userInCurrent.isOnline = isOnline;
        }
    }

    private sortUsersByStatus() {
        if (this.users && this.users.length) {
            this.users = [...this.users].sort((a, b) => {
                if (a.isOnline !== b.isOnline) {
                    return a.isOnline ? -1 : 1;
                }

                if (!a.isOnline && !b.isOnline && a.lastActiveTimePassed && b.lastActiveTimePassed) {
                    return b.lastActiveTimePassed - a.lastActiveTimePassed;
                }

                return a.lastname.localeCompare(b.lastname) ||
                    a.firstname.localeCompare(b.firstname);
            });
        }

        if (this.baseUsers && this.baseUsers.length) {
            this.baseUsers = [...this.baseUsers].sort((a, b) => {
                if (a.isOnline !== b.isOnline) {
                    return a.isOnline ? -1 : 1;
                }

                if (!a.isOnline && !b.isOnline && a.lastActiveTimePassed && b.lastActiveTimePassed) {
                    return b.lastActiveTimePassed - a.lastActiveTimePassed;
                }

                return a.lastname.localeCompare(b.lastname) ||
                    a.firstname.localeCompare(b.firstname);
            });
        }
    }

    getGroups() {
        this.groupListSub = this.groupService.getGroupListObs().pipe(
            map((res: any) => res.map(elem => ({
                value: +elem.id,
                viewValue: elem.name
            })))
        ).subscribe(groups => {
            if (!groups.length) {
                return;
            }

            this.groups = [
                ...groups,
                { value: this.DUTY_GROUP_ID, viewValue: this.translate.instant('USER-SIDEBAR.GROUP-DUTY') }
            ];

            const savedFilter = this.userInputStorageService.getValue('filters_usersSidebar');

            if (savedFilter) {
                // tslint:disable-next-line:no-shadowed-variable
                const filter = JSON.parse(savedFilter);

                if (filter && filter.type === 'groups' && filter.value) {
                    this.selectedGroupValue = filter.value;
                    this.onSelectGroupValue(filter.value);
                } else {
                    this.getUsers();
                }
            } else {
                this.getUsers();
            }
        });
    }

    onSelectGroupValue(id: number) {
        this.selectedGroupValue = id;

        if (id === this.DUTY_GROUP_ID) {
            const dutyUsers = this.allUsers.filter(user => this.isUserOnDuty(+user.id));

            const sortedUsers = dutyUsers.sort((a, b) => {
                if (a.isOnline !== b.isOnline) {
                    return a.isOnline ? -1 : 1;
                }
                return a.lastname.localeCompare(b.lastname) ||
                    a.firstname.localeCompare(b.firstname);
            });

            this.baseUsers = sortedUsers;
            this.users = sortedUsers;
        } else {
            this.userStoreService.getUsersForGroup(id)
                .pipe(
                    map(users => users.map(user => {
                        const userObj = user['User'];

                        const existingUser = this.allUsers.find(u => u.id === userObj.id);

                        return {
                            ...userObj,
                            isOnline: existingUser ? existingUser.isOnline : false,
                            lastActiveTimePassed: existingUser ? existingUser.lastActiveTimePassed : 0,
                            hasDuty: existingUser ? existingUser.hasDuty : false
                        };
                    }))
                )
                .subscribe(users => {
                    const sortedUsers = users.sort((a, b) => {
                        if (a.isOnline !== b.isOnline) {
                            return a.isOnline ? -1 : 1;
                        }
                        return a.lastname.localeCompare(b.lastname) ||
                            a.firstname.localeCompare(b.firstname);
                    });

                    this.baseUsers = sortedUsers;
                    this.users = sortedUsers;

                    this.updateUsersWithDutyStatus();
                });
        }

        this.saveFilterSettings('usersSidebar', 'groups', id);
    }

    getUserAvatar() {
        this.userSub$ = this.userStoreService.getUserFromStore(86)
            .pipe(
                switchMap(user => {
                    if (!user) {
                        return EMPTY;
                    }
                    return this.userAvatarService.addUserAvatar(user);
                })
            )
            .subscribe((userWithAvatar: any) => {
                if (!userWithAvatar || !userWithAvatar.User) {
                    return;
                }

                const firstNamePart = userWithAvatar.User.firstname ? userWithAvatar.User.firstname.split(' ')[0] : '';
                const lastNamePart = userWithAvatar.User.lastname ? userWithAvatar.User.lastname.split(' ')[0] : '';

                this.name = `${firstNamePart} ${lastNamePart}`;
                this.actualAvatar = userWithAvatar.avatar || ' ';
            });
    }

    onClickUser(event: MouseEvent, userDetails: UserInterface) {
        event.preventDefault();
        event.stopPropagation();

        if (this.isDelegateMode && this.issueService.delegateIssueData) {
            this.delegateIssueToUser(userDetails.id);
        } else {
            this.isPanelOpen = false;

            if (+this.detailsData?.id !== +userDetails?.id) {
                this.isDetailsOpen = false;
                this.detailsData = userDetails;
                this.detailsModel = 'users';
                this.isDetailsOpen = true;
            } else {
                this.isDetailsOpen = !this.isDetailsOpen;
            }
        }
    }

    private delegateIssueToUser(userId: number) {
        const issueData = this.issueService.delegateIssueData;
        if (!issueData || !issueData.id) {
            return;
        }

        const delegateToUser = this.allUsers.find(user => +user.id === +userId);
        const userName = delegateToUser ? `${delegateToUser.firstname} ${delegateToUser.lastname}` : '';

        this.issueService.setOwnership(issueData.id, userId)
            .subscribe({
                next: result => {
                    if (!result || result.status !== 'OK') {
                        this.alertService.showAlert(this.translate.instant('USER-SIDEBAR.CANNOT-DELEGATED-ISSUE'), AlertType.ERROR, AlertDuration.MEDIUM);
                        return;
                    }

                    this.isPanelOpen = false;
                    this.issueService.endDelegateMode();
                    this.resetSidebar();

                    const message = userName
                        ? `${this.translate.instant('USER-SIDEBAR.DELEGATED-ISSUE')} ${this.translate.instant('USER-SIDEBAR.TO')} ${userName}`
                        : this.translate.instant('USER-SIDEBAR.DELEGATED-ISSUE');

                    this.alertService.showAlert(message, AlertType.SUCCESS, AlertDuration.MEDIUM);
                },
                error: () => {
                    this.alertService.showAlert(this.translate.instant('USER-SIDEBAR.CANNOT-DELEGATED-ISSUE'), AlertType.ERROR, AlertDuration.MEDIUM);
                }
            });
    }

    private checkThreadExists() {
        this.threadsInternalService.getUserThreadAssociation().subscribe(ids => {
            this.userThreadsIds = ids;
        });
    }

    protected isThreadExist(userId: number) {
        const userIds = this.userThreadsIds.map(thread => thread.user_id);

        const isExist = userIds.includes(String(userId));

        if (isExist) {
            const foundThread = this.userThreadsIds.find(thread => thread.user_id === String(userId));

            this.threadId = foundThread ? foundThread.chat_internal_thread_id : null;
        }

        return isExist;
    }

    private saveFilterSettings(listName: string, type: string, value: number) {
        if (!listName) {
            return;
        }

        const settings = {
            type: type,
            value: value
        };

        this.userInputStorageService.setValue('filters_' + listName, JSON.stringify(settings));
    }

    trackById(index: number, user: any): number {
        return user.id;
    }

    protected readonly icons = icons;
    protected readonly CircleUserRound = CircleUserRound;
}
