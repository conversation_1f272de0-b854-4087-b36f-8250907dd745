<app-dialog-details
        [isOpen]="isDetailsOpen"
        [data]="detailsData"
        [model]="detailsModel"
        (isOpenChange)="isDetailsOpen = $event">
</app-dialog-details>

<div class="dimmer" *ngIf="isDelegateMode" (click)="cancelDelegation($event)"></div>

<div class="container">
    <button #toggleBtn (click)="togglePanel($event)"
            class="toggle-button"
            [@buttonSlide]="isPanelOpen ? 'open' : 'closed'">
        <i-lucide name="CircleUserRound" class="btn-icon"></i-lucide>
    </button>

    <div #usersPanel
         class="panel" [ngClass]="{'delegate-panel': isDelegateMode}" [@slideInOut]="isPanelOpen ? 'open' : 'closed'"
         (click)="$event.stopPropagation()"
    >
        <div class="panel-content">
            <app-5ways-search-input
                    [placeholder]="'ISSUE-LIST-TOOLBAR.SEARCH' | translate"
                    [(ngModel)]="searchValue"
                    (searchTriggered)="search()"
                    width="232"
            ></app-5ways-search-input>

            <app-5ways-dropdown
                    fullSize="true"
                    [options]="groups"
                    [(ngModel)]="selectedGroupValue"
                    (selectionChange)="onSelectGroupValue($event)"
                    [placeholder]="getGroupPlaceholder()">

            </app-5ways-dropdown>

            <div class="panel-content__users">
                <div class="panel-content__users__header" [ngClass]="{'delegate-mode': isDelegateMode}">
                    <span>{{ isDelegateMode ? ('USER-SIDEBAR.DELEGATE-TO' | translate) : '' }}</span>
                    <button *ngIf="isDelegateMode" class="cancel-delegate-btn" (click)="cancelDelegation($event)">
                        <i appLucideIcons data-lucide="x" class="cancel-icon"></i>
                    </button>
                </div>
                <div class="panel-content__users__content">
                    <div class="panel-content__users__content__item" *ngFor="let user of users; trackBy: trackById" (click)="onClickUser($event, user)">
                        <div class="avatar-container">
                            <app-user-sidebar-avatar size="28" [userId]="user.id"></app-user-sidebar-avatar>
                            <div class="user-status-indicator" [ngClass]="{'online': user.isOnline, 'offline': !user.isOnline}"></div>
                            <div class="user-duty-indicator" *ngIf="user.hasDuty">
                                <app-5ways-icon name="calendar" stroke="#1A9267" ></app-5ways-icon>
                            </div>
                        </div>
                        {{ user.firstname}} {{ user.lastname }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
