import {Component, Inject, OnInit} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import {UserAddComponent} from '../../settings/users/user-add/user-add.component';
import {UserService} from '../../services/user.service';
import {AuthService} from '../../services/auth/auth.service';
import { UserProfileComponent } from '../user-profile/user-profile.component';
import { NgIf } from '@angular/common';
import { MatButton } from '@angular/material/button';
import { DefaultFlexDirective } from 'ngx-flexible-layout/flex';
import { CheckPermissionNamePipe } from '../../shared/pipes/check-permission-name.pipe';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-user-profile-dialog',
    templateUrl: './user-profile-dialog.component.html',
    styleUrls: ['./user-profile-dialog.component.scss'],
    imports: [UserProfileComponent, MatDialogActions, NgIf, MatButton, DefaultFlexDirective, MatDialogClose, CheckPermissionNamePipe, TranslatePipe]
})
export class UserProfileDialogComponent implements OnInit {
    userIsAdmin: number;
    isAdmin;

    constructor(
        @Inject(MAT_DIALOG_DATA) public data: { userId: number, userThreadsIds: number[] },
        private dialog: MatDialog,
        private _userService: UserService,
        private _authService: AuthService) {
    }

    ngOnInit() {
        this.getUserProfile();
        this.getCurrentUser();
    }

    editUserDialog() {
        this.dialog.open(UserAddComponent, {
            width: '500px',
            data: {userId: this.data.userId}
        });

    }

    getUserProfile() {
        this._userService.getUser(this.data.userId)
            .subscribe(data => {
                this.userIsAdmin = +data.is_admin;
            });
    }

    getCurrentUser() {
        this._userService.getUser(this._authService.getUserId())
            .subscribe(data => {
                this.isAdmin = +data.is_admin;
            });
    }
}
