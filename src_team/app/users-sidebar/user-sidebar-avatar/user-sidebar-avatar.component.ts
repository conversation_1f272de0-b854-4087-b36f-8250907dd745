import { Component, Input, OnInit } from '@angular/core';
import { Store, select } from '@ngrx/store';
import * as AvatarActions from '../../store/avatar/avatar.actions';
import { Observable } from 'rxjs';
import {AvatarState} from '../../store/avatar/avatar.reducer';
import { AvatarModule } from 'ngx-avatars';
import { NgIf, NgClass, AsyncPipe } from '@angular/common';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import {LucideAngularModule} from 'lucide-angular';

@Component({
    selector: 'app-user-sidebar-avatar',
    template: `
        <div class="flex-center user-avatar" (click)="toggleChevron()">
            <ngx-avatars
                    class="avatar-border"
                    data-cy="avatar"
                    borderColor="#C9DFFF"
                    [size]="size"
                    [name]="(avatar$ | async)?.name"
                    [src]="(avatar$ | async)?.avatar || ''"
            ></ngx-avatars>
            <div *ngIf="showChevron" class="user-avatar-container" [ngClass]="{'active': active}">
                <i-lucide name="chevron-down" class="user-avatar-icon"></i-lucide>
            </div>
        </div>
    `,
    styleUrls: ['user-avatar-display.component.scss'],
    standalone: true,
    imports: [AvatarModule, NgIf, NgClass, DefaultClassDirective, AsyncPipe, LucideAngularModule]
})
export class UserSidebarAvatarComponent implements OnInit {
    @Input() userId: number;
    @Input() size = 40;
    @Input() showChevron: boolean = false;

    avatar$: Observable<{ avatar: string; name: string }>;
    active: boolean;

    constructor(
        private store: Store<{ avatar: AvatarState }>
    ) {}

    ngOnInit() {
       this.getUserAvatar();
    }

    toggleChevron() {
        this.active = !this.active;
    }

    getUserAvatar() {
        this.avatar$ = this.store.pipe(
            select(state => state.avatar.avatars[this.userId])
        );
    }
}
