import {Component, Input, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {UserProfileDialogComponent} from '../user-profile-dialog/user-profile-dialog.component';
import {MatDialog, MatDialogRef} from '@angular/material/dialog';
import {Subscription} from 'rxjs';
import {UserService} from '../../services/user.service';
import {GroupService} from '../../services/group.service';
import {ReportService} from '../../services/report.service';
import {AuthService} from '../../services/auth/auth.service';
import {UserInterface} from '../../common/interfaces/user.interface';
import {GroupInterface} from '../../common/interfaces/group.interface';
import {LoadingStatus} from '../../common/enums/loading-status.enum';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {ThreadsInternalService} from '../../services/livechat-wfirma/threads-internal.service';
import {UserThreadAssociationInterface} from '../../common/interfaces/user-thread-association.interface';
import {switchMap} from 'rxjs/operators';
import {CustomerService} from '../../services/Customer.service';
import { Router } from '@angular/router';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective, FlexFillDirective, DefaultLayoutGapDirective } from 'ngx-flexible-layout/flex';
import { NgIf, NgFor } from '@angular/common';
import { MatTooltip } from '@angular/material/tooltip';
import { MatIcon } from '@angular/material/icon';
import { MatChipListbox, MatChip } from '@angular/material/chips';
import { MatAnchor } from '@angular/material/button';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-user-info',
    templateUrl: './user-info.component.html',
    styleUrls: ['./user-info.component.scss'],
    imports: [DefaultLayoutDirective, DefaultLayoutAlignDirective, NgIf, FlexFillDirective, DefaultLayoutGapDirective, MatTooltip, MatIcon, MatChipListbox, NgFor, MatChip, MatAnchor, MatProgressSpinner, TranslatePipe]
})
export class UserInfoComponent implements OnInit, OnDestroy {
    @Input() userId: number;
    @Input() userThreadsIds: UserThreadAssociationInterface[] = [];

    userData: UserInterface;
    mentorData: UserInterface;
    groups: GroupInterface[];
    loadingStatus = LoadingStatus.loading;
    timer: string;
    threadId: string;
    userEditedSubscription: Subscription;
    chatInternal: number;
    isUserOwnId;

    constructor(
        private dialog: MatDialog,
        private userService: UserService,
        private groupService: GroupService,
        private reportService: ReportService,
        private authService: AuthService,
        private threadsInternalService: ThreadsInternalService,
        private customerService: CustomerService,
        public translate: TranslateService,
        private router: Router,
        private dialogRef: MatDialogRef<UserInfoComponent>,
    ) {
    }

    private getUserGroups() {
        this.groupService.getGroupsByUserId(this.userId).subscribe(result => {
            this.groups = result;
        });
    }

    private getMentorData(mentorId: number) {
        if (!+mentorId) {
            return;
        }

        this.userService.getUser(mentorId).subscribe(result => {
            this.mentorData = result;
        });
    }

    private getUserWorkTime() {
        this.reportService.getLoggedUserWorkTime().subscribe(result => {
            const date = new Date(null);

            date.setSeconds(result);

            this.timer = date.toISOString().substr(11, 5);
        });
    }

    private getAllData() {
        this.loadingStatus = LoadingStatus.loading;

        this.userService.getUser(this.userId).subscribe(result => {
            this.loadingStatus = LoadingStatus.loaded;
            this.userData = result;
            this.getMentorData(this.userData.mentor_id);
            this.getUserGroups();

            if (+this.userData.id !== this.authService.getUserId()) {
                return;
            }

            this.getUserWorkTime();
        });
    }

    private setUserEditedSubscription() {
        this.userEditedSubscription = this.userService.userEdited.subscribe((userId: number) => {
            if (userId === this.userId) {
                this.getAllData();
            }
        });
    }

    ngOnInit() {
        this.setUserEditedSubscription();
        this.getAllData();
        this.isThreadExist();
        this.checkChatInternalStatus();
        this.checkIsOwnId();
    }

    ngOnDestroy() {
        this.userEditedSubscription?.unsubscribe();
    }

    showMentorProfile(mentorId) {
        this.dialog.open(UserProfileDialogComponent, {
            width: '690px',
            height: '620px',
            data: mentorId,
            autoFocus: false,
            panelClass: 'full-width-dialog'
        });
    }

    initChat() {
        this.threadsInternalService.initThread().pipe(
            switchMap((val: any) => {
                this.threadId = val.id;
                const threadId = val.id;
                return this.threadsInternalService.createUserThreadAssociation(this.userId, threadId);
            })
        ).subscribe(() => {
            this.router.navigate(['/internal/thread/', this.threadId]);
            this.dialogRef.close();
            this.getNewThreadId();
        });
    }

    protected isThreadExist() {

        const userIds = this.userThreadsIds.map(thread => thread.user_id);

        const isExist = userIds.includes(String(this.userId));

        if (isExist) {

            const foundThread = this.userThreadsIds.find(thread => thread.user_id === String(this.userId));
            this.threadId = foundThread ? foundThread.chat_internal_thread_id : null;
        }

        return isExist;
    }

    private checkChatInternalStatus() {
        this.customerService.getList()
            .subscribe(data => {
                this.chatInternal = +data[0]['internal_chat'];
            });
    }

    protected checkIsOwnId() {
        const userIdFromService = this.authService.getUserId();

        if (+userIdFromService === +this.userId) {
            this.isUserOwnId = 1;
        }
    }

    private getNewThreadId() {
        this.threadsInternalService.getUserThreadAssociation().subscribe(ids => {
            const isExistingThread = this.userThreadsIds.some(thread => thread.user_id === String(this.userId));

            if (!isExistingThread) {
                const newThread = {
                    user_id: String(this.userId),
                    chat_internal_thread_id: this.threadId
                };

                this.userThreadsIds.push(newThread);
            }

        });
    }

    closeDialog() {
        this.dialogRef.close();
    }
}
