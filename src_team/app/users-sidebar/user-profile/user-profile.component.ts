import {Component, Input} from '@angular/core';
import {UserThreadAssociationInterface} from '../../common/interfaces/user-thread-association.interface';
import { IssueColumn } from 'src_team/app/common/enums/issue-column.enum';
import { Mat<PERSON>ab<PERSON>roup, MatTab, MatTabContent } from '@angular/material/tabs';
import { UserInfoComponent } from '../user-info/user-info.component';
import { NgIf } from '@angular/common';
import { IssueListComponent } from '../../issues/issue-list/issue-list.component';
import { UserStatisticsComponent } from '../user-statistics/user-statistics.component';
import { UserLogComponent } from '../user-log/user-log.component';
import { CheckPermissionNamePipe } from '../../shared/pipes/check-permission-name.pipe';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-user-profile',
    templateUrl: './user-profile.component.html',
    styleUrls: ['./user-profile.component.scss'],
    imports: [<PERSON><PERSON>ab<PERSON>roup, MatTab, UserInfoComponent, Mat<PERSON>ab<PERSON>ontent, NgIf, IssueListComponent, UserStatisticsComponent, UserLogComponent, CheckPermissionNamePipe, TranslatePipe]
})
export class UserProfileComponent {
    @Input() userId: number;
    @Input() userThreadsIds: UserThreadAssociationInterface[] = [];

    showIssuesTab = false;

    public readonly workerShowColumns: IssueColumn[] = [
        IssueColumn.ISSUE_INTERNAL_ID,
        IssueColumn.NAME,
        IssueColumn.CLIENT,
    ]

    constructor() {
    }

    onTabChange(event) {
        if (event.index === 1 && !this.showIssuesTab) {
            this.showIssuesTab = true;
        }
    }
}
