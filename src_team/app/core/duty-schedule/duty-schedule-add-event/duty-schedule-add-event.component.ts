import { CdkDrag, CdkDragHandle } from '@angular/cdk/drag-drop';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { AsyncPipe } from '@angular/common';
import { Component, ElementRef, Inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { MatOption } from '@angular/material/autocomplete';
import { MatChipListbox, MatChipRemove, MatChipRow } from '@angular/material/chips';
import { MatDatepicker, MatDatepickerInput, MatDatepickerToggle } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogActions, MatDialogClose, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { MatError, MatFormField, MatLabel, MatSelect, MatSuffix } from '@angular/material/select';
import { MatTooltip } from '@angular/material/tooltip';

import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import dayjs from 'dayjs';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { Observable, Subject, Subscription, from, of } from 'rxjs';
import { concatMap, map, startWith } from 'rxjs/operators';

import { ButtonVariant } from 'src_team/app/common/enums/button-variant.enum';
import { IconComponent } from 'src_team/app/elements/icon/icon.component';
import { DB_DATE_FORMAT } from '../../../common/utils/date-utils';
import { isStringInFirstNameOrLastName } from '../../../common/utils/search-utils';
import { ButtonComponent } from "../../../elements/button/button.component";
import { DutyScheduleService } from '../../../services/duty-schedule.service';
import { IsMobileService } from '../../../services/is-mobile.service';
import { UserStoreService } from '../../../services/store/user-store.service';
import { UserInputStorageService } from '../../../services/user-input-storage.service';
import { UserGroupFilterComponent } from '../../../shared/user-group-filter/user-group-filter.component';

const HoursRangeValidator: ValidatorFn = (formGroup: UntypedFormGroup) => {
    const start = formGroup.get('hourStartCtrl').value,
        end = formGroup.get('hourEndCtrl').value;

    if (!start || !end) {
        return null;
    }

    if (start.hours < end.hours || (start.hours === end.hours && +start.minutes < +end.minutes)) {
        return null;
    }

    return {range: true };
};

@Component({
    selector: 'app-duty-schedule-add-event',
    templateUrl: './duty-schedule-add-event.component.html',
    styleUrls: ['./duty-schedule-add-event.component.scss'],
    imports: [
        MatProgressSpinner,
        MatDialogTitle,
        CdkDrag,
        CdkDragHandle,
        CdkScrollable,
        MatDialogContent,
        FormsModule,
        ReactiveFormsModule,
        MatFormField,
        MatLabel,
        MatSelect,
        MatTooltip,
        MatOption,
        UserGroupFilterComponent,
        NgxMatSelectSearchModule,
        MatChipListbox,
        MatChipRow,
        MatIcon,
        MatChipRemove,
        MatInput,
        MatDatepickerInput,
        MatDatepickerToggle,
        MatSuffix,
        MatDatepicker,
        MatError,
        MatDialogActions,
        MatDialogClose,
        AsyncPipe,
        TranslatePipe,
        ButtonComponent,
        IconComponent
    ]
})
export class DutyScheduleAddEventComponent implements OnInit, OnDestroy {
    @ViewChild('btnDeleteEvent', {read: ElementRef}) btnDeleteEvent: ElementRef<HTMLElement>;
    @ViewChild('btnEnterCopyMode', {read: ElementRef}) btnEnterCopyMode: ElementRef<HTMLElement>;
    @ViewChild('btnSaveEvent', {read: ElementRef}) btnSaveEvent: ElementRef<HTMLElement>;
    @ViewChild('btnCopyEvent', {read: ElementRef}) btnCopyEvent: ElementRef<HTMLElement>;

    public readonly ButtonVariant: typeof ButtonVariant = ButtonVariant;

    public saveSelectValue: 'save' | 'continue' = 'save';
    public save$ = new Subject();
    public editMode: boolean;
    public copyMode: boolean = false;
    public loading: boolean = false;
    public subscription: Subscription = new Subscription();
    public form: UntypedFormGroup;
    public groupFilterId: number;
    public users: any[] = [];
    public usersFiltered: Observable<any[]>;
    public usersInEvent: any[] = [];
    public creatorName: string = '';
    public dutyHours: { hours: number, minutes: string }[] = [];
    public isDraggable: boolean = true;
    public searchCtrl: UntypedFormControl = new UntypedFormControl();
    public noEntriesFoundLabel: string;
    public searchLabel: string;

    constructor(
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialogRef: MatDialogRef<DutyScheduleAddEventComponent>,
        public dutyScheduleService: DutyScheduleService,
        private formBuilder: UntypedFormBuilder,
        private userInputStorageService: UserInputStorageService,
        private userStoreService: UserStoreService,
        private isMobileService: IsMobileService,
        private translate: TranslateService
    ) {
        this.form = this.formBuilder.group({
                'dateFormCtrl': ['', Validators.required],
                'hourStartCtrl': ['', Validators.required],
                'hourEndCtrl': ['', Validators.required],
                'termTypeCtrl': [''],
                'userCtrl': ['']
            },
            { validators: HoursRangeValidator });

        this.fillDutyHours();
    }

    private setupKeydownListeners(): void {
        this.subscription.add(
            this.dialogRef.keydownEvents().subscribe(event => {
                if (event.key.toLocaleLowerCase() === 'delete') {
                    this.btnDeleteEvent.nativeElement.click();
                }

                if (!event.ctrlKey || this.form.invalid) {
                    return;
                }

                event.stopImmediatePropagation();
                event.preventDefault();

                if (event.key.toLocaleLowerCase() === 's') {
                    if (this.btnSaveEvent) {
                        this.btnSaveEvent.nativeElement.click();
                    } else if (this.btnCopyEvent) {
                        this.btnCopyEvent.nativeElement.click();
                    }
                }

                if (event.key.toLocaleLowerCase() === 'd') {
                    this.btnEnterCopyMode.nativeElement.click();
                }
            })
        );
    }

    private fillDutyHours(): void {
        const hours = new Array(18).fill(0).map((item, index) => index + 6);

        hours.forEach(item => {
            this.dutyHours.push({ hours: item, minutes: '00' });
            this.dutyHours.push({ hours: item, minutes: '30' });
        });
    }

    get dateFormCtrl() {
        return this.form.get('dateFormCtrl');
    }

    get hourStartCtrl() {
        return this.form.get('hourStartCtrl');
    }

    get hourEndCtrl() {
        return this.form.get('hourEndCtrl');
    }

    get selectedUserCtrl() {
        return this.form.get('userCtrl');
    }

    get termTypeCtrl() {
        return this.form.get('termTypeCtrl');
    }

    ngOnInit() {
        this.isMobileService.isXS.subscribe(isXS => {
            this.isDraggable = isXS ? false : true;
        });

        this.noEntriesFoundLabel = this.translate.instant('DUTY-SCHEDULE-ADD.NO-EMPLOYEE-FOUND');
        this.searchLabel = this.translate.instant('ISSUE-LIST-TOOLBAR.SEARCH');

        this.getUsersForGroup();
        this.setupUserInputSubscription();

        if (this.data.type_id) {
            this.termTypeCtrl.setValue(this.data.type_id);
            this.editMode = true;
        } else {
            if (this.data.calendarTermId) {
                this.editMode = true;
            } else {
                this.editMode = false;
                this.loadSettings();
            }
        }

        if (this.data.selectedDay) {
            this.dateFormCtrl.setValue(this.data.selectedDay);
        }

        if (this.data.eventStart) {
            this.dateFormCtrl.setValue(this.data.eventStart);
            this.hourStartCtrl.setValue(this.dutyHours.find(item => item.hours === this.data.eventStart.getHours() && +item.minutes === this.data.eventStart.getMinutes()));
            this.hourEndCtrl.setValue(this.dutyHours.find(item => item.hours === this.data.eventEnd.getHours() && +item.minutes === this.data.eventEnd.getMinutes()));
        }

        if (this.data.userIds) {
            this.getUsersInEventData(this.data.userIds);
        }

        this.getCreatorsName(this.data.user_id);
        this.setupKeydownListeners();
    }

    ngOnDestroy() {
        this.subscription?.unsubscribe();
    }

    deleteEvent() {
        const eventData = {
            type: 'delete',
            calendarTermId: this.data.calendarTermId,
            calendarTermUserIds: this.data.calendarTermUserIds
        };

        this.dialogRef.close(eventData);
    }

    enterCopyMode() {
        this.loading = true;

        setTimeout(() => {
            this.copyMode = true;
            this.loading = false;
        }, 500);
    }

    private getTitle() {
        const result = [];

        for (const user of this.usersInEvent) {
            result.push(user.firstname + ' ' + user.lastname);
        }

        return result.join(', ');
    }

    copyEvent() {
        const momentDate = dayjs(this.dateFormCtrl.value),
            eventData = {
                type: 'add',
                title: this.getTitle(),
                start: momentDate.hour(this.hourStartCtrl.value.hours).minute(+this.hourStartCtrl.value.minutes).format(DB_DATE_FORMAT).toString(),
                end: momentDate.hour(this.hourEndCtrl.value.hours).minute(+this.hourEndCtrl.value.minutes).format(DB_DATE_FORMAT).toString(),
                userIds: this.usersInEvent.map(user => user.id),
                allDay: 0,
                calendarTermUserIds: this.data.calendarTermUserIds,
                type_id: +this.termTypeCtrl.value
            };

        this.dialogRef.close(eventData);
    }

    saveEvent(action: 'save' | 'continue') {
        const momentDate = dayjs(this.dateFormCtrl.value),
            eventData = {
                type: this.data.calendarTermId ? 'update' : 'add',
                title: this.getTitle(),
                start: momentDate.hour(this.hourStartCtrl.value.hours).minute(+this.hourStartCtrl.value.minutes).format(DB_DATE_FORMAT).toString(),
                end: momentDate.hour(this.hourEndCtrl.value.hours).minute(+this.hourEndCtrl.value.minutes).format(DB_DATE_FORMAT).toString(),
                userIds: this.usersInEvent.map(user => user.id),
                allDay: 0,
                calendarTermId: this.data.calendarTermId,
                calendarTermUserIds: this.data.calendarTermUserIds,
                type_id: +this.termTypeCtrl.value
            };

        if (action === 'continue') {
            this.save$.next(eventData);
            this.form.reset();
            this.usersInEvent = [];
            this.usersFiltered = of(this.usersNotAdded);
        } else {
            this.dialogRef.close(eventData);
        }
    }

    onGroupChange(groupId: number) {
        this.selectGroup(groupId);

        const groupIdStr = typeof groupId === 'undefined' ? '' : groupId.toString();

        this.userInputStorageService.setValue('calendar_selectedGroup', groupIdStr);
    }

    removeUser(userId: number) {
        this.usersInEvent = this.usersInEvent.filter(user => +user.id !== +userId);
        this.usersFiltered = of(this.usersNotAdded);
    }

    addUser(userId: number) {
        this.userStoreService.getUserFromStoreTake1(userId).subscribe(userData => {
            this.usersInEvent.push(userData.User);
            this.usersFiltered = of(this.usersNotAdded);
        });
    }

    displayFn(user?): string | undefined {
        return '';
    }

    onDateFromChange() {
        const dateTo = this.dutyHours.find(item => item.hours === this.hourStartCtrl.value.hours + 2 && +item.minutes === +this.hourStartCtrl.value.minutes);

        if (dateTo) {
            this.hourEndCtrl.setValue(dateTo);
        }
    }

    saveTypeSettings() {
        this.userInputStorageService.setValue('calendar_newType', this.termTypeCtrl.value);
    }

    toggleSelectValue() {
        if (this.editMode) {
            return;
        }

        this.saveSelectValue = this.saveSelectValue === 'save' ? 'continue' : 'save';
    }

    private getUsersInEventData(userIds: string[]) {
        this.usersInEvent = [];

        from(userIds)
            .pipe(concatMap(userId => this.userStoreService.getUserFromStoreTake1(+userId)))
            .subscribe(userData => {
                this.usersInEvent.push(userData.User);
                this.usersFiltered = of(this.usersNotAdded);
            });
    }

    private excludeAddedUsers(userId: number) {
        return !this.usersInEvent.find(user => +user.id === userId);
    }

    private get usersNotAdded() {
        return this.users.filter(user => this.excludeAddedUsers(user.id));
    }

    private getUsersForGroup(groupId?: number) {
        this.userStoreService.getUsersForGroupTake1(groupId)
            .pipe(
                map((result: any[]) => result.map(user => ({
                    id: +user.User.id,
                    firstname: user.User.firstname,
                    lastname: user.User.lastname,
                    banned: +user.User.banned === 1
                }))),
                map(users => users.filter(user => !user.banned)),
                map(users => users.filter(user => this.excludeAddedUsers(user.id)))
            )
            .subscribe(
                users => {
                    this.users = users;
                    this.usersFiltered = of(this.usersNotAdded);
                }
            );
    }

    private setupUserInputSubscription() {
        this.subscription.add(
            this.searchCtrl.valueChanges.pipe(
                startWith(''),
                map((inputText: string) => {
                    if (!inputText || typeof inputText !== 'string') {
                        return this.usersNotAdded;
                    }
                    inputText = inputText.toLowerCase();
                    return this.usersNotAdded.filter(user => isStringInFirstNameOrLastName(user.firstname, user.lastname, inputText));
                })
            ).subscribe(filteredUsers => this.usersFiltered = of(filteredUsers))
        );
    }

    private getCreatorsName(userId: number) {
        if (!userId) {
            return this.creatorName = '';
        }

        this.userStoreService.getUserFromStoreTake1(userId).subscribe(user => {
            this.creatorName = user.User.firstname + ' ' + user.User.lastname;
        });
    }

    private loadSettings() {
        const type = this.userInputStorageService.getValue('calendar_newType'),
            groupId = this.userInputStorageService.getValue('calendar_selectedGroup'),
            saveSelectValue: any = this.userInputStorageService.getValue('calendar_saveSelectValue');

        if (type) {
            this.termTypeCtrl.setValue(+type);
        }

        if (groupId) {
            this.selectGroup(+groupId);
        }

        if (saveSelectValue) {
            this.saveSelectValue = saveSelectValue;
        }
    }

    private selectGroup(groupId: number) {
        this.groupFilterId = groupId;
        this.selectedUserCtrl.setValue(null);
        this.getUsersForGroup(groupId);
    }
}
