@use '../variables' as *;

.filters {
  .mat-mdc-form-field {
    &.mat-mdc-form-field-type-mat-select,
    &.mat-mdc-form-field-type-mat-input {
      .mat-mdc-text-field-wrapper.mdc-text-field.mdc-text-field--outlined {
        height: 32px;

        .mat-mdc-form-field-flex {
          height: 32px;
          min-height: 32px;
        }

        .mdc-notched-outline__leading,
        .mdc-notched-outline__trailing,
        .mdc-notched-outline__notch {
          border-top-width: 1px;
          border-bottom-width: 1px;
        }

        .mat-mdc-form-field-infix {
          padding-top: 6px;
          padding-bottom: 6px;
          min-height: 32px;
        }
      }

      .mat-mdc-form-field-subscript-wrapper {
        height: 0;
        padding: 0;
      }

      .mat-mdc-select-value {
        padding-top: 0;
      }

      .mat-mdc-select-min-line {
        font-size: 12px;
      }

      .mat-mdc-form-field-icon-suffix {
        align-self: center;
        display: flex;
      }
    }
  }

  .mat-form-field-wrapper {
    font-size: 12px;
    border-radius: 7px;
    margin: 0;

    @media (min-width: 1280px) {
      width: 160px;
    }

    .mat-icon-button {
      color: $dark-blue;
    }
  }

  .mat-form-field-outline-start {
    border-radius: 7px 0 0 7px;
    min-width: 7px;
  }

  .mat-form-field-outline-end {
    border-radius: 0 7px 7px 0;
    min-width: 7px;
  }

  .mat-form-field-appearance-outline mat-label {
    padding-right: 23px;
  }

  .mat-select-value, .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
    position: relative;
    bottom: 3px;
  }
}

.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button {
  height: inherit;
}
