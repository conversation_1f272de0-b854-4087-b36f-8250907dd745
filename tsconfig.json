{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "types": ["jasmine", "jasminewd2", "node"], "esModuleInterop": true, "sourceMap": true, "declaration": false, "moduleResolution": "node", "experimentalDecorators": true, "skipLibCheck": true, "target": "ES2022", "typeRoots": ["node_modules/@types"], "lib": ["dom"], "useDefineForClassFields": false}, "files": ["src_team/polyfills.ts"], "include": ["src_team/**/*.spec.ts", "src_team/**/*.d.ts", "src_team/**/*.ts"], "angularCompilerOptions": {"enableIvy": true}}