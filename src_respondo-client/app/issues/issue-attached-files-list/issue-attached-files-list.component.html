<div class="file-list-panel">
    <mat-expansion-panel (opened)="expanded = true" (closed)="expanded = false">
        <mat-expansion-panel-header>
            <mat-panel-title>
                {{expanded ? ('ISSUES.HIDE-FILES' | translate) : ('ISSUES.SHOW-FILES' | translate)}} ({{commonFileObject.length}})
            </mat-panel-title>
        </mat-expansion-panel-header>

        <div class="files-list">
            <app-download-button
                *ngFor="let file of commonFileObject;"
                [fileName]="file.CommonFile.filename"
                [fileId]="file.CommonFile.id"
                [fileType]="file.CommonFile.type"
            ></app-download-button>
        </div>

        <app-issue-view-list-download-all-button [issueSentence]="issueSentence"></app-issue-view-list-download-all-button>
    </mat-expansion-panel>
</div>
