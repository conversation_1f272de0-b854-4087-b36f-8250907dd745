<mat-sidenav-container class="issue-view" [ngClass]="{'is-mobile': isHandset$ | async}">
    <div class="issue-mainblock-wrapper">
        <app-issue-view-menu
                [issueData]="issueData"
                [entryList]="entryList"
                (refreshIssue)="refreshAllIssueData()"
        >
        </app-issue-view-menu>
        <div class="issue-container"
             [ngClass.xs]="{
            'editor-expanded': editorVisible && editorExpanded,
            'is-mobile': true}"
             [ngClass]="{
            'editor-expanded': editorVisible && editorExpanded}">
            <section id="issue-title">
                <button mat-button color="primary" class="issue-id" ngxClipboard [cbContent]="issueData?.issue_internal_id + ''" matTooltip="{{'ISSUES.ISSUE-ID' | translate}}">
                    {{issueData?.issue_internal_id}}
                </button>

                <span class="mat-title">{{issueData?.subject}}</span>

                <div fxFlex></div>
            </section>

            <div class="issue-sentences"
                 [class.full]="!editorVisible"
                 (click)="collapseEditor()"
                 #issueSentences
            >
                <app-issue-view-list
                        [sentencesList]="issueSentencesListShown"
                        [issueId]="issueData?.id"
                        [issuePaymentRejectionId]="issueData?.issue_payment_rejection_id"
                        [customerId]="issueData?.customer_id"
                        (refreshList)="refreshAllIssueData()"
                        class="issue-view-list"
                >
                </app-issue-view-list>
            </div>
            <div class="issue-editor" *ngIf="editorVisible">
                <div
                        *ngIf="isHandset$ | async"
                        class="show-editor-mobile color-background color-foreground"
                        (click)="toggleMobileEditor()"
                >
                    <mat-icon *ngIf="!buttonExpand; else top">vertical_align_bottom</mat-icon>
                    <ng-template #top>
                        <mat-icon>vertical_align_top</mat-icon>
                    </ng-template>
                </div>
                <app-issue-editor
                        [disabled]="!editorEnabled"
                        [issueData]="issueData"
                        [lastSentence]="getLastIssueSentence()"
                        (questionSent)="getSentences$.next(null)"
                ></app-issue-editor>
            </div>
        </div>
    </div>
</mat-sidenav-container>
