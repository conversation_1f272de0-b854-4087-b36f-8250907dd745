import {Component, Input} from '@angular/core';
import { NgClass } from '@angular/common';
import { DefaultClassDirective } from 'ngx-flexible-layout/extended';
import { SafeHtmlPipe } from '../../../../src_team/app/shared/pipes/safe-html.pipe';

@Component({
    selector: 'app-issue-view-list-sentence-body',
    templateUrl: './issue-view-list-sentence-body.component.html',
    styleUrls: ['./issue-view-list-sentence-body.component.scss',
        '../../../../node_modules/quill/dist/quill.snow.css'
    ],
    imports: [NgClass, DefaultClassDirective, SafeHtmlPipe]
})
export class IssueViewListSentenceBodyComponent {
    @Input() body;
    @Input() addQuillStyling = false;
}
