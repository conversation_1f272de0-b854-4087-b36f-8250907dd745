<div class="payment-rejection">
    <h3>{{data.title}}</h3>
    <form novalidate #rejectionForm="ngForm" class="rejection-form">
        <mat-form-field class="description">
            <textarea
                matInput
                name="description"
                ngModel
                placeholder="{{'PAYMENT-REJECTION.REASON' | translate}}"
                tabindex="-1"
            ></textarea>
        </mat-form-field>
    </form>
    <div mat-dialog-actions fxLayout fxLayoutAlign="space-between">
        <button mat-button mat-dialog-close>{{'PAYMENT-REJECTION.CANCEL' | translate}}</button>
        <button mat-button [disabled]="rejectionForm.invalid" (click)="send(rejectionForm)">{{'PAYMENT-REJECTION.SEND' | translate}}</button>
    </div>
</div>
