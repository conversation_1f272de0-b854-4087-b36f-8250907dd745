import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {environment} from '../../../environments/environment';

@Injectable({
    providedIn: 'root'
})
export class WfirmaAuthService {
    private environment: any;

    constructor(
        private http: HttpClient
    ) {}

    wfirmaAuthorize(code: string) {
        return this.http.post(
            environment.wFirmaOauth2.authCodeEndpoint,
            {
                'WfirmaOauth2': {
                    'auth_code': code
                }
            }
        );
    }

    wFirmaCheckAuth() {
        return this.http.get(
            environment.wFirmaOauth2.authCodeEndpoint
        );
    }

    wFirmaLogOut(id: number) {
        return this.http.delete(
            environment.wFirmaOauth2.authCodeEndpoint + `/${id}`
        );
    }

    wFirmaCompanyAuthorize(id: string, companyId: string, companyName: string) {
        return this.http.put(
            environment.wFirmaOauth2.authCodeEndpoint + `/${id}`,
            {
                'WfirmaOauth2': {
                    'company_id': companyId,
                    'name': companyName
                }
            }
        );
    }
}
