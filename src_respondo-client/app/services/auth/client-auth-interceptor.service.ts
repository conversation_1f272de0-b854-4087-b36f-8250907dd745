import { HttpClient, HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import {Injectable, Injector} from '@angular/core';
import {catchError, first, switchMap} from 'rxjs/operators';
import {Observable, throwError, Subject, of, EMPTY} from 'rxjs';
import {Router} from '@angular/router';
import {AuthService} from './auth.service';

export const InterceptorSkipHeader = 'X-Skip-Interceptor';

@Injectable()
export class ClientAuthInterceptor implements HttpInterceptor {

    private refreshInProgress = false;
    private refreshSubject: Subject<boolean> = new Subject<boolean>();

    constructor(private authService: AuthService, private injector: Injector, private router: Router) {
    }

    public intercept(req: HttpRequest<any>, delegate: HttpHand<PERSON>): Observable<HttpEvent<any>> {
        const clone: HttpRequest<any> = req.clone();

        if (req.headers.has(InterceptorSkipHeader)) {
            const headers = req.headers.delete(InterceptorSkipHeader);

            return delegate.handle(req.clone({headers}));
        }

        if (this.authService.blockedReq.getValue()) {
            return EMPTY;
        }

        return this.request(clone)
            .pipe(
                switchMap((request: HttpRequest<any>) => delegate.handle(request)),
                catchError((res: HttpErrorResponse) => this.responseError(clone, res))
            );
    }

    private request(req: HttpRequest<any>): Observable<HttpRequest<any>> {
        if (this.refreshInProgress && !req.url.includes('token')) {
            return this.delayRequest(req);
        }

        return this.addToken(req);
    }

    private responseError(req: HttpRequest<any>, res: HttpErrorResponse): Observable<HttpEvent<any>> {
        if (!this.refreshInProgress && res.status === 401) {
            this.refreshInProgress = true;

            this.authService
                .refreshAccessToken()
                .subscribe(
                    data => {
                        if (data.access_token) {
                            this.authService.setAccessToken(data.access_token);
                        }

                        this.refreshInProgress = false;
                        this.refreshSubject.next(true);
                    },
                    () => {
                        if (this.refreshInProgress) {
                            this.refreshInProgress = false;
                            this.refreshSubject.next(false);
                            // this.authService.logout();

                            return EMPTY;
                        } else {
                            this.refreshInProgress = false;
                            this.refreshSubject.next(false);
                        }
                    }
                );
        }

        if (this.refreshInProgress && res.status === 401) {
            return this.retryRequest(req, res);
        }

        if (!res.status || (res.status === 401 && !this.refreshInProgress)) {
            // this.authService.logout();
        }

        return throwError(res);
    }

    private delayRequest(req: HttpRequest<any>): Observable<HttpRequest<any>> {
        return this.refreshSubject.pipe(
            first(),
            switchMap((status: boolean) => status ? this.addToken(req) : throwError(req))
        );
    }

    private retryRequest(req: HttpRequest<any>, res: HttpErrorResponse): Observable<HttpEvent<any>> {
        const http: HttpClient = this.injector.get<HttpClient>(HttpClient);

        return this.refreshSubject.pipe(
            first(),
            switchMap((status: boolean) => status ? http.request(req) : throwError(res || req))
        );
    }

    private addToken(req: HttpRequest<any>): Observable<HttpRequest<any>> {
        return of(req.clone({setHeaders: this.authService.headers}));
    }
}
