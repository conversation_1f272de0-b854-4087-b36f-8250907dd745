import { Injectable } from '@angular/core';
import {AuthService} from '../auth/auth.service';


@Injectable({
  providedIn: 'root'
})
export class UserInputStorageService {

  constructor(private authService: AuthService) { }

  setValue(key: string, value) {
    const storageKey = 'user_' + this.authService.getUserId() + '_' + key;

    localStorage.setItem(storageKey, value);
  }

  getValue(key: string) {
    const storageKey = 'user_' + this.authService.getUserId() + '_' + key;

    return localStorage.getItem(storageKey);
  }

  purgeMyLocalStorage() {
    const myId = +this.authService.getUserId(),
        myStorageKeys = Object.keys(localStorage).filter(key => key.includes(myId.toString()));

    myStorageKeys.forEach(key => localStorage.removeItem(key));
  }
}
