import {Injectable} from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {Observable} from 'rxjs';
import {map, pluck, shareReplay, switchMap} from 'rxjs/operators';
import {environment} from '../../../environments/environment';
import {SubscriptionTypeInterface} from '../../../../src_team/app/common/interfaces/subscription-type.interface';
import {DataResponseInterface} from '../../../../src_team/app/common/interfaces/data-response.interface';
import {UserSubscriptionInterface} from '../../../../src_team/app/common/interfaces/user-subscription.interface';
import {isClientSubscriptionCategoryActive} from '../../../../src_team/app/common/utils/subscriptions-utils';

@Injectable({
    providedIn: 'root'
})
export class ClientSubscriptionsService {
    subscriptionTypes$: Observable<SubscriptionTypeInterface[]> = this.http.get<DataResponseInterface>(environment.apiUrl + 'subscription_type?is_hidden=false&order=id|asc&limit=9999').pipe(
        pluck('results'),
        map(results => results.map(item => item.SubscriptionType)),
        map(subscriptionTypes => subscriptionTypes.map((subscriptionType, index) => ({...subscriptionType, css_class: 'type-' + index}))),
        shareReplay(1)
    );

    subscriptionTypesWithCategoriesIds$ = this.subscriptionTypes$.pipe(
        switchMap(subscriptionTypes => {
            const subscriptionTypesIds = subscriptionTypes.map(subscriptionType => +subscriptionType.id);

            return this.getCategoriesForSubscriptionTypes(subscriptionTypesIds).pipe(
                map((result: {subscription_type_id: string | number, category_id: string | number}[]) => {
                    return subscriptionTypes.map(subscriptionType => {
                        subscriptionType.issue_categories = result.filter(item => +item.subscription_type_id === +subscriptionType.id).map(item => +item.category_id);

                        return subscriptionType;
                    });
                })
            );
        })
    );

    constructor(private http: HttpClient) {
    }

    getCategoriesForSubscriptionTypes(subscriptionTypeIds: number[]) {
        return this.http.get<DataResponseInterface>(environment.apiUrl + 'subscription_type_category?subscription_type_id=' + subscriptionTypeIds.join(',') + '&limit=9999').pipe(
            pluck('results'),
            map(results => results.map(item => item.SubscriptionTypeCategory)),
        );
    }

    getUserSubscriptions(conditions: string): Observable<UserSubscriptionInterface[]> {
        const query = conditions ? '&' + conditions : '';

        return this.http.get<DataResponseInterface>(environment.apiUrl + 'user_subscription?limit=9999' + query).pipe(
            pluck('results'),
            map(subscriptions => subscriptions.map(subscription => subscription.UserSubscription))
        );
    }

    hasActiveSubscriptionInCategory(categoryId: number | string) {
        return this.getUserSubscriptions('').pipe(
            map(userSubscriptions => userSubscriptions.filter(userSubscription =>
                isClientSubscriptionCategoryActive(userSubscription.expiration_date, +userSubscription.issue_limit, +userSubscription.issue_counter))
            ),
            map(userSubscriptions => userSubscriptions.map(userSubscription => +userSubscription.subscription_type_id)),
            switchMap((userSubscriptionsTypIds) => {
                return this.getCategoriesForSubscriptionTypes(userSubscriptionsTypIds).pipe(
                    map(results => results.filter(result => result.category_id === categoryId)),
                    map(results => results.length > 0)
                );
            })
        );
    }
}
