import {Injectable} from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {environment} from '../../../environments/environment';
import {IssueInterface} from '../../../../src_team/app/common/interfaces/issue.interface';
import {IssueSentenceInterface} from '../../../../src_team/app/common/interfaces/issue-sentence.interface';
import {AuthService} from '../auth/auth.service';

@Injectable({
    providedIn: 'root'
})
export class ClientIssueService {
    private userId: number;

    constructor(private http: HttpClient, private authService: AuthService) {
        this.userId = this.authService.getUserId();
    }

    addIssue(data) {
        const Issue = {
            subject: data.subject,
            status: data.status || 'new',
            customer_id: data.customer_id,
            company_id: data.company_id
        };

        return this.http.post<IssueInterface>(environment.apiUrl + 'issue/', JSON.stringify({Issue}));
    }

    addIssueSentence(issueId: number, initiatorId: number, status: 'draft' | 'sent', body: string) {
        const data = {
            IssueSentence: {
                issue_id: issueId,
                speaker: 'initiator',
                speaker_id: initiatorId,
                status,
                body
            }
        };

        return this.http.post<IssueSentenceInterface>(environment.apiUrl + 'issue_sentence/', JSON.stringify(data));
    }
}
