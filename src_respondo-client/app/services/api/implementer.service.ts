import { Injectable } from '@angular/core';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {Implementer, ImplementerResponse} from '../../interfaces/implementation-spec.interface';
import { HttpClient, HttpParams } from '@angular/common/http';
import {environment} from '../../../environments/environment';

@Injectable({
    providedIn: 'root'
})
export class ImplementerService {
    endpointUrl = environment.apiUrl + 'implementer';

    constructor(private http: HttpClient) { }

    getImplementers(): Observable<Implementer[]> {
        const params = new HttpParams().set('limit', '9999');

        return this.http.get<ImplementerResponse>(`${this.endpointUrl}`, {params}).pipe(
            map(response => response.results.map(item => item.Implementer))
        );
    }

}
