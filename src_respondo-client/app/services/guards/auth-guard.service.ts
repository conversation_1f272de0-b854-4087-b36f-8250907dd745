import {Injectable} from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import {take} from 'rxjs/operators';
import {AuthService} from '../auth/auth.service';
import {ApplicationStateService} from '../../../../src_team/app/services/application-state.service';
import {EnvironmentService} from '../../../../src_team/app/services/environment.service';

@Injectable({
    providedIn: 'root'
})
export class AuthGuardService  {
    private environment: any;

    constructor(
        private authService: AuthService,
        private router: Router,
        private applicationStateService: ApplicationStateService,
        private environmentService: EnvironmentService
    ) {
        this.environment = this.environmentService.selectedEnvironment;
    }

    canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<boolean> {

        return new Promise(resolve =>
            this.authService.isLoggedIn.pipe(take(2)).subscribe(
                isLoggedIn => {
                    if (isLoggedIn) {
                        return resolve(true);
                    }

                    this.applicationStateService.setValue('urlBeforeLogout', state.url);

                    window.location.href =  this.environment.accountsUrl + '/externalLogout';

                    return resolve(false);
                }
            )
        );
    }

    canActivateChild(childRoute: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<boolean> {
        return this.canActivate(childRoute, state);
    }
}
