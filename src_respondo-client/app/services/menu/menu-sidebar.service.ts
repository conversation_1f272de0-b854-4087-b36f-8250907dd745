import { Injectable } from '@angular/core';
import {Route, Router, Routes} from '@angular/router';
import {BehaviorSubject, Observable} from 'rxjs';
import {DataRouting} from '../../interfaces/data-routing';

@Injectable({
  providedIn: 'root'
})
export class MenuSidebarService {
  private mainRoutesSubject: BehaviorSubject<{ path: string; data: DataRouting }[]> = new BehaviorSubject([]);
  mainRoutes$: Observable<{ path: string; data: DataRouting }[]> = this.mainRoutesSubject.asObservable();

  constructor(private router: Router) {
    this.getMainRoutes();
  }

  /**
   * Pobiera główne trasy z routera.
   */
  private getMainRoutes(): void {
    const routes = this.router.config
        .filter(route => {
          return route.data && route.data.mainMenuName;
        })
        .map(route => ({
          path: this.getMainChildRoute(route),
          data: route.data
        }))
        .sort((a, b) => (a.data.order) - (b.data.order));

    this.mainRoutesSubject.next(routes as { path: string; data: DataRouting }[]);
  }

  /**
   * Zwraca główną trasę dla danej trasy.
   * Jeśli podtrasa ma nazwę menu głównego, to zwraca trasę z podtrasa, w przeciwnym przypadku zwraca samą trasę.
   * @param {Route} parentRoute Trasa nadrzędna
   * @returns {string} Główna trasa
   */
  private getMainChildRoute(parentRoute: Route): string {
    if (parentRoute.children) {
      const childrenWithTopMenu = parentRoute.children.filter(child => child.data && child.data.topMenuName);
      if (childrenWithTopMenu.length > 0) {
        return `${parentRoute.path}/${childrenWithTopMenu[0].path}`;
      }
    }
    return parentRoute.path;
  }
}