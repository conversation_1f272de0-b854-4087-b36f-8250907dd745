<div class="implementers-list-card">
    <ng-container *ngIf="!isDataSourceEmpty; else emptyTemplate">
        <div class="table-wrapper">
            <table mat-table [dataSource]="dataSource" class="mat-elevation-z0">
                <ng-container matColumnDef="favourite">
                    <th mat-header-cell *matHeaderCellDef class="favourite-col">
                        {{ 'IMPLEMENTATION-SPEC.FAVOURITE' | translate }}
                    </th>
                    <td mat-cell *matCellDef="let element" class="favourite-col">
                        <a (click)="setFavouriteImplementer(element.id, $event)">
                            <mat-icon [id]="element.id"
                                      [svgIcon]="favouriteStates[element.id]?.iconName || 'empty-star-icon'"
                                      class="star-icon">
                            </mat-icon>
                        </a>
                    </td>
                </ng-container>

                <ng-container matColumnDef="name">
                    <th mat-header-cell *matHeaderCellDef>{{ 'IMPLEMENTATION-SPEC.NAME' | translate }}</th>
                    <td mat-cell *matCellDef="let element">{{ element.company_name }}</td>
                </ng-container>

                <ng-container matColumnDef="city">
                    <th mat-header-cell *matHeaderCellDef>{{ 'IMPLEMENTATION-SPEC.CITY' | translate }}</th>
                    <td mat-cell *matCellDef="let element">{{ element?.city }}</td>
                </ng-container>

                <ng-container matColumnDef="contact">
                    <th mat-header-cell *matHeaderCellDef class="contact-col">{{ 'IMPLEMENTATION-SPEC.CONTACT-DATA' | translate }}</th>
                    <td mat-cell *matCellDef="let element" class="contact-col">
                        <ng-container *ngIf="element?.email">
                            <mat-icon svgIcon="form-icon" matTooltip="{{element.email}}" class="overlay-icon"></mat-icon>
                        </ng-container>
                        <ng-container *ngIf="element?.phone">
                            <mat-icon svgIcon="phone-icon" matTooltip="{{element.phone}}"></mat-icon>
                        </ng-container>
                        {{ element.phone }}<br>
                    </td>
                </ng-container>

                <ng-container matColumnDef="action">
                    <th mat-header-cell *matHeaderCellDef class="contact-col"></th>
                    <td mat-cell *matCellDef="let element" class="contact-col">
                        <button mat-button color="primary" (click)="contactImplementer(element)">{{ 'IMPLEMENTATION-SPEC.CONTACT-ACTION' | translate }}</button>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
        </div>
    </ng-container>
    <ng-template #emptyTemplate>
        <div class="no-data-font no-data">
            {{ 'IMPLEMENTATION-SPEC.NO-DATA' | translate }}
        </div>
    </ng-template>
</div>

<div class="paginator-container mat-elevation-z2">
    <mat-paginator [length]="dataSource.data.length" [pageSize]="10" [pageSizeOptions]="[5, 10, 25]" showFirstLastButtons>
    </mat-paginator>
</div>

