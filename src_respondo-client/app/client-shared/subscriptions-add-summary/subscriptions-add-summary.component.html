<div class="subscriptions-add-summary" ngClass.xs="xs">
    <div [ngClass]="data.css_class" class="header">
        Pakiet {{data.issueLimit}} {{data.genitive_name}}
    </div>
    <div class="order-details">
        <div class="title">{{'SUBSCRIPTIONS-ADD-SUMMARY.SELECTED-PACKAGE' | translate}}</div>
        <div class="details" fxLayout.gt-xs="row" fxLayoutAlign.gt-xs="space-between" fxLayout.xs="column" fxLayoutAlign.xs="center" fxLayoutGap="10px">
            <div fxLayout="row" fxLayoutAlign="space-between" fxLayoutGap.gt-xs="22px">
                <div class="subscription-name">{{data.name}}</div>
                <div><strong>{{data.issueLimit}}</strong> {{'SUBSCRIPTIONS-ADD-SUMMARY.ISSUES' | translate}}<span *ngIf="data.issueLimit <= 3">y</span></div>
            </div>
            <div fxLayout="row" fxLayoutAlign="space-between" fxLayoutGap.gt-xs="22px">
                <div><strong>{{data.price | currencyFormat: ''}}</strong> PLN netto</div>
                <div>{{data.gross_price - data.price | currencyFormat: 'PLN'}} VAT</div>
            </div>
        </div>
    </div>
    <hr class="line-separator">
    <div class="contractor">
        <div fxLayout fxLayoutAlign="space-between center">
            <div class="title">
                {{'SUBSCRIPTIONS-ADD-SUMMARY.INVOICE-DATA' | translate}}
            </div>
            <button
                mat-stroked-button
                (click)="addInvoiceData()"
            >
                {{!!contractor ? ('SUBSCRIPTIONS-ADD-SUMMARY.CHANGE-DATA' | translate) : ('SUBSCRIPTIONS-ADD-SUMMARY.ADD-DATA' | translate)}}
            </button>
        </div>
        <div *ngIf="contractor">
            <div
                fxLayout="column"
                fxLayoutGap.xs="5px"
            >
                <div class="company-name font-bold">{{contractor.name}}</div>
                <div *ngIf="contractor.nip"><span class="font-bold">NIP:</span> {{contractor.nip}}</div>
                <div>{{contractor.street}}</div>
                <div>
                    <span>{{contractor.zip}}&nbsp;</span><span>{{contractor.city}}</span>
                </div>
            </div>
        </div>
    </div>
    <hr class="line-separator">
    <div class="summary" fxLayout="column" fxLayoutAlign="start center">
        <div class="title">{{'SUBSCRIPTIONS-ADD-SUMMARY.SUMMARY' | translate}}</div>
        <div class="content" fxLayout.gt-xs="row" fxLayoutAlign.gt-xs="space-around">
            <div class="font-bold">{{'SUBSCRIPTIONS-ADD-SUMMARY.TOTAL-PAID' | translate}}</div>
            <div class="no-wrap"><span class="font-bold">{{data.gross_price | currencyFormat: ''}}</span> PLN brutto</div>
        </div>
        <div fxLayout fxLayoutAlign="space-around" class="actions">
            <button
                mat-raised-button
                color="primary"
                [disabled]="!contractor || loadingStatus === loadingStatusValues.loading"
                (click)="pay()"
            >
                {{'SUBSCRIPTIONS-ADD-SUMMARY.PAY' | translate}}
            </button>
        </div>
    </div>
    <div *ngIf="loadingStatus === loadingStatusValues.loading" class="loading" fxLayoutAlign="center center">
        <mat-spinner></mat-spinner>
    </div>
</div>
