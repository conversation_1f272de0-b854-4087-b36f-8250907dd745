<mat-card
        *ngIf="loadingStatus !== loadingStatusValues.loading"
        class="subscriptions-add"
        ngClass.sm="sm"
        fxLayout.gt-md="row"
        fxLayout.lt-md="column"
        fxLayoutAlign.gt-md="space-around"
        fxLayoutGap.gt-md="20px"
        fxLayoutGap.xs="45px"
>
    <div class="subscription-about" ngClass.xs="xs" ngClass.sm="sm" ngClass.md="md">
        <h2 class="title">{{'SUBSCRIPTIONS-ADD.ABOUT.TITLE' | translate}}</h2>

        <p class="question">
            {{'SUBSCRIPTIONS-ADD.ABOUT.QUESTION-ONE' | translate}}
        </p>
        <ul class="categories-list">
            <li>{{'SUBSCRIPTIONS-ADD.ABOUT.LIST-ONE-ITEM-ONE' | translate}}</li>
            <li>{{'SUBSCRIPTIONS-ADD.ABOUT.LIST-ONE-ITEM-TWO' | translate}}</li>
            <li>{{'SUBSCRIPTIONS-ADD.ABOUT.LIST-ONE-ITEM-THREE' | translate}}</li>
            <li>{{'SUBSCRIPTIONS-ADD.ABOUT.LIST-ONE-ITEM-FOUR' | translate}}</li>
        </ul>
        <p class="answer">{{'SUBSCRIPTIONS-ADD.ABOUT.ANSWER-ONE' | translate}}</p>

        <p class="question">
            {{'SUBSCRIPTIONS-ADD.ABOUT.QUESTION-TWO' | translate}}
        </p>
        <ul class="categories-list">
            <li>{{'SUBSCRIPTIONS-ADD.ABOUT.LIST-TWO-ITEM-ONE' | translate}}</li>
            <li>{{'SUBSCRIPTIONS-ADD.ABOUT.LIST-TWO-ITEM-TWO' | translate}}</li>
            <li>{{'SUBSCRIPTIONS-ADD.ABOUT.LIST-TWO-ITEM-THREE' | translate}}</li>
            <li>{{'SUBSCRIPTIONS-ADD.ABOUT.LIST-TWO-ITEM-FOUR' | translate}}</li>
        </ul>
        <p class="answer">{{'SUBSCRIPTIONS-ADD.ABOUT.ANSWER-TWO' | translate}}</p>

    </div>

    <div
            fxLayout.gt-xs="row"
            fxLayout.xs="column"
            fxLayoutAlign.gt-xs="space-around"
            fxLayoutAlign.xs="stretch center"
            fxLayoutGap.xs="65px"
            fxLayoutGap.md="10px"
            fxLayoutGap.gt-md="100px"
            class="subscriptions-types"
    >
        <mat-card
                *ngFor="let subscriptionType of subscriptionTypes"
                [class]="subscriptionType.css_class"
                [ngClass]="{'subscription-type': true}"
        >
            <div class="price-box">
                <div class="total no-wrap">
                    {{+subscriptionType.unit_price | currencyFormat}} netto
                </div>
                <div>{{'SUBSCRIPTIONS-ADD.FOR-ONE-ADVICE' | translate}}</div>
            </div>
            <div class="details">
                <div class="name">{{subscriptionType.name}}</div>
                <div class="slider">
                    <span class="title">{{'SUBSCRIPTIONS-ADD.ISSUE-AMOUNT' | translate}}</span>
                    <ngx-slider [value]="12" [options]="sliderOptions" (userChange)="onChangeSlider($event, subscriptionType)"></ngx-slider>
                </div>
                <div class="description-button">
                    <span style="display: block;">{{'SUBSCRIPTIONS-ADD.PACKAGE-DESC' | translate}}</span>
                    <div class="description">
                        <div class="description-title">{{'SUBSCRIPTIONS-ADD.PACKAGE-TITLE' | translate}}</div>
                        <div><strong>{{subscriptionType.issue_limit}}</strong> {{'SUBSCRIPTIONS-ADD.ADVICES' | translate}}<span *ngIf="subscriptionType.issue_limit <= 3">y</span> {{'SUBSCRIPTIONS-ADD.FOR' | translate}} <strong>{{'SUBSCRIPTIONS-ADD.YEAR' | translate}}</strong>.</div>
                        <div>{{'SUBSCRIPTIONS-ADD.TEXTS-TEMPLATE' | translate}}</div>
                        <div *ngIf="subscriptionType.css_class === 'type-1'">
                            <h3>{{'SUBSCRIPTIONS-ADD.LAW-ADVICE' | translate}}</h3>
                            <ul>
                                <li>{{'SUBSCRIPTIONS-ADD.LAW-ADVICE-ITEM-ONE' | translate}}</li>
                                <li>{{'SUBSCRIPTIONS-ADD.LAW-ADVICE-ITEM-TWO' | translate}}</li>
                                <li>{{'SUBSCRIPTIONS-ADD.LAW-ADVICE-ITEM-THREE' | translate}}</li>
                                <li>{{'SUBSCRIPTIONS-ADD.LAW-ADVICE-ITEM-FOUR' | translate}}</li>
                                <li>{{'SUBSCRIPTIONS-ADD.LAW-ADVICE-ITEM-FIVE' | translate}}</li>
                            </ul>
                        </div>

                        <div class="font-bold no-wrap description-price">{{'SUBSCRIPTIONS-ADD.PACKAGE-PRICE-FOR-YEAR' | translate}} {{getTotalPrice(subscriptionType) | currencyFormat}} netto</div>
                    </div>
                </div>
                <div class="subscription-categories">
                    <ul class="categories-list">
                        <ng-container *ngFor="let category of subscriptionType.issue_categories | orderBy: 'sequence'">
                            <li>{{category.name}}</li>
                        </ng-container>
                    </ul>
                </div>
            </div>
            <div>
                <button
                        class="pay"
                        mat-raised-button
                        color="primary"
                        (click)="proceedToCheckout(subscriptionType)"
                >
                    {{'SUBSCRIPTIONS-ADD.CHOOSE' | translate}}
                </button>
            </div>
        </mat-card>
    </div>
</mat-card>
