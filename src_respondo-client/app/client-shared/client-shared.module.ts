import {NgModule} from '@angular/core';
import {IssueManagerComponent} from './issue-manager/issue-manager.component';
import {InvoiceDataComponent} from './invoice-data/invoice-data.component';
import {ContractorsComponent} from './contrators/contractors.component';
import {SubscriptionsListComponent} from './subscriptions-list/subscriptions-list.component';
import {SubscriptionsListElementComponent} from './subscriptions-list-element/subscriptions-list-element.component';
import {SubscriptionsAddComponent} from './subscriptions-add/subscriptions-add.component';
import {SubscriptionsAddSummaryComponent} from './subscriptions-add-summary/subscriptions-add-summary.component';
import {OrderModule} from 'ngx-order-pipe';
import {AvatarModule} from 'ngx-avatars';
import {EmployeeAvatarDisplayComponent} from './employee-avatar-display/employee-avatar-display.component';
import {TranslateModule} from '@ngx-translate/core';
import {SvgIconComponent} from './svg-icon/svg-icon.component';
import {MaskitoDirective} from '@maskito/angular';
import {NgxSliderModule} from '@angular-slider/ngx-slider';

@NgModule({
    imports: [
        OrderModule,
        NgxSliderModule,
        AvatarModule,
        TranslateModule,
        MaskitoDirective,
        IssueManagerComponent,
        InvoiceDataComponent,
        ContractorsComponent,
        SubscriptionsListComponent,
        SubscriptionsListComponent,
        SubscriptionsListElementComponent,
        SubscriptionsAddComponent,
        SubscriptionsAddSummaryComponent,
        EmployeeAvatarDisplayComponent,
        SvgIconComponent
    ],
    exports: [
        IssueManagerComponent,
        InvoiceDataComponent,
        ContractorsComponent,
        SubscriptionsListComponent,
        SubscriptionsAddComponent,
        AvatarModule,
        EmployeeAvatarDisplayComponent
    ]
})
export class ClientSharedModule {
}
