<div class="subscriptions-list" ngClass.xs="xs" ngClass.sm="sm" ngClass.md="md">
    <ng-container [ngSwitch]="loadingStatus">
        <ng-container *ngSwitchCase="loadingStatusValues.loaded" [ngTemplateOutlet]="dataTemplate"></ng-container>
        <ng-container *ngSwitchCase="loadingStatusValues.nodata" [ngTemplateOutlet]="noDataTemplate"></ng-container>
    </ng-container>
</div>

<ng-template #dataTemplate>
    <mat-card class="subscriptions-container">
        <div
            class="header"
            fxLayout.gt-xs="row"
            fxLayout.xs="column"
            fxLayoutAlign="start center"
            fxLayoutGap.xs="10px"
        >
            <div class="title">{{'SUBSCRIPTIONS-LIST.PURCHASED-PACKAGES' | translate}}</div>
            <button class="buy-button" mat-raised-button color="primary" (click)="buySubscription.emit()">{{'SUBSCRIPTIONS-LIST.BUY-NEW-PACKAGE' | translate}}</button>
        </div>

        <div class="container" fxLayout.gt-sm="row wrap" fxLayoutAlign.lt-lg="center" fxLayout="column" fxLayoutGap="20px">
            <ng-container *ngFor="let subscription of subscriptions" >
                <div *ngIf="subscription.active" fxFlex.gt-lg="1 1 25%" fxFlex.gt-md="1 1 31%" fxFlex="1 1 100%" fxFlex.md="1 1 50%">
                    <app-subscriptions-list-element [subscription]="subscription"></app-subscriptions-list-element>
                </div>
            </ng-container>
        </div>
    </mat-card>
</ng-template>

<ng-template #noDataTemplate>
    <app-subscriptions-add [showCancelButton]="false"></app-subscriptions-add>
</ng-template>
