<div class="issue-manager">
    <h2 mat-dialog-title>{{data.firstname + ' ' + data.lastname}}</h2>
    <div
        fxLayout.gt-xs="row"
        fxLayout.xs="column"
        fxLayoutGap="10px"
    >
        <app-employee-avatar-display class="user-avatar" [userId]="data.id"></app-employee-avatar-display>
        <div>{{data.description ? data.description : 'ISSUE-MANAGER.NO-DESCRIPTION' | translate}}</div>
    </div>
    <div mat-dialog-actions>
        <div fxFlex></div>
        <button mat-button mat-dialog-close>{{'ISSUE-MANAGER.CLOSE' | translate}}</button>
    </div>
</div>

