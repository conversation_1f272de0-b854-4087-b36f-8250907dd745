<div fxLayout="column" fxLayoutAlign="stretch center" class="restore-password">
  <h3>{{'RESTORE-PASSWORD.RESETTING-PASSWORD' | translate}}</h3>

  <div *ngIf="resetPasswordSent; else restorePasswordForm">
      <p class="message">{{'RESTORE-PASSWORD.MESSAGE-SEND-ON-EMAIL' | translate}}</p>
      <p class="footer">{{'RESTORE-PASSWORD.GO-TO' | translate}} <a href="#" routerLink="/">{{'RESTORE-PASSWORD.HOME-PAGE' | translate}}</a></p>
  </div>

  <ng-template #restorePasswordForm>
      <mat-form-field appearance="outline" class="reset-area">
          <mat-label>{{'RESTORE-PASSWORD.YOUR-ADDRESS-EMAIL' | translate}}</mat-label>
          <input
              matInput
              [formControl]="passwordFormCtrl"
              name="reset-email"
              type="email"
              autocomplete="off"
              required
          >
          <mat-error *ngIf="passwordFormCtrl.invalid && (passwordFormCtrl.dirty || passwordFormCtrl.touched)">
              <span *ngIf="passwordFormCtrl.errors.email">{{'RESTORE-PASSWORD.INCORRECT-EMAIL-FORMAT' | translate}}</span>
              <span *ngIf="passwordFormCtrl.errors.email404">{{'RESTORE-PASSWORD.EMAIL-NOT-EXISTS' | translate}}</span>
              <span *ngIf="passwordFormCtrl.errors.unknown">{{apiResetPasswordError.message}}</span>
          </mat-error>
      </mat-form-field>
      <button
          mat-stroked-button
          class="button-rounded primary submit-button"
          color="primary"
          (click)="restorePassword()"
          [disabled]="passwordFormCtrl.invalid"
      >
          {{'RESTORE-PASSWORD.RESET' | translate}}
      </button>
  </ng-template>
</div>
