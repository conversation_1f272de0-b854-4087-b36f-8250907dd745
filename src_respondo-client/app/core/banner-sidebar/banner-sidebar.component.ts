import {Component, Input} from '@angular/core';
import {environment} from '../../../environments/environment';
import { MatNavList } from '@angular/material/list';
import { NgFor, NgIf } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { MatButton } from '@angular/material/button';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-banner-sidebar',
    templateUrl: './banner-sidebar.component.html',
    styleUrls: ['./banner-sidebar.component.scss'],
    standalone: true,
    imports: [MatNavList, NgFor, MatIcon, MatButton, NgIf, TranslatePipe]
})
export class BannerSidebarComponent {

  appUrl = environment.appUrl;
  accountsUrl = `${environment.accountsUrl}/respondo/switch/expert`;

  @Input() expertAccount: number | null;

  cards = [
    {
      title: 'Obsługuj klientów',
      icon: 'case-management-shortcut',
      url: `${this.appUrl}/serve-customers`
    },
    {
      title: 'Prowadź czat',
      icon: 'chat-shortcut',
      url: `${this.appUrl}/customer-chat`
    },
    {
      title: 'Badaj rynek',
      icon: 'research-market-shortcut',
      url: `${this.appUrl}/badaj-rynek`
    },
    {
      title: 'Generuj leady',
      icon: 'generate-leads-shortcut',
      url: `${this.appUrl}/zdobywaj-leady`
    }
  ];

  constructor() {}

}
