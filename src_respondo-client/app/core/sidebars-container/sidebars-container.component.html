<mat-sidenav-container
        [ngClass]="{
        'is-mobile': isMobile,
        'is-xs': isMobileService.isXS | async}">
    <!--[hasBackdrop]="isMobile"-->

    <mat-sidenav
        #menuSidebar
        class="menu-sidebar mat-elevation-z8"
        fixedInViewport="false"
        [opened]="openedMenuSidebar"
        [mode]="menuSidebarMode"
        [ngClass]="expandMenuSidebar ? 'is-expanded' : ''"
        (keydown.escape)="onMenuSidebarEscape($event)"
        disableClose
    >
        <app-menu-sidebar
                [isExpanded]="expandMenuSidebar"
                [showPaymentTab]="numberOfPayments"
                [showArchiveTab]="numberOfClosedIssues">
        </app-menu-sidebar>
    </mat-sidenav>

    <mat-sidenav-content [ngClass]="{'is-expanded-left': openedMenuSidebar && expandMenuSidebar && menuDrawerPush, 'is-expanded-right': bannerDrawerPush}">
        <app-header [expertAccount]="expertAccount"></app-header>
        <router-outlet></router-outlet>
        <footer></footer>
    </mat-sidenav-content>
<!--    <mat-sidenav-->
<!--        #bannerSidebar-->
<!--        [ngClass]="{'is-expanded-right': bannerDrawerPush}"-->
<!--        class="is-expanded-banner mat-elevation-z5"-->
<!--        fixedInViewport="false"-->
<!--        [opened]="true"-->
<!--        [mode]="bannerSidebarMode"-->
<!--        position="end"-->
<!--        disableClose-->
<!--    >-->
<!--        <app-banner-sidebar [expertAccount]="expertAccount"></app-banner-sidebar>-->
<!--    </mat-sidenav>-->

</mat-sidenav-container>
