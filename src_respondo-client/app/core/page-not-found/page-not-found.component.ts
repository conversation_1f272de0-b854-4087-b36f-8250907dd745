import {Component} from '@angular/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {ActivatedRoute, Router} from '@angular/router';
import {TranslateService} from '@ngx-translate/core';
import {AuthService} from '../../services/auth/auth.service';
import { take } from 'rxjs/operators';

@Component({
    selector: 'app-page-not-found',
    standalone: true,
    template: ''
})
export class PageNotFoundComponent {
    constructor(matSnackBar: MatSnackBar, authService: AuthService, router: Router, private route: ActivatedRoute, private translate: TranslateService) {
        const message = this.translate.instant(this.route.snapshot.data.message);

        authService.isLoggedIn.pipe(take(1)).subscribe(isLoggedIn => {
            if (isLoggedIn) {
                matSnackBar.open(message, '',  {panelClass: ['error-snackbar']});
                router.navigateByUrl('/issue/list/my');
            } else {
                router.navigateByUrl('/login');
            }
        });
    }
}
