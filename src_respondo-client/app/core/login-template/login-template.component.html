<div class="login-template" fxLayout.xs="column" fxLayoutAlign.xs="space-between stretch">
    <ng-container *ngIf="formType === 'password'">
        <div class="welcome-panel" fxHide.lt-lg fxFlex.gt-md="40%" fxLayout="column">
            <div class="text">
                <h2>
                    Jeszcze chwila i aktywujemy <br> Twoją sprawę za pomocą<br>
                    <span class="primary-text">bezpłatnego konta klienta 5 ways…!</span>
                </h2>

                <p class="first-paragraph">
                    <strong>Panel klienta 5 ways…</strong> to miej<PERSON><PERSON>, które umożliwia kontakt z ekspertem i dostęp do wszelkich szczegółów związanych z Twoją sprawą.
                </p>

                <p class="second-paragraph">
                    Za pomocą darmowych narzędzi b<PERSON><PERSON><PERSON><PERSON> mógł:
                </p>

                <ul class="list">
                    <li>z<PERSON><PERSON><PERSON> dodatkowe pytania ekspertowi,</li>
                    <li>negocjować wysłaną przez eksperta cenę za produkt/usługę/poradę,</li>
                    <li>opłacać sprawę,</li>
                    <li>dokonywać ewentualnych reklamacji.</li>
                </ul>
            </div>
            <div class="image-footer">
                <img class="image" src="/assets/images/5ways-login-image.svg" alt="5ways"/>
                <div class="line"></div>
            </div>
        </div>
    </ng-container>

    <div class="login-panel" fxFlex="auto" fxLayout="column" fxLayoutAlign.gt-xs="space-between stretch">
        <div fxFlex="auto" fxLayout="column" fxLayoutAlign="center">
            <img class="logo" src="/assets/images/5ways-logo.svg" alt="logo" />
            <ng-container [ngSwitch]="formType">
                <app-login *ngSwitchCase="'login'"></app-login>
                <app-user-password *ngSwitchCase="'password'"></app-user-password>
                <app-restore-password *ngSwitchCase="'restore'"></app-restore-password>
                <app-user-remove *ngSwitchCase="'remove'"></app-user-remove>
            </ng-container>
        </div>
        <div class="copyright" fxLayout="column" fxLayoutAlign="center center">
            <div>© Copyright 2022 - {{currentDate | date:'yyyy'}}</div>
            <div>5 WAYS CORP</div>
        </div>
    </div>
</div>
