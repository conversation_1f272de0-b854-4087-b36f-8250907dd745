import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {environment} from '../../../environments/environment';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective, DefaultFlexDirective } from 'ngx-flexible-layout/flex';
import { NgIf, NgSwitch, NgSwitchCase, DatePipe } from '@angular/common';
import { DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { LoginComponent } from '../login/login.component';
import { UserPasswordComponent } from '../password/user-password.component';
import { RestorePasswordComponent } from '../restore-password/restore-password.component';
import { UserRemoveComponent } from '../user-remove/user-remove.component';

@Component({
    selector: 'app-login-template',
    templateUrl: './login-template.component.html',
    styleUrls: ['./login-template.component.scss'],
    standalone: true,
    imports: [DefaultLayoutDirective, DefaultLayoutAlignDirective, NgIf, DefaultShowHideDirective, DefaultFlexDirective, NgSwitch, NgSwitchCase, LoginComponent, UserPasswordComponent, RestorePasswordComponent, UserRemoveComponent, DatePipe]
})
export class LoginTemplateComponent implements OnInit {
    formType: 'login' | 'password' | 'restore';
    currentDate: Date;
    appVersion = '';
    environment = environment;

    constructor(private route: ActivatedRoute) {
    }

    ngOnInit() {
        this.formType = this.route.snapshot.data.formType;
        this.currentDate = new Date();
        this.appVersion =  environment.appVersion;
    }

    isFirstVisit() {
        return !localStorage.getItem('isLoggedInBefore');
    }
}
