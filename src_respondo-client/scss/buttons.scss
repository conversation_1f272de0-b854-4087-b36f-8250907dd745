@use "../styles/variables" as *;

.button-rounded {
    &.mat-button,
    &.mat-raised-button,
    &.mat-flat-button,
    &.mat-stroked-button {
        border-radius: 30px;
        border: 1px solid #1270B6;
        padding: 0 30px;

        &[disabled] {
            border-color: #00000042;
            background: none !important;
            cursor: default;
        }
    }
}

.button-rounded-green {
    &.mat-button,
    &.mat-raised-button,
    &.mat-flat-button,
    &.mat-stroked-button {
        border-radius: 30px;
        border: 1px solid #1270B6;
        padding: 0 30px;

        &.green {
            width: 150px;
            border: none;
            background: transparent linear-gradient(90deg, #309a00, #3cc300) 0 0 no-repeat padding-box;
            color: #fff;
            padding: 3px 20px 3px 14px;
            font-size: 15px;
            font-weight: 700;

            .mat-icon {
                vertical-align: sub;
                font-size: 20px;
                width: 20px;
                height: 20px;
            }

            &.cdk-focused {
                background: linear-gradient(90deg, #309a00, #3cc300) !important;
            }

            &.cdk-program-focused {
                background: linear-gradient(90deg, #309a00, #3cc300) !important;
            }
        }

        &[disabled] {
            border-color: #00000042;
            background: none !important;
            cursor: default;
        }
    }
}

.button-rounded-white {
    &.mat-button,
    &.mat-raised-button,
    &.mat-flat-button,
    &.mat-stroked-button {
        border-radius: 36px;
        border: none;
        background-color: white;
        padding: 0px 18px;
        font-weight: 700;
        font-size: 14px;
        position: absolute;
        right: 10px;
        bottom: 12px;

        &:hover {
            background-color: white;
        }
    }

    &:hover {
        background-color: white;
    }
}

//.button-rounded {
//    border-radius: 24px;
//    padding: 7px 37px;
//
//    &.primary:not([disabled]) {
//        background: #fff;
//        color: #1270B6;
//        font-size: 15px;
//        border-color: #1270B6;
//
//        &:hover {
//            background: $hover;
//            cursor: pointer;
//        }
//    }
//}

.button-icon {
    align-items: center;
    background: #fff;
    border-radius: 7px;
    border: none;
    color: #1270B6;
    display: flex;
    font-size: 15px;
    height: 30px;
    justify-content: center;
    line-height: 29px;
    margin: 0;
    padding: 0;
    width: 30px;

    &:hover {
        background: $hover;
        cursor: pointer;
    }
}

.transparent-button {
    color: #596269 !important;
    font-size: 14px !important;
    margin-left: 0 !important;
    padding-left: 4px !important;
    padding-right: 12px !important;
    font-weight: 400 !important;

    .mat-button-wrapper {
        color: #596269 !important;
    }

    .mat-icon {
        font-size: 16px !important;
        vertical-align: sub !important;
    }

    &:hover {
        background-color: $hover !important;
    }

    &.blue {
        .mat-icon {
            color: $blue !important;
        }
    }

    &.warn {
        .mat-icon {
            color: #BC0100 !important;
        }

        &:hover {
            background-color: #FFF2F2 !important;
        }
    }
}

.mat-button, .mat-icon-button, .mat-stroked-button {
    &:hover {
        background-color: $hover !important;
    }
}

.mat-button.mat-primary, .mat-icon-button.mat-primary, .mat-stroked-button.mat-primary {
    color: #005FAA;
}

.mat-form-field-appearance-legacy .mat-form-field-underline {
    background-color: #005FAA;
}

.mat-form-field.mat-focused .mat-form-field-ripple {
    background-color: #005FAA;
}
