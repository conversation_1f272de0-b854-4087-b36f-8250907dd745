import { enableProdMode } from '@angular/core';
import './app/ag-grid-modules';
import {environment} from './environments/environment';
import { HttpClient } from '@angular/common/http';
import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import {registerLocaleData} from '@angular/common';
import {PdfMakeWrapper} from 'pdfmake-wrapper';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import localePl from '@angular/common/locales/pl';
import { appConfig } from './app/app.config';

if (environment.production) {
    enableProdMode();
}

bootstrapApplication(AppComponent, appConfig)
    .catch(err => console.error(err));

registerLocaleData(localePl);

PdfMakeWrapper.setFonts(pdfFonts);

export function httpTranslateLoaderFactory(http: HttpClient): TranslateHttpLoader {
    if (environment.production) {
        return new TranslateHttpLoader(http,  '/assets/i18n/');
    } else {
        return new TranslateHttpLoader(http, 'assets/i18n/');
    }
}
