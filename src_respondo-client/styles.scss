@use '@angular/material' as mat;
@use "styles/variables" as *;
@use '@covalent/core/theming/all-theme' as cov;
@use 'scss/buttons' as buttons;
@import 'quill/dist/quill.snow.css';
@import 'dragula/dist/dragula.css';
@import "ag-grid-community/styles/ag-grid.css";
@import "ag-grid-community/styles/ag-theme-material.css";

$app-primary: mat.m2-define-palette(mat.$m2-indigo-palette);
$app-accent: mat.m2-define-palette(mat.$m2-pink-palette, A200, A100, A400);
$app-warn: mat.m2-define-palette(mat.$m2-red-palette);

$covalent-theme: mat.m2-define-light-theme((
        color: (
                primary: $app-primary,
                accent: $app-accent,
                warn: $app-warn,
        ),
        typography: mat.m2-define-typography-config(
            $font-family: "Manrope, sans-serif"
        ),
        density: 0
));

$app-theme: mat.m2-define-light-theme((
        color: (
                primary: $app-primary,
                accent:  $app-accent,
                warn:    $app-warn,
        ),
        typography: mat.m2-define-typography-config(),
        density:    0,
));

@include mat.all-component-themes($app-theme);

@include cov.covalent-theme($covalent-theme);

:root {
    --background-color: #fff;
    --background-highlight-color: #f5f5f5;
    --primary-color: #1270B6;
    --yellow-color: #fffdcc;
}

@import url('https://fonts.googleapis.com/css2?family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap');


.warn-color {
    color: var(--warn-color);
}
.warn-hint{
    color: var(--warn-color) !important;
}

.mat-focused .mat-form-field-required-marker {
    color: #ff4081 !important;
}

* {
    box-sizing: border-box;
}

html {
    height: 100%;
}

body {
    height: 100%;
    margin: 0 !important;
    padding: 0 !important;
}

.flex {
    flex: 1 1 auto;
}

mat-sidenav-container {
    height: 100%;
}

.mat-tab-label {
    min-width: 30px !important;
}

.cursor-pointer {
    cursor: pointer;
}

.full-width {
    width: 100%;
}

.no-max-width {
    max-width: none !important;
}

.no-wrap {
    white-space: nowrap;
}

.no-data-font {
    font-size: 30px;
    font-weight: bold;
    letter-spacing: 2px;
    opacity: .2;
}

.panel-info {
    font-size: 30px;
    font-weight: bold;
    height: 100%;
    letter-spacing: 2px;
    margin-top: 0;
    opacity: .2;
}

.success-snackbar {
    background: $brand-success;
    color: #fff ;
}

.error-snackbar {
    background: $brand-danger;
    color: #fff ;
}

.full-width-dialog {
    max-width: 100vw !important;
    overflow-y: auto;
}

.full-width {
    width: 100%;
}

.full-height {
    height: 100%;
}

.font-weight-500 {
    font-weight: 500;
}

.font-bold {
    font-weight: bold;
}

.ql-tooltip {
    &::before {
        content: 'Link:' !important;
        color: #000 !important;
    }

    a.ql-remove::before {
        content: '\2715' !important;
        color: #000 !important;
    }

    a.ql-action::after {
        content: '\2699' !important;
        color: #000 !important;
    }

    &[data-mode=link]::before {
        content: 'Link:' !important;
        color: #000 !important;
    }

    &.ql-editing {
        a.ql-action::after {
            content: '\2399' !important;
            font-size: 21px !important;
            color: #000 !important;
        }
    }

    .ql-preview {
        color: #000 !important;
    }
}

.ql-toolbar.ql-snow {
    border-width: 0 0 1px 0 !important;
    border-color: $grey;
}

.ql-container {
    border: none !important;
}

.message-box {
    text-align: center;
}

.mat-mdc-tooltip {
    height: auto !important;
    white-space: pre-line !important;
    color: white !important;
}

.mat-button {
    color: #596269;
}

.mat-menu-panel {
    .mat-menu-content {
        margin: 12px;
        padding: 0;

        .mat-menu-item {
            border-radius: 8px;

            &:hover {
                background-color: $hover;
                border-radius: 8px;
            }
        }
    }

    .mat-icon {
        color: $blue;
    }
}

.mat-expansion-indicator::after {
    color: $blue;
    border-width: 0 3px 3px 0 !important;
}

.mat-slide-toggle-bar {
    .mat-slide-toggle-thumb {
        background-color: #005FAA;
    }

    background-color: #E2E8EC;
}

.mat-nav-list {
    .mat-list-item:hover {
        background-color: $hover;
    }

    .mat-list-item.active {
        background-color: $hover;
    }
}

.mat-select-panel-wrap {
    .mat-option:focus:not(.mat-option-disabled), .mat-option:hover:not(.mat-option-disabled) {
        background: $hover;
    }

    .mat-option.mat-active {
        background: $hover;
    }
}

.mat-select-panel .mat-option.mat-selected:not(.mat-option-multiple) {
    background: $hover;
}

.mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
    color: $dark-grey;
}

// mat-form start

.mat-form-field-appearance-outline {
    .mat-form-field-outline-start {
        border-radius: $mat-form-field-border-radius 0 0 $mat-form-field-border-radius !important;
        min-width: $mat-form-field-border-radius !important;
        border-width: 1px !important;
    }

    .mat-form-field-outline-end {
        border-radius: 0 $mat-form-field-border-radius $mat-form-field-border-radius 0 !important;
        border-width: 1px !important;
    }

    .mat-form-field-outline-gap {
        border-width: 1px !important;
    }

    mat-label {
        color: #333132;
        padding-left: $mat-form-field-border-radius + 4px;
        padding-right: 0;
        box-sizing: border-box;
    }

    &.mat-form-field-hide-placeholder {
        mat-label {
            padding-left: 8px;
            padding-right: 0;
            box-sizing: border-box;
        }
    }

    &.mat-form-field-disabled {
        mat-label {
            color: $grey;
        }
    }

    .mat-form-field-infix {
        padding: 1em 0 1em 0;
    }

    .mat-form-field-outline {
        color: $dark-blue;
        opacity: 0.5;
    }

    &.mat-focused {
        .mat-form-field-outline {
            color: $dark-blue;
            opacity: 0.5;
        }
    }

    .mat-form-field-outline.mat-form-field-outline-thick {
        color: $dark-blue;
        opacity: 0.5;
    }

    .mat-select-arrow-wrapper .mat-select-arrow {
        border-width: 0 3px 3px 0;
        display: inline-block;
        padding: 3px;
        transform: rotate(45deg);
        vertical-align: baseline;
    }
}

.mat-form-field.mat-form-field-appearance-fill {
    .mat-form-field-underline {
        display: none;
    }

    .mat-form-field-infix {
        border: 0;
    }

    .mat-form-field-flex {
        background-color: transparent !important;
        border-radius: 8px !important;
        padding-left: 15px;

        &:hover {
            background-color: $hover !important;
        }
    }

    .mat-form-field-wrapper {
        padding-bottom: 0 !important;
    }

    .mat-select-arrow-wrapper .mat-select-arrow {
        border-width: 0 3px 3px 0;
        display: inline-block;
        padding: 3px;
        transform: rotate(45deg);
        vertical-align: sub;
    }
}

// mat-form stop

.mat-paginator {
    .mat-paginator-page-size {
        align-items: center !important;
    }

    .mat-select-arrow-wrapper {
        transform: none !important;
    }

    .mat-form-field-wrapper {
        margin: 0 !important;
    }

    .mat-form-field-outline {
        display: none !important;
    }

    .mat-select-arrow {
        color: $dark-blue;
    }

    .mat-paginator-range-actions .mat-icon-button {
        &:hover {
          background-color: transparent !important;
        }

        &:not([disabled]) {
            color: $dark-blue;

            &:hover {
              color: $blue !important;
            }
        }
    }
}

// mat-checkbox start

mat-checkbox {
    .mat-checkbox-frame {
        border-color: $dark-blue;
        border-radius: 2px;
        border-width: 0.5px;
    }
}

// mat-checkbox stop

// mat-card start

.mat-mdc-card {
    --mdc-elevated-card-container-shape: 0;
    --mdc-elevated-card-container-elevation: 0;
    width: 100%;
}

mat-card.panel {
    border-radius: $container-border-radius !important;
    background-color: #fff !important;

    .mat-card-title {
        padding: 5px 10px 10px 10px;
        font-weight: 500;
        font-size: 16px;
        border-bottom: 1px solid $dark-blue;
        margin-bottom: 20px;
    }
}

// mat-card stop

/*
DRAGULA

in-flight clone
*/
.gu-mirror {
    margin: 0 !important;
    opacity: 0.8;
    pointer-events: none;
    position: fixed !important;
    z-index: 9999 !important;
}

/* high-performance display:none; helper */
.gu-hide {
    left: -9999px !important;
}

/* added to mirrorContainer (default = body) while dragging */
.gu-unselectable {
    user-select: none !important;
}

/* added to the source element while its mirror is dragged */
.gu-transit {
    opacity: 0.2;
}

input[type=text] {
    &::-ms-clear, &::-ms-reveal {
        display: none;
        height: 0;
        width: 0;
    }
}

input[type="search"] {
    &::-webkit-search-decoration,
    &::-webkit-search-cancel-button,
    &::-webkit-search-results-button,
    &::-webkit-search-results-decoration {
        display: none;
    }
}

.sticky-tabs {
    .mat-tab-header {
        position: sticky;
        top:0;
        z-index: 100;
    }

    &--dark {
        .mat-tab-header {
            background: #323131;
        }

        .config-block {
            background: #2c2c2c;
        }
    }

    &--light {
        .mat-tab-header {
            background: #fff;
        }

        .config-block {
            background: #fff;
        }
    }
}

.mat-expansion-panel-body {
    padding: 0 !important;
}
.mat-slide-toggle-bar {
    background-color: #E2E8EC;
}
.mat-slide-toggle-thumb {
    background-color: #005FAA;
}
.mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-thumb {
    background-color: #005FAA;
}

.mat-slider-horizontal .mat-slider-ticks {
    background-color: #005FAA;
}

.mat-dialog-container {
    border-radius: 12px !important;
    box-shadow: none !important;

    h1, h2 {
        border-bottom: 2px solid #005FAA;
        width: 100%;
        padding-bottom: 10px;
    }
}

// scrollbar start

/* Works on Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: #E2E8ED #F8F8F8;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
    width: 15px;
}

*::-webkit-scrollbar-track {
    background: #F8F8F8;
    border-radius: 20px;
}

*::-webkit-scrollbar-thumb {
    background-color: #E2E8ED;
    border-radius: 20px;
    border: 3px solid #F8F8F8;
}

// scrollbar stop

@media screen and (max-height: 700px) {
    .max-height-700 {
        height: 100vh;
    }
}

@media screen and (max-height: 400px) {
    .max-height-400 {
        height: 100vh;
    }
}

@media screen and (max-height: 765px) {
    .max-height-765 {
        height: 100vh;
    }
}
