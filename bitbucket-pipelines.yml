image: node:22

pipelines:
  default:
    - step:
        name: Unit Tests (respondo-team)
        caches:
          - node
        script:
          - apt-get update
          - apt-get install -y chromium chromium-driver fonts-liberation libappindicator3-1 libasound2 libatk-bridge2.0-0 libatk1.0-0 libcups2 libdbus-1-3 libgdk-pixbuf2.0-0 libnspr4 libnss3 libx11-xcb1 libxcomposite1 libxdamage1 libxrandr2 xdg-utils

          - export CHROME_BIN=/usr/bin/chromium

          - npm install --force

          - cp src_respondo-client/environments/environment.default.ts src_respondo-client/environments/environment.ts
          - cp src_respondo-client/environments/environment.default.ts src_respondo-client/environments/environment.prod.ts

          - cp src_team/environments/environment.default.ts src_team/environments/environment.ts
          - cp src_team/environments/environment.default.ts src_team/environments/environment.prod.ts

          - npm run build:team
          - npm run build:client

          - npm run ut
